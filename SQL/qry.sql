saas 360

ALTER TABLE `user_subscription` ADD COLUMN `cbsub_id` VARCHAR(50) DEFAULT 'NA' NOT NULL  ;
	
ALTER TABLE `all_chargebee_subscription`  ADD COLUMN `event_type` VARCHAR(100) DEFAULT 'NA' NOT NULL 

ALTER TABLE `chargebee_subscription_history`  ADD COLUMN `event_type` VARCHAR(100) DEFAULT 'NA' NOT NULL 

ALTER TABLE `invoicehistory_new_v2`   ADD COLUMN `plan_amt` DOUBLE DEFAULT 0 NOT NULL ;
	
To update cbsub_id in user_subscription 

UPDATE user_subscription u JOIN all_chargebee_subscription a ON a.chargebee_id = u.chargebeeid SET u.cbsub_id=a.subscription_id 

executed in prod :

ALTER TABLE `invoicehistory_new_v2` ADD COLUMN `credits_applied` DOUBLE DEFAULT 0 NOT NULL, ADD COLUMN `issued_amt` DOUBLE DEFAULT 0 NOT NULL , ADD COLUMN `refund_amt` DOUBLE DEFAULT 0 NOT NULL ;

ALTER TABLE `invoicehistory_new_v2` ADD COLUMN `plan_period` VARCHAR(100) DEFAULT 'NA' NOT NULL ;
