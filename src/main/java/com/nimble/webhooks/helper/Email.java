package com.nimble.webhooks.helper;

import java.util.Properties;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.InternetHeaders;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.nimble.webhooks.util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
public class Email {

	@Value("${from_email}")
	private String from_email;

	@Autowired
	SecretManagerService secretManagerService;

	@Value("${aws_ses_secret_name}")
	private String SES_SECRET_NAME;

	private static final Logger log = LogManager.getLogger(Email.class);

	public boolean SendEmail_SES(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg) {

		String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
		String EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
		String EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");
		
		//String login = "<EMAIL>";
		String login =from_email;// "<EMAIL>";
		Transport transport = null;

		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
			props.setProperty("mail.smtp.port", "587");
           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
			
			// Create a Session object to represent a mail session with the
			// specified properties.
			Session session = Session.getDefaultInstance(props);

			// Create a message with the specified information.
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(login));

			if (toAddr != null) {
				msg.setRecipients(Message.RecipientType.TO, toAddr);
			}
			
			if (ccAddr != null) {
				msg.setRecipients(Message.RecipientType.CC, ccAddr);
			}

			if (bccAddr != null) {
				msg.setRecipients(Message.RecipientType.BCC, bccAddr);
			}
			msg.setSubject(sub);
			// msg.setContent(mailmsg,"text/plain");

			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");
			MimeBodyPart body = new MimeBodyPart(headers, mailmsg.getBytes("UTF-8"));
			Multipart multipart = new MimeMultipart();
			multipart.addBodyPart(body);

			// Send the complete message parts
			msg.setContent(multipart);

			// Create a transport.
			transport = session.getTransport();
			log.info("SendEmail_SES : Attempting to send an email");

			// Connect to Amazon SES using the SMTP username and password you
			// specified above.
			transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

			// Send the email.

			transport.sendMessage(msg, msg.getAllRecipients());
			System.out.println("Email sent!");
			return true;
		} catch (AddressException ex) {
			System.out.println("Address Exception occured :" + ex.getMessage());
		} catch (MessagingException ex) {
			System.out.println("Message Exception occured :" + ex.getMessage());
		} catch (Exception ex) {
			System.out.println("SendEmail Exception: " + ex.getMessage());
		}

		finally {
			try {
				if (null != transport)
					transport.close();
			} catch (Exception e) {
				System.out.println("SendEmail Exception: " + e.getMessage());
			}
		}
		return false;
	}
	
	public boolean SendEmail_SES_V2(String toAddr, String ccAddr, String bccAddr, String sub, String mailmsg) {

		String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
		String EMAIL_HOST_USER = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_access_key");
		String EMAIL_HOST_PASSWORD = secretManagerService.getSecretValue(SES_SECRET_NAME, "aws_secret_key");

		//String login = "<EMAIL>";
		String login =from_email;//  "<EMAIL>";
		Transport transport = null;
		
		String ccAdressSplit[] = ccAddr.split(",");
		String bccAdressSplit[] = bccAddr.split(",");

		try {
			Properties props = new Properties();
			props.put("mail.transport.protocol", "smtps");
			props.put("mail.smtp.auth", "true");
			props.setProperty("mail.smtp.port", "587");
	           
			props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");

			// Create a Session object to represent a mail session with the
			// specified properties.
			Session session = Session.getDefaultInstance(props);

			// Create a message with the specified information.
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(login));
			
			InternetAddress[] ccAddress = new InternetAddress[ccAdressSplit.length];
			for (int i = 0; i < ccAdressSplit.length; i++) {
				ccAddress[i] = new InternetAddress(ccAdressSplit[i]);
			}
			InternetAddress[] bccAddress = new InternetAddress[bccAdressSplit.length];
			for (int i = 0; i < bccAdressSplit.length; i++) {
				bccAddress[i] = new InternetAddress(bccAdressSplit[i]);
			}

			if (toAddr != null) {
				msg.setRecipients(Message.RecipientType.TO, toAddr);
			}
			
			for (int i = 0; i < ccAddress.length; i++) {
				msg.addRecipient(Message.RecipientType.CC, ccAddress[i]);
			}
			
			for (int i = 0; i < bccAddress.length; i++) {
				msg.addRecipient(Message.RecipientType.BCC, bccAddress[i]);
			}
			
			msg.setSubject(sub);
			// msg.setContent(mailmsg,"text/plain");

			InternetHeaders headers = new InternetHeaders();
			headers.addHeader("Content-type", "text/html; charset=UTF-8");
			MimeBodyPart body = new MimeBodyPart(headers, mailmsg.getBytes("UTF-8"));
			Multipart multipart = new MimeMultipart();
			multipart.addBodyPart(body);

			// Send the complete message parts
			msg.setContent(multipart);

			// Create a transport.
			transport = session.getTransport();
			log.info("Attempting to send an email through the Amazon SES SMTP interface...");

			// Connect to Amazon SES using the SMTP username and password you
			// specified above.
			transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);

			// Send the email.

			transport.sendMessage(msg, msg.getAllRecipients());
			System.out.println("Email sent!");
			return true;
		} catch (AddressException ex) {
			System.out.println("Address Exception occured :" + ex.getMessage());
		} catch (MessagingException ex) {
			System.out.println("Message Exception occured :" + ex.getMessage());
		} catch (Exception ex) {
			System.out.println("SendEmail Exception: " + ex.getMessage());
		}

		finally {
			try {
				if (null != transport)
					transport.close();
			} catch (Exception e) {
				System.out.println("SendEmail Exception: " + e.getMessage());
			}
		}
		return false;
	}
}
