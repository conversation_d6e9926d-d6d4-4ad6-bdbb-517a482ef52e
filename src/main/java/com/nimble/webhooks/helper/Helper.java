package com.nimble.webhooks.helper;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;
import com.squareup.okhttp.Response;

@Component
public class Helper {

	private static final Logger log = LogManager.getLogger(Helper.class);

	@Value("${iris.slackurl}")
	private String slackurl;

	@Value("${Shopify.DNS}")
	private String shopify_DNS;

	@Value("${Shopify.Username}")
	private String shopify_Username;

	@Value("${Shopify.password}")
	private String shopify_password;
	
	@Value("${Recharge.access-token}")
	private String access_token;

	public String getCurrentTimeinUTC() {

		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		df.setTimeZone(TimeZone.getTimeZone("UTC"));
		//System.out.println(df.format(d));
		return df.format(d);
	}
	
	public String getRechargeResp(String webServiceURL,String access_token) throws Exception {

		HttpURLConnection request = null;
		BufferedReader in = null;
		try {

			// Setup the Request
			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Accept", "application/json");
			request.setRequestProperty("X-Recharge-Access-Token",access_token );
			request.setDoOutput(true);

			// Get Response
			int responseCode = request.getResponseCode();
			// System.out.println(responseCode);
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} catch (Exception e) {
			// This exception will be raised if the serve didn't return 200 - OK
			throw e;
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}

	public void sendtoSlack(String title, String message) {

		String URL = slackurl;

		try {
			JSONObject fieldObj = new JSONObject();
			fieldObj.put("title", "WAGGLE WEBHOOK : " + title);
			fieldObj.put("value", message);
			fieldObj.put("short", false);

			JSONArray fieldArr = new JSONArray();
			fieldArr.put(fieldObj);
			JSONObject attachmentObj = new JSONObject();
			attachmentObj.put("color", "#9733EE");

			attachmentObj.put("fields", fieldArr);

			JSONArray attachmentArr = new JSONArray();
			attachmentArr.put(attachmentObj);
			JSONObject postParams = new JSONObject();
			postParams.put("attachments", attachmentArr);
			System.out.println(httpPOSTRequest(URL, postParams.toString()));
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public String httpPOSTRequest(String url, String urlParams) {
//		log.info("Entered into httpPOSTRequest : "+url);
		String response = null;
		try {
			URL url_ = new URL(url);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			http.setRequestMethod("POST");
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);
			http.setRequestProperty("Content-Type", "application/json");
			http.setRequestProperty("charset", "utf-8");

			http.setUseCaches(false);
			if (urlParams != null) {
				byte[] postData = urlParams.getBytes(StandardCharsets.UTF_8);
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write(postData);
			}
			int responseCode = http.getResponseCode();
			InputStream content = (InputStream) http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader(content);
			BufferedReader bufRdr = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			return sb.toString();
		} catch (Exception e) {
			log.error("Exception occured at httpPOSTRequest : " + e.getLocalizedMessage());
		}
		return response;
	}

	public String ShopifygetURL(String webServiceURL) throws Exception {

		HttpURLConnection request = null;
		BufferedReader in = null;
		try {

			// Setup the Request

			webServiceURL = "https://" + shopify_DNS + "/admin/api/2021-01/" + webServiceURL;

			String userpass = shopify_Username + ":" + shopify_password;
			String basicAuth = "Basic " + new String(Base64.getEncoder().encode(userpass.getBytes()));

			URL url = new URL(webServiceURL);
			request = (HttpURLConnection) url.openConnection();
			request.setRequestMethod("GET");
			request.setRequestProperty("Content-Type", "application/json");
			request.setRequestProperty("Authorization", basicAuth);
			request.setDoOutput(true);

			// Get Response
			int responseCode = request.getResponseCode();
			System.out.println(responseCode);
			StringBuilder response = new StringBuilder();
			response.append("");
			String inputLine;
			if (responseCode >= 400) {
				in = new BufferedReader(new InputStreamReader(request.getErrorStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
				while ((inputLine = in.readLine()) != null) {
					response.append(inputLine);
					response.append("\n");
				}
				in.close();
			}
			request.disconnect();
			return response.toString();
		} catch (IOException e) {
			return "Error: While Create Customer Request : " + e.getLocalizedMessage();
		} catch (Exception e) {
			return "Error: While Create Customer Request : " + e.getLocalizedMessage();
		} finally {
			if (in != null) {
				in.close();
			}
			if (request != null) {
				request.disconnect();
			}
		}
	}

	public String ShopifyCreateOrUpdateRequest(String webServiceURL, String urlParams, int requestMethod) {
		log.info("Entered into httpPOSTRequest : " + webServiceURL);
		String response = null;
		try {
			webServiceURL = "https://" + shopify_DNS + "/admin/api/2021-01/" + webServiceURL;

			String userpass = shopify_Username + ":" + shopify_password;
			String basicAuth = "Basic " + new String(Base64.getEncoder().encode(userpass.getBytes()));

			URL url_ = new URL(webServiceURL);
			HttpURLConnection http = (HttpURLConnection) url_.openConnection();
			
			if (requestMethod == 1)
				http.setRequestMethod("POST");
			else if (requestMethod == 2)
				http.setRequestMethod("PUT");
			
			http.setDoOutput(true);
			http.setInstanceFollowRedirects(false);

			http.setRequestProperty("Authorization", basicAuth);
//
			http.setRequestProperty("Content-Type", "application/json");
			http.setRequestProperty("charset", "utf-8");

			http.setUseCaches(false);
			if (urlParams != null) {
				byte[] postData = urlParams.getBytes(StandardCharsets.UTF_8);
				http.setRequestProperty("Content-Length", Integer.toString(postData.length));
				DataOutputStream wr = new DataOutputStream(http.getOutputStream());
				wr.write(postData);
			}
			int responseCode = http.getResponseCode();
			log.info("Response Code : " + responseCode);

			if (responseCode == 422)
				return "Error:422 Already Customer Info Exists : ";

			InputStream content = (InputStream) http.getInputStream();
			InputStreamReader inpStrmRdr = new InputStreamReader(content);
			BufferedReader bufRdr = new BufferedReader(inpStrmRdr);
			String line;
			StringBuilder sb = new StringBuilder();
			while ((line = bufRdr.readLine()) != null) {
				sb.append(line);
			}
			log.info("Http POST Response : " + sb.toString());
			return sb.toString();
		} catch (Exception e) {
			System.out.println("Exception occured at httpPOSTRequest : " + e.getLocalizedMessage());
			return "Error: While Create Customer Request : " + e.getLocalizedMessage();
		}
	}

	public String httpPOSTRequestV2(String url, String basic_auth, String json_body_data) {
		log.info("Entered into httpPOSTRequestV2 :: URL : "+ url + " :: basic auth : " + basic_auth+ " :: json_body_data : "+ json_body_data);
		try {
			
			OkHttpClient client = new OkHttpClient();
			
			MediaType mediaType = MediaType.parse("application/json");
			RequestBody body = RequestBody.create(mediaType, json_body_data);
			Request request = new Request.Builder()
			  .url(url)
			  .method("POST", body)
			  .addHeader("Content-Type", "application/json")
			  .addHeader("Authorization", "Basic "+basic_auth)
			  .build();
			Response response = client.newCall(request).execute();
			String responseBodyString = response.body().string();
			log.info("response : "+ responseBodyString);
			return responseBodyString;
			
		} catch (Exception e) {
			log.error("Error in httpPOSTRequestV2 :: Error : "+ e.getLocalizedMessage());
		}
		return "NA";
	}
	
	public String httpPOSTRequestV3(String url, String basic_auth, String json_body_data) {
		log.info("Entered into httpPOSTRequestV3 :: URL : "+ url + " :: basic auth : " + basic_auth+ " :: json_body_data : "+ json_body_data);
		try {
			
			OkHttpClient client = new OkHttpClient();
			
			MediaType mediaType = MediaType.parse("application/json");
			RequestBody body = RequestBody.create(mediaType, json_body_data);
			Request request = new Request.Builder()
			  .url(url)
			  .method("POST", body)
			  .addHeader("Content-Type", "application/json")
			  .addHeader("Authorization", "App "+basic_auth)
			  .build();
			Response response = client.newCall(request).execute();
			String responseBodyString = response.body().string();
			log.info("response : "+ responseBodyString);
			return responseBodyString;
			
		} catch (Exception e) {
			log.error("Error in httpPOSTRequestV3 :: Error : "+ e.getLocalizedMessage());
		}
		return "NA";
	}

}
