package com.nimble.webhooks.pojo;

import java.util.ArrayList;
import java.util.Date;

public class Order {

	public int id;
	public int address_id;
	public BillingAddress billing_address;
	public Charge charge;
	public ClientDetails client_details;
	public Date created_at;
	public String currency;
	public Customer customer;
	public ArrayList<Object> discounts;
	public Object error;
	public Object external_cart_token;
	public ExternalOrderId external_order_id;
	public ExternalOrderName external_order_name;
	public ExternalOrderNumber external_order_number;
	public boolean is_prepaid;
	public ArrayList<LineItem> line_items;
	public Object note;
	public ArrayList<Object> order_attributes;
	public Date processed_at;
	public Date scheduled_at;
	public ShippingAddress shipping_address;
	public ArrayList<ShippingLine> shipping_lines;
	public String status;
	public String subtotal_price;
	public Object tags;
	public ArrayList<Object> tax_lines;
	public boolean taxable;
	public String total_discounts;
	public Object total_duties;
	public String total_line_items_price;
	public String total_price;
	public String total_refunds;
	public String total_tax;
	public int total_weight_grams;
	public String type;
	public Date updated_at;

	public class BillingAddress {
		public String address1;
		public Object address2;
		public String city;
		public Object company;
		public String country_code;
		public String first_name;
		public String last_name;
		public String phone;
		public String province;
		public String zip;
	}

	public class Charge {
		public int id;
		public ExternalTransactionId external_transaction_id;
	}

	public class ClientDetails {
		public String browser_ip;
		public String user_agent;
	}

	public class Customer {
		public int id;
		public String email;
		public ExternalCustomerId external_customer_id;
		public String hash;
	}

	public class ExternalCustomerId {
		public String ecommerce;
	}

	public class ExternalOrderId {
		public String ecommerce;
	}

	public class ExternalOrderName {
		public String ecommerce;
	}

	public class ExternalOrderNumber {
		public String ecommerce;
	}

	public class ExternalProductId {
		public String ecommerce;
	}

	public class ExternalTransactionId {
		public String payment_processor;
	}

	public class ExternalVariantId {
		public String ecommerce;
	}

	public class Images {
		public String large;
		public String medium;
		public String original;
		public String small;
	}

	public class LineItem {
		public int purchase_item_id;
		public String external_inventory_policy;
		public ExternalProductId external_product_id;
		public ExternalVariantId external_variant_id;
		public Object grams;
		public Object handle;
		public Images images;
		public ArrayList<Object> properties;
		public String purchase_item_type;
		public int quantity;
		public String sku;
		public String tax_due;
		public ArrayList<Object> tax_lines;
		public boolean taxable;
		public String taxable_amount;
		public String title;
		public String total_price;
		public String unit_price;
		public boolean unit_price_includes_tax;
		public String variant_title;
	}

	public class ShippingAddress {
		public String address1;
		public Object address2;
		public String city;
		public Object company;
		public String country_code;
		public String first_name;
		public String last_name;
		public String phone;
		public String province;
		public String zip;
	}

	public class ShippingLine {
		public String code;
		public String price;
		public String source;
		public ArrayList<TaxLine> tax_lines;
		public boolean taxable;
		public String title;
	}

	public class TaxLine {
		public String price;
		public String rate;
		public String title;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getAddress_id() {
		return address_id;
	}

	public void setAddress_id(int address_id) {
		this.address_id = address_id;
	}

	public BillingAddress getBilling_address() {
		return billing_address;
	}

	public void setBilling_address(BillingAddress billing_address) {
		this.billing_address = billing_address;
	}

	public Charge getCharge() {
		return charge;
	}

	public void setCharge(Charge charge) {
		this.charge = charge;
	}

	public ClientDetails getClient_details() {
		return client_details;
	}

	public void setClient_details(ClientDetails client_details) {
		this.client_details = client_details;
	}

	public Date getCreated_at() {
		return created_at;
	}

	public void setCreated_at(Date created_at) {
		this.created_at = created_at;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Customer getCustomer() {
		return customer;
	}

	public void setCustomer(Customer customer) {
		this.customer = customer;
	}

	public ArrayList<Object> getDiscounts() {
		return discounts;
	}

	public void setDiscounts(ArrayList<Object> discounts) {
		this.discounts = discounts;
	}

	public Object getError() {
		return error;
	}

	public void setError(Object error) {
		this.error = error;
	}

	public Object getExternal_cart_token() {
		return external_cart_token;
	}

	public void setExternal_cart_token(Object external_cart_token) {
		this.external_cart_token = external_cart_token;
	}

	public ExternalOrderId getExternal_order_id() {
		return external_order_id;
	}

	public void setExternal_order_id(ExternalOrderId external_order_id) {
		this.external_order_id = external_order_id;
	}

	public ExternalOrderName getExternal_order_name() {
		return external_order_name;
	}

	public void setExternal_order_name(ExternalOrderName external_order_name) {
		this.external_order_name = external_order_name;
	}

	public ExternalOrderNumber getExternal_order_number() {
		return external_order_number;
	}

	public void setExternal_order_number(ExternalOrderNumber external_order_number) {
		this.external_order_number = external_order_number;
	}

	public boolean isIs_prepaid() {
		return is_prepaid;
	}

	public void setIs_prepaid(boolean is_prepaid) {
		this.is_prepaid = is_prepaid;
	}

	public ArrayList<LineItem> getLine_items() {
		return line_items;
	}

	public void setLine_items(ArrayList<LineItem> line_items) {
		this.line_items = line_items;
	}

	public Object getNote() {
		return note;
	}

	public void setNote(Object note) {
		this.note = note;
	}

	public ArrayList<Object> getOrder_attributes() {
		return order_attributes;
	}

	public void setOrder_attributes(ArrayList<Object> order_attributes) {
		this.order_attributes = order_attributes;
	}

	public Date getProcessed_at() {
		return processed_at;
	}

	public void setProcessed_at(Date processed_at) {
		this.processed_at = processed_at;
	}

	public Date getScheduled_at() {
		return scheduled_at;
	}

	public void setScheduled_at(Date scheduled_at) {
		this.scheduled_at = scheduled_at;
	}

	public ShippingAddress getShipping_address() {
		return shipping_address;
	}

	public void setShipping_address(ShippingAddress shipping_address) {
		this.shipping_address = shipping_address;
	}

	public ArrayList<ShippingLine> getShipping_lines() {
		return shipping_lines;
	}

	public void setShipping_lines(ArrayList<ShippingLine> shipping_lines) {
		this.shipping_lines = shipping_lines;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSubtotal_price() {
		return subtotal_price;
	}

	public void setSubtotal_price(String subtotal_price) {
		this.subtotal_price = subtotal_price;
	}

	public Object getTags() {
		return tags;
	}

	public void setTags(Object tags) {
		this.tags = tags;
	}

	public ArrayList<Object> getTax_lines() {
		return tax_lines;
	}

	public void setTax_lines(ArrayList<Object> tax_lines) {
		this.tax_lines = tax_lines;
	}

	public boolean isTaxable() {
		return taxable;
	}

	public void setTaxable(boolean taxable) {
		this.taxable = taxable;
	}

	public String getTotal_discounts() {
		return total_discounts;
	}

	public void setTotal_discounts(String total_discounts) {
		this.total_discounts = total_discounts;
	}

	public Object getTotal_duties() {
		return total_duties;
	}

	public void setTotal_duties(Object total_duties) {
		this.total_duties = total_duties;
	}

	public String getTotal_line_items_price() {
		return total_line_items_price;
	}

	public void setTotal_line_items_price(String total_line_items_price) {
		this.total_line_items_price = total_line_items_price;
	}

	public String getTotal_price() {
		return total_price;
	}

	public void setTotal_price(String total_price) {
		this.total_price = total_price;
	}

	public String getTotal_refunds() {
		return total_refunds;
	}

	public void setTotal_refunds(String total_refunds) {
		this.total_refunds = total_refunds;
	}

	public String getTotal_tax() {
		return total_tax;
	}

	public void setTotal_tax(String total_tax) {
		this.total_tax = total_tax;
	}

	public int getTotal_weight_grams() {
		return total_weight_grams;
	}

	public void setTotal_weight_grams(int total_weight_grams) {
		this.total_weight_grams = total_weight_grams;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getUpdated_at() {
		return updated_at;
	}

	public void setUpdated_at(Date updated_at) {
		this.updated_at = updated_at;
	}

}
