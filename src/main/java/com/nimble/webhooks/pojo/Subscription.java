package com.nimble.webhooks.pojo;

import java.util.ArrayList;
import java.util.Date;

public class Subscription {
	public class AnalyticsData {
		public ArrayList<Object> utm_params;
	}

	public class ExternalProductId {
		public String ecommerce;
	}

	public class ExternalVariantId {
		public String ecommerce;
	}

	private int id;
	private int address_id;
	private int customer_id;
	private Object locked_pending_charge_id;
	private AnalyticsData analytics_data;
	private Object cancellation_reason;
	private Object cancellation_reason_comments;
	private Object cancelled_at;
	private Object charge_delay;
	private int charge_interval_frequency;
	private Date created_at;
	private Object cutoff_day_of_month_before_and_after;
	private Object cutoff_day_of_week_before_and_after;
	private Object expire_after_specific_number_of_charges;
	private ExternalProductId external_product_id;
	private ExternalVariantId external_variant_id;
	private Object first_charge_date;
	private boolean has_queued_charges;
	private boolean is_prepaid;
	private boolean is_skippable;
	private boolean is_swappable;
	private boolean max_retries_reached;
	private String next_charge_scheduled_at;
	private Object order_day_of_month;
	private Object order_day_of_week;
	private int order_interval_frequency;
	private String order_interval_unit;
	private String presentment_currency;
	private String price;
	private String product_title;
	private ArrayList<Object> properties;
	private int quantity;
	private String sku;
	private boolean sku_override;
	private String status;
	private String type;
	private Date updated_at;
	private String variant_title;
	private String email = "NA";

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getAddress_id() {
		return address_id;
	}

	public void setAddress_id(int address_id) {
		this.address_id = address_id;
	}

	public int getCustomer_id() {
		return customer_id;
	}

	public void setCustomer_id(int customer_id) {
		this.customer_id = customer_id;
	}

	public Object getLocked_pending_charge_id() {
		return locked_pending_charge_id;
	}

	public void setLocked_pending_charge_id(Object locked_pending_charge_id) {
		this.locked_pending_charge_id = locked_pending_charge_id;
	}

	public AnalyticsData getAnalytics_data() {
		return analytics_data;
	}

	public void setAnalytics_data(AnalyticsData analytics_data) {
		this.analytics_data = analytics_data;
	}

	public Object getCancellation_reason() {
		return cancellation_reason;
	}

	public void setCancellation_reason(Object cancellation_reason) {
		this.cancellation_reason = cancellation_reason;
	}

	public Object getCancellation_reason_comments() {
		return cancellation_reason_comments;
	}

	public void setCancellation_reason_comments(Object cancellation_reason_comments) {
		this.cancellation_reason_comments = cancellation_reason_comments;
	}

	public Object getCancelled_at() {
		return cancelled_at;
	}

	public void setCancelled_at(Object cancelled_at) {
		this.cancelled_at = cancelled_at;
	}

	public Object getCharge_delay() {
		return charge_delay;
	}

	public void setCharge_delay(Object charge_delay) {
		this.charge_delay = charge_delay;
	}

	public int getCharge_interval_frequency() {
		return charge_interval_frequency;
	}

	public void setCharge_interval_frequency(int charge_interval_frequency) {
		this.charge_interval_frequency = charge_interval_frequency;
	}

	public Date getCreated_at() {
		return created_at;
	}

	public void setCreated_at(Date created_at) {
		this.created_at = created_at;
	}

	public Object getCutoff_day_of_month_before_and_after() {
		return cutoff_day_of_month_before_and_after;
	}

	public void setCutoff_day_of_month_before_and_after(Object cutoff_day_of_month_before_and_after) {
		this.cutoff_day_of_month_before_and_after = cutoff_day_of_month_before_and_after;
	}

	public Object getCutoff_day_of_week_before_and_after() {
		return cutoff_day_of_week_before_and_after;
	}

	public void setCutoff_day_of_week_before_and_after(Object cutoff_day_of_week_before_and_after) {
		this.cutoff_day_of_week_before_and_after = cutoff_day_of_week_before_and_after;
	}

	public Object getExpire_after_specific_number_of_charges() {
		return expire_after_specific_number_of_charges;
	}

	public void setExpire_after_specific_number_of_charges(Object expire_after_specific_number_of_charges) {
		this.expire_after_specific_number_of_charges = expire_after_specific_number_of_charges;
	}

	public ExternalProductId getExternal_product_id() {
		return external_product_id;
	}

	public void setExternal_product_id(ExternalProductId external_product_id) {
		this.external_product_id = external_product_id;
	}

	public ExternalVariantId getExternal_variant_id() {
		return external_variant_id;
	}

	public void setExternal_variant_id(ExternalVariantId external_variant_id) {
		this.external_variant_id = external_variant_id;
	}

	public Object getFirst_charge_date() {
		return first_charge_date;
	}

	public void setFirst_charge_date(Object first_charge_date) {
		this.first_charge_date = first_charge_date;
	}

	public boolean isHas_queued_charges() {
		return has_queued_charges;
	}

	public void setHas_queued_charges(boolean has_queued_charges) {
		this.has_queued_charges = has_queued_charges;
	}

	public boolean isIs_prepaid() {
		return is_prepaid;
	}

	public void setIs_prepaid(boolean is_prepaid) {
		this.is_prepaid = is_prepaid;
	}

	public boolean isIs_skippable() {
		return is_skippable;
	}

	public void setIs_skippable(boolean is_skippable) {
		this.is_skippable = is_skippable;
	}

	public boolean isIs_swappable() {
		return is_swappable;
	}

	public void setIs_swappable(boolean is_swappable) {
		this.is_swappable = is_swappable;
	}

	public boolean isMax_retries_reached() {
		return max_retries_reached;
	}

	public void setMax_retries_reached(boolean max_retries_reached) {
		this.max_retries_reached = max_retries_reached;
	}

	public String getNext_charge_scheduled_at() {
		return next_charge_scheduled_at;
	}

	public void setNext_charge_scheduled_at(String next_charge_scheduled_at) {
		this.next_charge_scheduled_at = next_charge_scheduled_at;
	}

	public Object getOrder_day_of_month() {
		return order_day_of_month;
	}

	public void setOrder_day_of_month(Object order_day_of_month) {
		this.order_day_of_month = order_day_of_month;
	}

	public Object getOrder_day_of_week() {
		return order_day_of_week;
	}

	public void setOrder_day_of_week(Object order_day_of_week) {
		this.order_day_of_week = order_day_of_week;
	}

	public int getOrder_interval_frequency() {
		return order_interval_frequency;
	}

	public void setOrder_interval_frequency(int order_interval_frequency) {
		this.order_interval_frequency = order_interval_frequency;
	}

	public String getOrder_interval_unit() {
		return order_interval_unit;
	}

	public void setOrder_interval_unit(String order_interval_unit) {
		this.order_interval_unit = order_interval_unit;
	}

	public String getPresentment_currency() {
		return presentment_currency;
	}

	public void setPresentment_currency(String presentment_currency) {
		this.presentment_currency = presentment_currency;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getProduct_title() {
		return product_title;
	}

	public void setProduct_title(String product_title) {
		this.product_title = product_title;
	}

	public ArrayList<Object> getProperties() {
		return properties;
	}

	public void setProperties(ArrayList<Object> properties) {
		this.properties = properties;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public boolean isSku_override() {
		return sku_override;
	}

	public void setSku_override(boolean sku_override) {
		this.sku_override = sku_override;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getUpdated_at() {
		return updated_at;
	}

	public void setUpdated_at(Date updated_at) {
		this.updated_at = updated_at;
	}

	public String getVariant_title() {
		return variant_title;
	}

	public void setVariant_title(String variant_title) {
		this.variant_title = variant_title;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

}
