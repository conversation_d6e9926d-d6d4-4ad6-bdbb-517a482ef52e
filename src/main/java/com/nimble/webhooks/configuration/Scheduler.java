package com.nimble.webhooks.configuration;

import java.util.Calendar;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.IErrorService;

@Configuration
@EnableScheduling
public class Scheduler {

	private static final Logger log = LogManager.getLogger(Scheduler.class);

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Autowired
	IChargebeeWebhookService chargebeeWebhookService;

	@Autowired
	IAsyncService async;

	@Autowired
	IErrorService errorinfoService;

	@Value("${scheduler.date}")
	private Integer schedulerDate;

	@Value("${scheduler.cron}")
	private String schedulerCron;

//	@Scheduled(cron = "${scheduler.cron}") // * 0/1 * ? * *")
//	public void chargebeeEvent() {
//		System.out.println();
//
//		try {
//			Calendar calendar = Calendar.getInstance();
//			calendar.setTime(new Date());
//			calendar.add(Calendar.DAY_OF_YEAR, schedulerDate);
//
//			System.out.println(calendar.getTimeInMillis());
//
//			List<ChargebeeWebhooksStatus> cbStatusList = chargebeeWebhookService.getWebHookslist(calendar.getTime());
//
//			for (ChargebeeWebhooksStatus cbStatus : cbStatusList) {
//				try {
//
//					Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//					Result result = Event.retrieve(cbStatus.getEventId()).request();
//					System.out.println(result.toString());
//
//					if (cbStatus.getEventType().contains("subscription")) {
//						JSONObject js = new JSONObject(result.toString());
//						async.chargebeeEventWebhookProcess(js.getJSONObject("event"), cbStatus);
//					}
//
//				} catch (Exception e) {
//					log.info("Error While Updateing Retry Count : " + cbStatus.getEventId());
//				}
//			}
//		} catch (Exception e) {
//			log.info("Error While Updateing Retry Count : " + e.getLocalizedMessage());
//		}
//
//		return;
//	}
//
//	@Scheduled(cron = "${scheduler.cron}") // * 0/1 * ? * *")
//	public void chargebeeErrorCase() {
//		System.out.println();
//
//		try {
//			Calendar calendar = Calendar.getInstance();
//			calendar.setTime(new Date());
//			calendar.add(Calendar.DAY_OF_YEAR, schedulerDate);
//
//			System.out.println(calendar.getTimeInMillis());
//
//			ArrayList<ErrorInfo> errorList = errorinfoService.getAllErrorInfo();
//
//			for (ErrorInfo errInfo : errorList) {
//				try {
//
//					int retry = errInfo.getRetry_count();
//
//					if (errInfo.getService_name().equalsIgnoreCase("WaggleHooks") && retry <= 3) {
//
//						Environment.configure(chargebeeSiteName, chargebeeSiteKey);
//						Result result = Event.retrieve(errInfo.getEvent_id()).request();
//						System.out.println(result.toString());
//						boolean opStatus = false;
//						JSONObject js = new JSONObject(result.toString());
//
//						if (errInfo.getFunction_type().equalsIgnoreCase("VoidInvoice"))
//							opStatus = async.chargebeeEventWebhook_VOID_Operation(js.getJSONObject("event"));
//						else if (errInfo.getFunction_type().equalsIgnoreCase("Reactivation SIM"))
//							opStatus = async.chargebeeEventWebhook_SIM_Activaion(js.getJSONObject("event"));
//						else if (errInfo.getFunction_type().equalsIgnoreCase("Update User Feature"))
//							opStatus = async.chargebeeEventWebhook_UserFeature_Update(js.getJSONObject("event"));
//						else if (errInfo.getFunction_type().equalsIgnoreCase("Deactivation SIM"))
//							opStatus = async.chargebeeEventWebhook_SIM_Deactivation(js.getJSONObject("event"));
//
//						errInfo.setRetry_count(retry++);
//
//						if (opStatus)
//							errInfo.setResolved(true);
//
//						errorinfoService.updateErrorInfo(errInfo);
//
//					}
//
//				} catch (Exception e) {
//					log.info("Error While Updateing Retry Count : " + errInfo.getEvent_id());
//				}
//			}
//		} catch (Exception e) {
//			log.info("Error While Updateing Retry Count : " + e.getLocalizedMessage());
//		}
//
//		return;
//	}

	private void yu() {
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.set(Calendar.DATE, 1);

		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);

		System.out.println(cal.getTimeInMillis());

		System.out.println();
	}
}
