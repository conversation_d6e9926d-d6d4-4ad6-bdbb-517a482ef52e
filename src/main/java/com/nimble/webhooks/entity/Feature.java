package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name="feature", uniqueConstraints =@UniqueConstraint (columnNames={"feature_name" }) )

public class Feature implements Serializable{
	private static final long serialVersionUID = 0l;
	
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
	private long id ;
	
	@Column(name="feature_name")
	private String feature_name;
	
	@Column(name="description")
	private String description;
	
	@Column(name = "enable")
	private boolean enable;
	
	@ManyToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "type_id")
	private FeatureType type_id;
	
	@Transient
	private long featuretype;
	
	public Feature()
	{
		super();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getFeature_name() {
		return feature_name;
	}

	public void setFeature_name(String feature_name) {
		this.feature_name = feature_name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public FeatureType getType_id() {
		return type_id;
	}

	public void setType_id(FeatureType type_id) {
		this.type_id = type_id;
	}

	public long getFeaturetype() {
		return featuretype;
	}

	public void setFeaturetype(long featuretype) {
		this.featuretype = featuretype;
	}	
	
	
}
