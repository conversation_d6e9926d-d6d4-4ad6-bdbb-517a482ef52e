package com.nimble.webhooks.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name = "user_subscription")
@Table(name = "user_subscription", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class UserSubscription {

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;

	@Column(name = "user_id")
	private long user_id;

	@Column(name = "chargebeeid")
	private String chargebeeid = "NA";
	
	@Column(name = "cbsub_id")
	private String cbsub_id = "NA";
	
	@Column(name = "sub_status")
	private String sub_status = "NA";

	@Column(name = "first_planid")
	private String first_planid = "NA";

	@Column(name = "first_plan_dt")
	private Timestamp first_plan_dt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "cur_planid")
	private String cur_planid = "NA";

	@Column(name = "cur_plan_dt")
	private Timestamp cur_plan_dt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "free_trial_applied")
	private boolean free_trial_applied ;

	@Column(name = "vpmfirst_planid")
	private String vpmfirst_planid = "NA";

	@Column(name = "vpmfirst_plan_dt")
	private Timestamp vpmfirst_plan_dt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "vpmcur_planid")
	private String vpmcur_planid = "NA";

	@Column(name = "vpmcur_plan_dt")
	private Timestamp vpmcur_plan_dt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "vpmfree_trial_applied")
	private boolean vpmfree_trial_applied =false;

	@Column(name = "plan_info")
	private String plan_info ;

	@Column(name = "addon_info")
	private String addon_info ;
	
	private String prev_plan = "NA";
	
	private Timestamp prev_plan_start_dt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	private Timestamp prev_plan_end_dt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	private Timestamp next_renew_dt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	public String getPlan_info() {
		return plan_info;
	}

	public void setPlan_info(String plan_info) {
		this.plan_info = plan_info;
	}

	public String getAddon_info() {
		return addon_info;
	}

	public void setAddon_info(String addon_info) {
		this.addon_info = addon_info;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public String getFirst_planid() {
		return first_planid;
	}

	public void setFirst_planid(String first_planid) {
		this.first_planid = first_planid;
	}

	public Timestamp getFirst_plan_dt() {
		return first_plan_dt;
	}

	public void setFirst_plan_dt(Timestamp first_plan_dt) {
		this.first_plan_dt = first_plan_dt;
	}

	public String getCur_planid() {
		return cur_planid;
	}

	public void setCur_planid(String cur_planid) {
		this.cur_planid = cur_planid;
	}

	public Timestamp getCur_plan_dt() {
		return cur_plan_dt;
	}

	public void setCur_plan_dt(Timestamp cur_plan_dt) {
		this.cur_plan_dt = cur_plan_dt;
	}

	public boolean isFree_trial_applied() {
		return free_trial_applied;
	}

	public void setFree_trial_applied(boolean free_trial_applied) {
		this.free_trial_applied = free_trial_applied;
	}

	public String getVpmfirst_planid() {
		return vpmfirst_planid;
	}

	public void setVpmfirst_planid(String vpmfirst_planid) {
		this.vpmfirst_planid = vpmfirst_planid;
	}

	public Timestamp getVpmfirst_plan_dt() {
		return vpmfirst_plan_dt;
	}

	public void setVpmfirst_plan_dt(Timestamp vpmfirst_plan_dt) {
		this.vpmfirst_plan_dt = vpmfirst_plan_dt;
	}

	public String getVpmcur_planid() {
		return vpmcur_planid;
	}

	public void setVpmcur_planid(String vpmcur_planid) {
		this.vpmcur_planid = vpmcur_planid;
	}

	public Timestamp getVpmcur_plan_dt() {
		return vpmcur_plan_dt;
	}

	public void setVpmcur_plan_dt(Timestamp vpmcur_plan_dt) {
		this.vpmcur_plan_dt = vpmcur_plan_dt;
	}

	public boolean isVpmfree_trial_applied() {
		return vpmfree_trial_applied;
	}

	public void setVpmfree_trial_applied(boolean vpmfree_trial_applied) {
		this.vpmfree_trial_applied = vpmfree_trial_applied;
	}

	public String getSub_status() {
		return sub_status;
	}

	public void setSub_status(String sub_status) {
		this.sub_status = sub_status;
	}

	public String getCbsub_id() {
		return cbsub_id;
	}

	public void setCbsub_id(String cbsub_id) {
		this.cbsub_id = cbsub_id;
	}

	public String getPrev_plan() {
		return prev_plan;
	}

	public void setPrev_plan(String prev_plan) {
		this.prev_plan = prev_plan;
	}

	public Timestamp getPrev_plan_start_dt() {
		return prev_plan_start_dt;
	}

	public void setPrev_plan_start_dt(Timestamp prev_plan_start_dt) {
		this.prev_plan_start_dt = prev_plan_start_dt;
	}

	public Timestamp getPrev_plan_end_dt() {
		return prev_plan_end_dt;
	}

	public void setPrev_plan_end_dt(Timestamp prev_plan_end_dt) {
		this.prev_plan_end_dt = prev_plan_end_dt;
	}

	public Timestamp getNext_renew_dt() {
		return next_renew_dt;
	}

	public void setNext_renew_dt(Timestamp next_renew_dt) {
		this.next_renew_dt = next_renew_dt;
	}
	
}
