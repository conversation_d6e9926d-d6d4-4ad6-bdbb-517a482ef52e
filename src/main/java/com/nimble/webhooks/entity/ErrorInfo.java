package com.nimble.webhooks.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name="error_info")
@Table(name="error_info",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class ErrorInfo {
	
	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	@Column(name="event_id")
	private String event_id = "NA";
	
	@Column(name="service_name")
	private String service_name = "WaggleHooks";
	
	@Column(name="function_type")
	private String function_type = "NA";

	@Column(name="user_id")
	private long user_id = 0;
	
	@Column(name="retry_count")
	private int retry_count = 1;
	
	@Column(name="createdon")
	private String createdon;
	
	@Column(name="updatedon")
	private String updatedon ;
	
	@Column(name="description")
	private String description = "NA";
	
	@Column(name="meta_data")
	private String metaDatatext = "NA";
	
	@Column(name="is_resolved")
	private boolean isResolved = false;
	

	

	public boolean isResolved() {
		return isResolved;
	}

	public void setResolved(boolean isResolved) {
		this.isResolved = isResolved;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getEvent_id() {
		return event_id;
	}

	public void setEvent_id(String event_id) {
		this.event_id = event_id;
	}

	public String getService_name() {
		return service_name;
	}

	public void setService_name(String service_name) {
		this.service_name = service_name;
	}

	public String getFunction_type() {
		return function_type;
	}

	public void setFunction_type(String function_type) {
		this.function_type = function_type;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public int getRetry_count() {
		return retry_count;
	}

	public void setRetry_count(int retry_count) {
		this.retry_count = retry_count;
	}

	public String getCreatedon() {
		return createdon;
	}

	public void setCreatedon(String createdon) {
		this.createdon = createdon;
	}

	public String getUpdatedon() {
		return updatedon;
	}

	public void setUpdatedon(String updatedon) {
		this.updatedon = updatedon;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getMetaDatatext() {
		return metaDatatext;
	}

	public void setMetaDatatext(String metaDatatext) {
		this.metaDatatext = metaDatatext;
	}
	
	
	
	
}