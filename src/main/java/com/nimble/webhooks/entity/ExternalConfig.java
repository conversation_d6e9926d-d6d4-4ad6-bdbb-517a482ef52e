package com.nimble.webhooks.entity;
// Generated Jan 10, 2019 6:07:46 PM by Hibernate Tools 4.3.1


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * JAdclookup generated by hbm2java
 */
@Entity
@Table(name="external_config") 
public class ExternalConfig  implements java.io.Serializable {
	@Id
	@Column(name="id")
	@GenericGenerator(name="gen",strategy="identity")
	@GeneratedValue(generator="gen")
     private Long id;
	
	@Column(name="parametername")
     private String parametername;
    
	@Column(name="value")
     private String value;
	

	public ExternalConfig() {
		super();
		// TODO Auto-generated constructor stub
	}
	

	public ExternalConfig(Long id, String parametername, String value) {
		super();
		this.id = id;
		this.parametername = parametername;
		this.value = value;
	}
	
	


	public ExternalConfig(String parametername, String value) {
		super();
		this.parametername = parametername;
		this.value = value;
	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getParametername() {
		return parametername;
	}

	public void setParametername(String parametername) {
		this.parametername = parametername;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	    
    

}


