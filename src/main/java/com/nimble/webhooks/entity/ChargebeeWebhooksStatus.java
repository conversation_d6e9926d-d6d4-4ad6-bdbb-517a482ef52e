package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "chargebee_webhooks_status")
public class ChargebeeWebhooksStatus implements Serializable {

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id = 0;

	@Column(name = "event_id")
	private String eventId = "NA";

	@Column(name = "event_type")
	private String eventType = "NA";

	@Column(name = "event_process")
	private String eventProcess = "NA";

	@Column(name = "event_status")
	private String eventStatus = "NA";

	@Column(name = "chargebee_id")
	private String chargebeeId = "NA";

	@Column(name = "createdon")
	private String createdOn = "1753-01-01 00:00:00";

	@Column(name = "updatedon")
	private String updatedOn = "1753-01-01 00:00:00";

	@Column(name = "vpm_status")
	private int vpmStatus = 2;

	@Column(name = "activate_sim_status")
	private int activateSimStatus = 2;

	@Column(name = "deactivate_sim_status")
	private int deactivateSimStatus = 2;

	@Column(name = "status")
	private boolean status = false;

	@Column(name = "subscription_insert")
	private String subscriptionInsert = "2,2,2,2";

	@Column(name = "planfeature_status")
	private String planfeatureStatus = "NA";

	public String getPlanfeatureStatus() {
		return planfeatureStatus;
	}

	public void setPlanfeatureStatus(String planfeatureStatus) {
		this.planfeatureStatus = planfeatureStatus;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getEventProcess() {
		return eventProcess;
	}

	public void setEventProcess(String eventProcess) {
		this.eventProcess = eventProcess;
	}

	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	public String getChargebeeId() {
		return chargebeeId;
	}

	public void setChargebeeId(String chargebeeId) {
		this.chargebeeId = chargebeeId;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public int getVpmStatus() {
		return vpmStatus;
	}

	public void setVpmStatus(int vpmStatus) {
		this.vpmStatus = vpmStatus;
	}

	public int getActivateSimStatus() {
		return activateSimStatus;
	}

	public void setActivateSimStatus(int activateSimStatus) {
		this.activateSimStatus = activateSimStatus;
	}

	public int getDeactivateSimStatus() {
		return deactivateSimStatus;
	}

	public void setDeactivateSimStatus(int deactivateSimStatus) {
		this.deactivateSimStatus = deactivateSimStatus;
	}

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public String getSubscriptionInsert() {
		return subscriptionInsert;
	}

	public void setSubscriptionInsert(String subscriptionInsert) {
		this.subscriptionInsert = subscriptionInsert;
	}

}