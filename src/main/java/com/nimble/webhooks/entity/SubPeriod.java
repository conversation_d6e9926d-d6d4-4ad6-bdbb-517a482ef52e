package com.nimble.webhooks.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name="sub_period")
@Table(name="sub_period",uniqueConstraints =@UniqueConstraint (columnNames={"id"} ))
public class SubPeriod {
	
	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	
	private String period_name;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getPeriod_name() {
		return period_name;
	}

	public void setPeriod_name(String period_name) {
		this.period_name = period_name;
	}

	public SubPeriod() {
		super();
	}
	public SubPeriod(int id, String period_name) {
		super();
		this.id = id;
		this.period_name = period_name;
	}
	
	

}
