package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity(name = "cb_activate_cancel_sub_status")
@Table(name = "cb_activate_cancel_sub_status")
public class CbActivateCancelSubStatus implements Serializable {

	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	long id;
	
	@Column(name="chargebeeid")
	String chargebeeId = "NA";
	
	@Column(name="simno")
	String simNo = "NA";
	
	@Column(name="userid")
	String userId = "NA";
	
	@Column(name="status")
	boolean status = false;

	@Column(name="gateway_operation")
	String gatewayStatus = "NA";
	
	@Column(name="ordermap_operation")
	String ordermapStatus = "NA";
	
	@Column(name="user_email")
	String email = "NA";

	@Column(name="event_id")
	String eventId = "NA";
	
	@Column(name="sim_vendor")
	String simVendor = "NA";
	
	@Column(name="meids")
	String meids = "NA";
	
	@Column(name="action")
	String action = "NA";
	
	@Column(name="details") 
	String details ="NA";
	
	@Column(name="sim_status")
	String simStatus = "NA";

	public String getSimStatus() {
		return simStatus;
	}

	public void setSimStatus(String simStatus) {
		this.simStatus = simStatus;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getChargebeeId() {
		return chargebeeId;
	}

	public void setChargebeeId(String chargebeeId) {
		this.chargebeeId = chargebeeId;
	}

	public String getSimNo() {
		return simNo;
	}

	public void setSimNo(String simNo) {
		this.simNo = simNo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public String getGatewayStatus() {
		return gatewayStatus;
	}

	public void setGatewayStatus(String gatewayStatus) {
		this.gatewayStatus = gatewayStatus;
	}

	public String getOrdermapStatus() {
		return ordermapStatus;
	}

	public void setOrdermapStatus(String ordermapStatus) {
		this.ordermapStatus = ordermapStatus;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public String getSimVendor() {
		return simVendor;
	}

	public void setSimVendor(String simVendor) {
		this.simVendor = simVendor;
	}

	public String getMeids() {
		return meids;
	}

	public void setMeids(String meids) {
		this.meids = meids;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getDetails() {
		return details;
	}

	public void setDetails(String details) {
		this.details = details;
	}
		
	
}
