package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name = "invoicehistory_new_v2")
@Table(name = "invoicehistory_new_v2", uniqueConstraints = @UniqueConstraint(columnNames = { "invoice_id" }))
public class Invoices implements Serializable {

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;

	@Column(name = "invoice_id")
	private String invoice_id;

	@Column(name = "invoice_date")
	private String invoice_date = "1753-01-01 00:00:00";

	@Column(name = "planid")
	private String planName = "NA";

	@Column(name = "subscription_id")
	private String sub_id = "NA";

	@Column(name = "chargebee_id")
	private String chargebeeId = "NA";

	@Column(name = "invoicestatus")
	private String invoice_Status = "NA";

	@Column(name = "billing_email")
	private String billing_Email = "NA";

	@Column(name = "amount_paid")
	private String amountPaid = "NA";

	@Column(name = "coupon_id")
	private String couponCode = "NA";

	@Column(name = "coupon_amount")
	private int couponAmount = 0;

	@Column(name = "start_date")
	private String startDate = "1753-01-01 00:00:00";

	@Column(name = "end_date")
	private String endDate = "1753-01-01 00:00:00";

	@Column(name = "is_deleted")
	private int isDeleted = 0;

	@Column(name = "addon_id")
	private String addons = "NA:0";

	@Column(name = "plan_amt")
	private int plan_amt = 0;

	@Column(name = "credits_applied")
	private int credits_applied = 0;

	@Column(name = "issued_amt")
	private int issued_amt = 0;

	@Column(name = "refund_amt")
	private int refund_amt = 0;

	@Column(name = "plan_period")
	private String planperiod;

	@Column(name = "curr_code")
	private String currency_code;

	@Column(name = "exchange_rate")
	private float exchange_rate = 1;
	
	@Column(name = "state_code")
	private String state_code = "NA";


	public Invoices() {
		super();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getInvoice_id() {
		return invoice_id;
	}

	public void setInvoice_id(String invoice_id) {
		this.invoice_id = invoice_id;
	}

	public String getInvoice_date() {
		return invoice_date;
	}

	public void setInvoice_date(String invoice_date) {
		this.invoice_date = invoice_date;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public String getChargebeeId() {
		return chargebeeId;
	}

	public void setChargebeeId(String chargebeeId) {
		this.chargebeeId = chargebeeId;
	}

	public String getInvoice_Status() {
		return invoice_Status;
	}

	public void setInvoice_Status(String invoice_Status) {
		this.invoice_Status = invoice_Status;
	}

	public String getBilling_Email() {
		return billing_Email;
	}

	public void setBilling_Email(String billing_Email) {
		this.billing_Email = billing_Email;
	}

	public String getAmountPaid() {
		return amountPaid;
	}

	public void setAmountPaid(String amountPaid) {
		this.amountPaid = amountPaid;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public int getCouponAmount() {
		return couponAmount;
	}

	public void setCouponAmount(int couponAmount) {
		this.couponAmount = couponAmount;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public int getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(int isDeleted) {
		this.isDeleted = isDeleted;
	}

	public String getAddons() {
		return addons;
	}

	public void setAddons(String addons) {
		this.addons = addons;
	}

	public int getPlan_amt() {
		return plan_amt;
	}

	public void setPlan_amt(int plan_amt) {
		this.plan_amt = plan_amt;
	}

	public int getCredits_applied() {
		return credits_applied;
	}

	public void setCredits_applied(int credits_applied) {
		this.credits_applied = credits_applied;
	}

	public int getIssued_amt() {
		return issued_amt;
	}

	public void setIssued_amt(int issued_amt) {
		this.issued_amt = issued_amt;
	}

	public int getRefund_amt() {
		return refund_amt;
	}

	public void setRefund_amt(int refund_amt) {
		this.refund_amt = refund_amt;
	}

	public String getPlanperiod() {
		return planperiod;
	}

	public void setPlanperiod(String planperiod) {
		this.planperiod = planperiod;
	}

	public String getCurrency_code() {
		return currency_code;
	}

	public void setCurrency_code(String currency_code) {
		this.currency_code = currency_code;
	}

	public float getExchange_rate() {
		return exchange_rate;
	}

	public void setExchange_rate(float exchange_rate) {
		this.exchange_rate = exchange_rate;
	}

	public String getState_code() {
		return state_code;
	}

	public void setState_code(String state_code) {
		this.state_code = state_code;
	}

	public Invoices(int id, String invoice_id, String invoice_date, String planName, String sub_id, String chargebeeId,
			String invoice_Status, String billing_Email, String amountPaid, String couponCode, int couponAmount,
			String startDate, String endDate, int isDeleted, String addons, int plan_amt) {
		super();
		this.id = id;
		this.invoice_id = invoice_id;
		this.invoice_date = invoice_date;
		this.planName = planName;
		this.sub_id = sub_id;
		this.chargebeeId = chargebeeId;
		this.invoice_Status = invoice_Status;
		this.billing_Email = billing_Email;
		this.amountPaid = amountPaid;
		this.couponCode = couponCode;
		this.couponAmount = couponAmount;
		this.startDate = startDate;
		this.endDate = endDate;
		this.isDeleted = isDeleted;
		this.addons = addons;
		this.plan_amt = plan_amt;
	}

}
