package com.nimble.webhooks.entity;


import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name = "all_product_subscription")
@Table(name = "all_product_subscription")
public class AllProductSubscription implements Serializable {


	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "subscription_id")
	private String subscriptionId;

	@Column(name = "plan_id")
	private String planId;

	@Column(name = "subscription_status")
	private String subscriptionStatus;

	@Column(name = "subscription_created_at")
	private String subscriptionCreatedAt;

	@Column(name = "subscription_started_at")
	private String subscriptionStartedAt;

	@Column(name = "subscription_activated_at")
	private String subscriptionActivatedAt;

	@Column(name = "subscription_cancelled_at")
	private String subscriptionCancelledAt;

	@Column(name = "plan_amount")
	private String planAmount;

	@Column(name = "chargebee_id")
	private String customerId;

	@Column(name = "billing_email")
	private String billingEmail;

	@Column(name = "addons")
	private String addons;

	@Column(name = "trial_start")
	private String trialStart;

	@Column(name = "trial_end")
	private String trialEnd;

	@Column(name = "subscription_next_billing_at")
	private String nextBillingAt;

	@Column(name = "updated_indb")
	private String updatedIndb;

	@Column(name = "is_deleted")
	private int isDeleted;

	@Column(name = "metadata")
	private String metaData;

	@Column(name = "enable")
	private int enable =1;

	@Column(name = "updated_date")
	private String updatedDate;

	@Column(name = "plan_period")
	private String planPeriod;
	
	@Column(name = "event_type")
	String event_type = "NA";

	@Column(name = "curr_code")
	private String currency_code = "NA";

	@Column(name = "exchange_rate")
	private float exchange_rate = 1;
	
	@Column(name = "state_code")
	private String state_code="NA";
	
	@Column(name = "monitor_type")
	private long monitor_type = 1;
	
	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "mrr")
	private long mrr=0;
	
	@Column(name = "resume_date")
	private String resume_date;
	
	public AllProductSubscription() {
		super();
	}

	public AllProductSubscription(AllProductSubscription subscription) {
		this.id = subscription.getId();
		this.subscriptionId = subscription.getSubscriptionId();
		this.planId = subscription.getPlanId();
		this.subscriptionStatus = subscription.getSubscriptionStatus();
		this.subscriptionCreatedAt = subscription.getSubscriptionCreatedAt();
		this.subscriptionStartedAt = subscription.getSubscriptionStartedAt();
		this.subscriptionActivatedAt = subscription.getSubscriptionActivatedAt();
		this.subscriptionCancelledAt = subscription.getSubscriptionCancelledAt();
		this.planAmount = subscription.getPlanAmount();
		this.customerId = subscription.getCustomerId();
		this.billingEmail = subscription.getBillingEmail();
		this.addons = subscription.getAddons();
		this.trialStart = subscription.getTrialStart();
		this.trialEnd = subscription.getTrialEnd();
		this.nextBillingAt = subscription.getNextBillingAt();
		this.updatedIndb = subscription.getUpdatedIndb();
		this.isDeleted = subscription.getIsDeleted();
		this.metaData = subscription.getMetaData();
		this.enable = subscription.getEnable();
		this.updatedDate = subscription.getUpdatedDate();
		this.planPeriod = subscription.getPlanPeriod();
		this.event_type = subscription.getEvent_type();
		this.currency_code = subscription.getCurrency_code();
		this.exchange_rate = subscription.getExchange_rate();
		this.state_code = subscription.getState_code();
		this.monitor_type = subscription.getMonitor_type();
		this.gateway_id = subscription.getGateway_id();
		this.mrr = subscription.getMrr();
		this.resume_date = subscription.getResume_date();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getSubscriptionId() {
		return subscriptionId;
	}

	public void setSubscriptionId(String subscriptionId) {
		this.subscriptionId = subscriptionId;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getSubscriptionStatus() {
		return subscriptionStatus;
	}

	public void setSubscriptionStatus(String subscriptionStatus) {
		this.subscriptionStatus = subscriptionStatus;
	}

	public String getSubscriptionCreatedAt() {
		return subscriptionCreatedAt;
	}

	public void setSubscriptionCreatedAt(String subscriptionCreatedAt) {
		this.subscriptionCreatedAt = subscriptionCreatedAt;
	}

	public String getSubscriptionStartedAt() {
		return subscriptionStartedAt;
	}

	public void setSubscriptionStartedAt(String subscriptionStartedAt) {
		this.subscriptionStartedAt = subscriptionStartedAt;
	}

	public String getSubscriptionActivatedAt() {
		return subscriptionActivatedAt;
	}

	public void setSubscriptionActivatedAt(String subscriptionActivatedAt) {
		this.subscriptionActivatedAt = subscriptionActivatedAt;
	}

	public String getSubscriptionCancelledAt() {
		return subscriptionCancelledAt;
	}

	public void setSubscriptionCancelledAt(String subscriptionCancelledAt) {
		this.subscriptionCancelledAt = subscriptionCancelledAt;
	}

	public String getPlanAmount() {
		return planAmount;
	}

	public void setPlanAmount(String planAmount) {
		this.planAmount = planAmount;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getBillingEmail() {
		return billingEmail;
	}

	public void setBillingEmail(String customerEmail) {
		this.billingEmail = customerEmail;
	}

	public String getAddons() {
		return addons;
	}

	public void setAddons(String addons) {
		this.addons = addons;
	}

	public String getTrialStart() {
		return trialStart;
	}

	public void setTrialStart(String trialStart) {
		this.trialStart = trialStart;
	}

	public String getTrialEnd() {
		return trialEnd;
	}

	public void setTrialEnd(String trialEnd) {
		this.trialEnd = trialEnd;
	}

	public String getNextBillingAt() {
		return nextBillingAt;
	}

	public void setNextBillingAt(String nextBillingAt) {
		this.nextBillingAt = nextBillingAt;
	}

	public String getUpdatedIndb() {
		return updatedIndb;
	}

	public void setUpdatedIndb(String updatedIndb) {
		this.updatedIndb = updatedIndb;
	}

	public int getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(int isDeleted) {
		this.isDeleted = isDeleted;
	}

	public String getMetaData() {
		return metaData;
	}

	public void setMetaData(String metaData) {
		this.metaData = metaData;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public String getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(String updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getPlanPeriod() {
		return planPeriod;
	}

	public void setPlanPeriod(String planPeriod) {
		this.planPeriod = planPeriod;
	}

	public String getEvent_type() {
		return event_type;
	}

	public void setEvent_type(String event_type) {
		this.event_type = event_type;
	}

	public String getCurrency_code() {
		return currency_code;
	}

	public void setCurrency_code(String currency_code) {
		this.currency_code = currency_code;
	}

	public float getExchange_rate() {
		return exchange_rate;
	}

	public void setExchange_rate(float exchange_rate) {
		this.exchange_rate = exchange_rate;
	}

	public String getState_code() {
		return state_code;
	}

	public void setState_code(String state_code) {
		this.state_code = state_code;
	}

	public AllProductSubscription(String subscriptionId, String planId, String subscriptionStatus,
			String subscriptionCreatedAt, String subscriptionStartedAt, String subscriptionActivatedAt,
			String subscriptionCancelledAt, String planAmount, String customerId, String billingEmail, String addons,
			String trialStart, String trialEnd, String nextBillingAt, String updatedIndb, int isDeleted,
			String metaData, int enable, String updatedDate, String planPeriod,String event_type,String currency_code,
			float exchange_rate,String state_code,long monitor_type,long mrr, String resume_date) {
		super();
		//this.id = id;
		this.subscriptionId = subscriptionId;
		this.planId = planId;
		this.subscriptionStatus = subscriptionStatus;
		this.subscriptionCreatedAt = subscriptionCreatedAt;
		this.subscriptionStartedAt = subscriptionStartedAt;
		this.subscriptionActivatedAt = subscriptionActivatedAt;
		this.subscriptionCancelledAt = subscriptionCancelledAt;
		this.planAmount = planAmount;
		this.customerId = customerId;
		this.billingEmail = billingEmail;
		this.addons = addons;
		this.trialStart = trialStart;
		this.trialEnd = trialEnd;
		this.nextBillingAt = nextBillingAt;
		this.updatedIndb = updatedIndb;
		this.isDeleted = isDeleted;
		this.metaData = metaData;
		this.enable = enable;
		this.updatedDate = updatedDate;
		this.planPeriod = planPeriod;
		this.event_type = event_type;
		this.currency_code = currency_code;
		this.exchange_rate = exchange_rate;
		this.state_code = state_code;
		this.monitor_type = monitor_type;
		this.mrr = mrr;
		this.resume_date = resume_date;
	}

	public long getMonitor_type() {
		return monitor_type;
	}

	public void setMonitor_type(long monitor_type) {
		this.monitor_type = monitor_type;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public long getMrr() {
		return mrr;
	}

	public void setMrr(long mrr) {
		this.mrr = mrr;
	}

	public String getResume_date() {
		return resume_date;
	}

	public void setResume_date(String resume_date) {
		this.resume_date = resume_date;
	}

}
