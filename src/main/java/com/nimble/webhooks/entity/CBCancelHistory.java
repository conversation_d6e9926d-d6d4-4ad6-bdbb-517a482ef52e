package com.nimble.webhooks.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name = "cb_cancel_history")
@Table(name = "cb_cancel_history", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class CBCancelHistory {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "chargebee_id")
    private String chargebeeId;

    @Column(name = "sub_id")
    private String subscriptionId;

    @Column(name = "plan_id")
    private String planId;

    @Column(name = "billing_email")
    private String billingEmail;

    @Column(name = "plan_period")
    private String planPeriod;

    @Column(name = "createdon")
    private String createdOn;

    @Column(name = "cancelled_date")
    private String cancelledDate;

    public CBCancelHistory() {
        super();
    }

    public CBCancelHistory(Integer id, String chargebeeId, String subscriptionId, String planId,
                           String billingEmail, String planPeriod, String createdOn, String cancelledDate) {
        this.id = id;
        this.chargebeeId = chargebeeId;
        this.subscriptionId = subscriptionId;
        this.planId = planId;
        this.billingEmail = billingEmail;
        this.planPeriod = planPeriod;
        this.createdOn = createdOn;
        this.cancelledDate = cancelledDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChargebeeId() {
        return chargebeeId;
    }

    public void setChargebeeId(String chargebeeId) {
        this.chargebeeId = chargebeeId;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getBillingEmail() {
        return billingEmail;
    }

    public void setBillingEmail(String billingEmail) {
        this.billingEmail = billingEmail;
    }

    public String getPlanPeriod() {
        return planPeriod;
    }

    public void setPlanPeriod(String planPeriod) {
        this.planPeriod = planPeriod;
    }

    public String getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(String createdOn) {
        this.createdOn = createdOn;
    }

    public String getCancelledDate() {
        return cancelledDate;
    }

    public void setCancelledDate(String cancelledDate) {
        this.cancelledDate = cancelledDate;
    }
}
