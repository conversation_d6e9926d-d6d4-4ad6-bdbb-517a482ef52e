package com.nimble.webhooks.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "addition_benefits_cancel_reward")
public class AdditionBenefitsCancelReward {

	@Id
	@GenericGenerator(name = "gen", strategy = "identity")
	@GeneratedValue(generator = "gen")
	private long id;
	
	private long user_id = 0;
	
	private String code = "NA";
	
	private String start_date = "1753-01-01 11:11:11";
	
	private String end_date = "1753-01-01 11:11:11";

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getStart_date() {
		return start_date;
	}

	public void setStart_date(String start_date) {
		this.start_date = start_date;
	}

	public String getEnd_date() {
		return end_date;
	}

	public void setEnd_date(String end_date) {
		this.end_date = end_date;
	}

	public AdditionBenefitsCancelReward() {
		super();
	}
	
	public AdditionBenefitsCancelReward(long id, long user_id, String code, String start_date, String end_date) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.code = code;
		this.start_date = start_date;
		this.end_date = end_date;
	}
	
	
	
}
