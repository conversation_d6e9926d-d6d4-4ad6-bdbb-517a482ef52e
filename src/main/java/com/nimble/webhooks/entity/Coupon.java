package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "coupon_details")
public class Coupon implements Serializable {

	@Id
	@Column(name = "coupon_id")
	private String coupon_id = "NA";

	@Column(name = "coupon_name")
	private String coupon_name = "NA";

	@Column(name = "acquisition_type")
	private int acquisition_type = 0;

	@Column(name = "discount_type")
	private String discount_type = "NA";

	@Column(name = "discount_value")
	private String discount_value = "NA";

	@Column(name = "coupon_status")
	private String coupon_status = "NA";

	@Column(name = "duration_type")
	private String duration_type = "NA";

	@Column(name = "valid_till")
	private String valid_till = "NA";

	@Column(name = "is_deleted")
	private boolean deleted = false;

	@Column(name = "created_on")
	private String created_on = "1753-01-01 00:00:01";

	@Column(name = "updated_on")
	private String updated_on = "1753-01-01 00:00:01";

	public String getCoupon_id() {
		return coupon_id;
	}

	public void setCoupon_id(String coupon_id) {
		this.coupon_id = coupon_id;
	}

	public String getCoupon_name() {
		return coupon_name;
	}

	public void setCoupon_name(String coupon_name) {
		this.coupon_name = coupon_name;
	}

	public int getAcquisition_type() {
		return acquisition_type;
	}

	public void setAcquisition_type(int acquisition_type) {
		this.acquisition_type = acquisition_type;
	}

	public String getDiscount_type() {
		return discount_type;
	}

	public void setDiscount_type(String discount_type) {
		this.discount_type = discount_type;
	}

	public String getDiscount_value() {
		return discount_value;
	}

	public void setDiscount_value(String discount_value) {
		this.discount_value = discount_value;
	}

	public String getCoupon_status() {
		return coupon_status;
	}

	public void setCoupon_status(String coupon_status) {
		this.coupon_status = coupon_status;
	}

	public String getDuration_type() {
		return duration_type;
	}

	public void setDuration_type(String duration_type) {
		this.duration_type = duration_type;
	}

	public String getValid_till() {
		return valid_till;
	}

	public void setValid_till(String valid_till) {
		this.valid_till = valid_till;
	}

	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	public String getCreated_on() {
		return created_on;
	}

	public void setCreated_on(String created_on) {
		this.created_on = created_on;
	}

	public String getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(String updated_on) {
		this.updated_on = updated_on;
	}

}
