package com.nimble.webhooks.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="recharge_latest_sub_history")
public class LatestRechargeSubscription implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;	

	@Column(name="sub_id")
	private String sub_id = "NA";
	
	@Column(name="order_id")
	private String order_id = "NA";
	
	@Column(name="customer_id")
	private String customer_id = "NA";
	
	@Column(name="email")
	private String email = "NA";
	
	@Column(name="sku")
	private String sku = "NA";
	
	@Column(name="nextrenewal_at")
	private Date nextrenewal_at;
			
	@Column(name="status")
	private String status = "NA";
	
	@Column(name="created_at")
	private Date created_at;
	
	@Column(name="updated_at")
	private Date updated_at;
	
	@Column(name="cancelled_at")
	private Date cancelled_at;

	@Column(name="event_type")
	private String event_type = "NA";
	
	@Column(name="cb_plan")
	private String cb_plan = "NA";
	
	@Column(name="is_sub_activated")
	private boolean is_sub_activated = false;
	
	@Column(name="enable")
	private boolean enable = true;
	
	@Column(name="cb_sub_status")
	private String cb_sub_status = "NA";
	
	@Column(name="price")
	private float price =0.0f;
	
	@Column(name="order_count")
	private float order_count =0;
	
	@Column(name="mapped_count")
	private float mapped_count =0;
	
	public LatestRechargeSubscription() {
		
	}
	
	public LatestRechargeSubscription(String sub_id, String order_id, String customer_id, String email,
			String sku,String cb_plan, Date nextrenewal_at, String status, Date created_at, Date updated_at,
			Date cancelled_at,float price, int order_count,String cb_sub_status) {
		super();
		this.sub_id = sub_id;
		this.order_id = order_id;
		this.customer_id = customer_id;
		this.email = email;
		this.sku = sku;
		this.cb_plan = cb_plan;
		this.nextrenewal_at = nextrenewal_at;
		this.status = status;
		this.created_at = created_at;
		this.updated_at = updated_at;
		this.cancelled_at = cancelled_at;
		this.price = price;
		this.order_count = order_count;
		this.cb_sub_status = cb_sub_status;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getCustomer_id() {
		return customer_id;
	}

	public void setCustomer_id(String customer_id) {
		this.customer_id = customer_id;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public Date getNextrenewal_at() {
		return nextrenewal_at;
	}

	public void setNextrenewal_at(Date nextrenewal_at) {
		this.nextrenewal_at = nextrenewal_at;
	}
	
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getCreated_at() {
		return created_at;
	}

	public void setCreated_at(Date created_at) {
		this.created_at = created_at;
	}

	public Date getUpdated_at() {
		return updated_at;
	}

	public void setUpdated_at(Date updated_at) {
		this.updated_at = updated_at;
	}

	public Date getCancelled_at() {
		return cancelled_at;
	}

	public void setCancelled_at(Date cancelled_at) {
		this.cancelled_at = cancelled_at;
	}

	public String getEvent_type() {
		return event_type;
	}

	public void setEvent_type(String event_type) {
		this.event_type = event_type;
	}

	public String getCb_plan() {
		return cb_plan;
	}

	public void setCb_plan(String cb_plan) {
		this.cb_plan = cb_plan;
	}

	public boolean isIs_sub_activated() {
		return is_sub_activated;
	}

	public void setIs_sub_activated(boolean is_sub_activated) {
		this.is_sub_activated = is_sub_activated;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public float getOrder_count() {
		return order_count;
	}

	public void setOrder_count(float order_count) {
		this.order_count = order_count;
	}

	public float getMapped_count() {
		return mapped_count;
	}

	public void setMapped_count(float mapped_count) {
		this.mapped_count = mapped_count;
	}

	public String getCb_sub_status() {
		return cb_sub_status;
	}

	public void setCb_sub_status(String cb_sub_status) {
		this.cb_sub_status = cb_sub_status;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}
	
}


