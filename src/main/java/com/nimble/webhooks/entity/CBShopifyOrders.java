package com.nimble.webhooks.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "cb_shopify_orders")
public class CBShopifyOrders implements Serializable {

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id = 0;

	@Column(name = "cbtag")
	private String cbtag = "NA";

	@Column(name = "variant_id")
	private String variantId = "NA";

	@Column(name = "enable")
	private boolean enable = true;

	@Column(name = "fulfillment")
	private boolean fulfillment = true;

	@Column(name = "discount")
	private int discount = 0;

	@Column(name = "tags")
	private String tags = "NA";

	@Column(name = "Createdon")
	private String Createdon = "1753-01-01 00:00:00";

	public int getDiscount() {
		return discount;
	}

	public void setDiscount(int discount) {
		this.discount = discount;
	}

	public String getTags() {
		return tags;
	}

	public void setTags(String tags) {
		this.tags = tags;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getCbtag() {
		return cbtag;
	}

	public void setCbtag(String cbtag) {
		this.cbtag = cbtag;
	}

	public String getVariantId() {
		return variantId;
	}

	public void setVariantId(String variantId) {
		this.variantId = variantId;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public boolean isFulfillment() {
		return fulfillment;
	}

	public void setFulfillment(boolean fulfillment) {
		this.fulfillment = fulfillment;
	}

	public String getCreatedon() {
		return Createdon;
	}

	public void setCreatedon(String createdon) {
		Createdon = createdon;
	}

}
