package com.nimble.webhooks.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name="plan_to_period")
@Table(name="plan_to_period",uniqueConstraints =@UniqueConstraint (columnNames={"id"} ))
public class PlantoPeriod {

	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;	
	private long plan_id;	
	private long sub_period_id;
	private String chargebee_planid;
	private int custom;
	private int enable;
	private int strike_price;
	private String ios_planid;
	private float ios_price	;
	private int ios_showplan;	
	private int free_trial_days;	
	private String offer_desc;	
	private int offer_id;	
	private String offer_label;	
	private String offer_content;
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public long getPlan_id() {
		return plan_id;
	}
	public void setPlan_id(long plan_id) {
		this.plan_id = plan_id;
	}
	public long getSub_period_id() {
		return sub_period_id;
	}
	public void setSub_period_id(long sub_period_id) {
		this.sub_period_id = sub_period_id;
	}
	public String getChargebee_planid() {
		return chargebee_planid;
	}
	public void setChargebee_planid(String chargebee_planid) {
		this.chargebee_planid = chargebee_planid;
	}
	public int getCustom() {
		return custom;
	}
	public void setCustom(int custom) {
		this.custom = custom;
	}
	public int getEnable() {
		return enable;
	}
	public void setEnable(int enable) {
		this.enable = enable;
	}
	public int getStrike_price() {
		return strike_price;
	}
	public void setStrike_price(int strike_price) {
		this.strike_price = strike_price;
	}
	public String getIos_planid() {
		return ios_planid;
	}
	public void setIos_planid(String ios_planid) {
		this.ios_planid = ios_planid;
	}
	public float getIos_price() {
		return ios_price;
	}
	public void setIos_price(float ios_price) {
		this.ios_price = ios_price;
	}
	public int getIos_showplan() {
		return ios_showplan;
	}
	public void setIos_showplan(int ios_showplan) {
		this.ios_showplan = ios_showplan;
	}
	public int getFree_trial_days() {
		return free_trial_days;
	}
	public void setFree_trial_days(int free_trial_days) {
		this.free_trial_days = free_trial_days;
	}
	public String getOffer_desc() {
		return offer_desc;
	}
	public void setOffer_desc(String offer_desc) {
		this.offer_desc = offer_desc;
	}
	public int getOffer_id() {
		return offer_id;
	}
	public void setOffer_id(int offer_id) {
		this.offer_id = offer_id;
	}
	public String getOffer_label() {
		return offer_label;
	}
	public void setOffer_label(String offer_label) {
		this.offer_label = offer_label;
	}
	public String getOffer_content() {
		return offer_content;
	}
	public void setOffer_content(String offer_content) {
		this.offer_content = offer_content;
	}
	public PlantoPeriod() {
		super();
	}
	public PlantoPeriod(long plan_id, long sub_period_id, String chargebee_planid) {
		super();
		this.plan_id = plan_id;
		this.sub_period_id = sub_period_id;
		this.chargebee_planid = chargebee_planid;
	}
	
	
	
}
