package com.nimble.webhooks.entity;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name = "device_subscription")
@Table(name = "device_subscription", uniqueConstraints = @UniqueConstraint(columnNames = { "id" }))
public class DeviceSubscription implements Cloneable{

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;

	@Column(name = "user_id")
	private long user_id;
	
	@Column(name = "gateway_id")
	private long gateway_id;

	@Column(name = "purchase_date")
	private Timestamp purchase_date = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "instal_date")
	private Timestamp instal_date = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "paid_subscription_date")
	private Timestamp paid_subscription_date = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "cancel_date")
	private Timestamp cancel_date = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "sub_status")
	private String sub_status ="";

	@Column(name = "sales_channel")
	private String sales_channel = "NA";

	@Column(name = "chargebee_id")
	private String chargebeeid = "NA";
	
	@Column(name = "plan_id")
	private String plan_id = "NA";

	@Column(name = "updated_on")
	private Timestamp updated_on = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "isgps")
	private boolean isgps =false;

	@Column(name = "cur_period")
	private String cur_period ="NA";

	@Column(name = "first_plan")
	private String first_plan ="NA" ;
	
	@Column(name = "first_period")
	private String first_period ="NA";

	@Column(name = "order_id")
	private String order_id="NA" ;
	
	@Column(name = "return_date")
	private Timestamp return_date = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "is_deleted")
	private boolean is_deleted =false;
	
	@Column(name = "deleted_on")
	private Timestamp deleted_on = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "order_account_status")
	private String order_account_status = "NA";
	
	@Column(name = "mtype_id")
	private long mtype_id=0;
	
	@Column(name = "trial_start_date")
	private Timestamp trial_start_date = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "is_bundle")
	private boolean is_bundle =false;
	
	@Column(name = "is_test")
	private boolean is_test =false;
	
	@Column(name = "prev_planid")
	private String prev_planid ="NA";

	@Column(name = "prev_period")
	private String prev_period = "NA";
	
	@Column(name = "prev_plan_startdt")
	private Timestamp prev_plan_startdt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "prev_plan_enddt")
	private Timestamp prev_plan_enddt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "cur_plan_startdt")
	private Timestamp cur_plan_startdt = Timestamp.valueOf("1753-01-01 00:00:00");

	@Column(name = "sub_id")
	private String sub_id = "NA";
	
	@Column(name = "cur_plan_enddt")
	private Timestamp cur_plan_enddt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "next_renewal_dt")
	private Timestamp next_renewal_dt = Timestamp.valueOf("1753-01-01 00:00:00");
	
	@Column(name = "cur_plan_activated_date")
	private Timestamp cur_plan_activated_date = Timestamp.valueOf("1753-01-01 00:00:00");
	

	public DeviceSubscription()
	{
		super();
	}
	
	public DeviceSubscription(long id, long user_id, long gateway_id, Timestamp purchase_date, Timestamp instal_date,
			Timestamp paid_subscription_date, Timestamp cancel_date, String sub_status, String sales_channel,
			String chargebeeid, String plan_id, Timestamp updated_on, boolean isgps, String cur_period,
			String first_plan, String first_period, String order_id, Timestamp return_date, boolean is_deleted,
			Timestamp deleted_on, String order_account_status, long mtype_id, Timestamp trial_start_date,
			boolean is_bundle, boolean is_test, String prev_planid, String prev_period, Timestamp prev_plan_startdt,
			Timestamp prev_plan_enddt, Timestamp cur_plan_startdt, String sub_id) {
		super();
		this.id = id;
		this.user_id = user_id;
		this.gateway_id = gateway_id;
		this.purchase_date = purchase_date;
		this.instal_date = instal_date;
		this.paid_subscription_date = paid_subscription_date;
		this.cancel_date = cancel_date;
		this.sub_status = sub_status;
		this.sales_channel = sales_channel;
		this.chargebeeid = chargebeeid;
		this.plan_id = plan_id;
		this.updated_on = updated_on;
		this.isgps = isgps;
		this.cur_period = cur_period;
		this.first_plan = first_plan;
		this.first_period = first_period;
		this.order_id = order_id;
		this.return_date = return_date;
		this.is_deleted = is_deleted;
		this.deleted_on = deleted_on;
		this.order_account_status = order_account_status;
		this.mtype_id = mtype_id;
		this.trial_start_date = trial_start_date;
		this.is_bundle = is_bundle;
		this.is_test = is_test;
		this.prev_planid = prev_planid;
		this.prev_period = prev_period;
		this.prev_plan_startdt = prev_plan_startdt;
		this.prev_plan_enddt = prev_plan_enddt;
		this.cur_plan_startdt = cur_plan_startdt;
		this.sub_id = sub_id;
	}


	@Override
	public String toString() {
		return "DeviceSubscription [id=" + id + ", user_id=" + user_id + ", gateway_id=" + gateway_id
				+ ", purchase_date=" + purchase_date + ", instal_date=" + instal_date + ", paid_subscription_date="
				+ paid_subscription_date + ", cancel_date=" + cancel_date + ", sub_status=" + sub_status
				+ ", sales_channel=" + sales_channel + ", chargebeeid=" + chargebeeid + ", plan_id=" + plan_id
				+ ", updated_on=" + updated_on + ", isgps=" + isgps + ", cur_period=" + cur_period + ", first_plan="
				+ first_plan + ", first_period=" + first_period + ", order_id=" + order_id + ", return_date="
				+ return_date + ", is_deleted=" + is_deleted + ", deleted_on=" + deleted_on + ", order_account_status="
				+ order_account_status + ", mtype_id=" + mtype_id + ", trial_start_date=" + trial_start_date
				+ ", is_bundle=" + is_bundle + ", is_test=" + is_test + ", prev_planid=" + prev_planid
				+ ", prev_period=" + prev_period + ", prev_plan_startdt=" + prev_plan_startdt + ", prev_plan_enddt="
				+ prev_plan_enddt + ", cur_plan_startdt=" + cur_plan_startdt + ", sub_id=" + sub_id + "]";
	}


	@Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
	
	
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUser_id() {
		return user_id;
	}

	public void setUser_id(long user_id) {
		this.user_id = user_id;
	}

	public long getGateway_id() {
		return gateway_id;
	}

	public void setGateway_id(long gateway_id) {
		this.gateway_id = gateway_id;
	}

	public Timestamp getPurchase_date() {
		return purchase_date;
	}

	public void setPurchase_date(Timestamp purchase_date) {
		this.purchase_date = purchase_date;
	}

	public Timestamp getInstal_date() {
		return instal_date;
	}

	public void setInstal_date(Timestamp instal_date) {
		this.instal_date = instal_date;
	}

	public Timestamp getPaid_subscription_date() {
		return paid_subscription_date;
	}

	public void setPaid_subscription_date(Timestamp paid_subscription_date) {
		this.paid_subscription_date = paid_subscription_date;
	}

	public Timestamp getCancel_date() {
		return cancel_date;
	}

	public void setCancel_date(Timestamp cancel_date) {
		this.cancel_date = cancel_date;
	}

	public String getSub_status() {
		return sub_status;
	}

	public void setSub_status(String sub_status) {
		this.sub_status = sub_status;
	}

	public String getSales_channel() {
		return sales_channel;
	}

	public void setSales_channel(String sales_channel) {
		this.sales_channel = sales_channel;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public String getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}

	public Timestamp getUpdated_on() {
		return updated_on;
	}

	public void setUpdated_on(Timestamp updated_on) {
		this.updated_on = updated_on;
	}

	public boolean isIsgps() {
		return isgps;
	}

	public void setIsgps(boolean isgps) {
		this.isgps = isgps;
	}

	public String getCur_period() {
		return cur_period;
	}

	public void setCur_period(String cur_period) {
		this.cur_period = cur_period;
	}

	public String getFirst_plan() {
		return first_plan;
	}

	public void setFirst_plan(String first_plan) {
		this.first_plan = first_plan;
	}

	public String getFirst_period() {
		return first_period;
	}

	public void setFirst_period(String first_period) {
		this.first_period = first_period;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public Timestamp getReturn_date() {
		return return_date;
	}

	public void setReturn_date(Timestamp return_date) {
		this.return_date = return_date;
	}

	public boolean isIs_deleted() {
		return is_deleted;
	}

	public void setIs_deleted(boolean is_deleted) {
		this.is_deleted = is_deleted;
	}

	public Timestamp getDeleted_on() {
		return deleted_on;
	}

	public void setDeleted_on(Timestamp deleted_on) {
		this.deleted_on = deleted_on;
	}

	public String getOrder_account_status() {
		return order_account_status;
	}

	public void setOrder_account_status(String order_account_status) {
		this.order_account_status = order_account_status;
	}

	public long getMtype_id() {
		return mtype_id;
	}

	public void setMtype_id(long mtype_id) {
		this.mtype_id = mtype_id;
	}

	public Timestamp getTrial_start_date() {
		return trial_start_date;
	}

	public void setTrial_start_date(Timestamp trial_start_date) {
		this.trial_start_date = trial_start_date;
	}

	public boolean isIs_bundle() {
		return is_bundle;
	}

	public void setIs_bundle(boolean is_bundle) {
		this.is_bundle = is_bundle;
	}

	public boolean isIs_test() {
		return is_test;
	}

	public void setIs_test(boolean is_test) {
		this.is_test = is_test;
	}

	public String getPrev_planid() {
		return prev_planid;
	}

	public void setPrev_planid(String prev_planid) {
		this.prev_planid = prev_planid;
	}

	public String getPrev_period() {
		return prev_period;
	}

	public void setPrev_period(String prev_period) {
		this.prev_period = prev_period;
	}

	public Timestamp getPrev_plan_startdt() {
		return prev_plan_startdt;
	}

	public void setPrev_plan_startdt(Timestamp prev_plan_startdt) {
		this.prev_plan_startdt = prev_plan_startdt;
	}

	public Timestamp getPrev_plan_enddt() {
		return prev_plan_enddt;
	}

	public void setPrev_plan_enddt(Timestamp prev_plan_enddt) {
		this.prev_plan_enddt = prev_plan_enddt;
	}

	
	public Timestamp getCur_plan_startdt() {
		return cur_plan_startdt;
	}

	public void setCur_plan_startdt(Timestamp cur_plan_startdt) {
		this.cur_plan_startdt = cur_plan_startdt;
	}

	public String getSub_id() {
		return sub_id;
	}

	public void setSub_id(String sub_id) {
		this.sub_id = sub_id;
	}

	public Timestamp getCur_plan_enddt() {
		return cur_plan_enddt;
	}

	public void setCur_plan_enddt(Timestamp cur_plan_enddt) {
		this.cur_plan_enddt = cur_plan_enddt;
	}

	public Timestamp getNext_renewal_dt() {
		return next_renewal_dt;
	}

	public void setNext_renewal_dt(Timestamp next_renewal_dt) {
		this.next_renewal_dt = next_renewal_dt;
	}

	public Timestamp getCur_plan_activated_date() {
		return cur_plan_activated_date;
	}

	public void setCur_plan_activated_date(Timestamp cur_plan_activated_date) {
		this.cur_plan_activated_date = cur_plan_activated_date;
	}	
}
