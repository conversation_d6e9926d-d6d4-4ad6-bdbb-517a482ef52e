package com.nimble.webhooks.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity(name="chargebee_user_credits")
@Table(name="chargebee_user_credits",uniqueConstraints =@UniqueConstraint (columnNames={"id" } ))
public class Credits {
	
	@Id  
    @Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	private String chargebee_id;
	
	private double promotional_credits;
	
	private double refundable_credits;
		
	public Credits() {
		
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getChargebee_id() {
		return chargebee_id;
	}

	public void setChargebee_id(String chargebee_id) {
		this.chargebee_id = chargebee_id;
	}

	public double getPromotional_credits() {
		return promotional_credits;
	}

	public void setPromotional_credits(double promotional_credits) {
		this.promotional_credits = promotional_credits;
	}

	public double getRefundable_credits() {
		return refundable_credits;
	}

	public void setRefundable_credits(double refundable_credits) {
		this.refundable_credits = refundable_credits;
	}

	public Credits(Integer id, String chargebee_id, double promotional_credits, double refundable_credits) {
		super();
		this.id = id;
		this.chargebee_id = chargebee_id;
		this.promotional_credits = promotional_credits;
		this.refundable_credits = refundable_credits;
	}

	
	

}
