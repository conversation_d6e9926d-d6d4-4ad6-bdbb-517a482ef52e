package com.nimble.webhooks.entity;

public class cancelSubscription {

	long userId = 0;

	long orderId = 0;

	long gatewayId = 0;

	public cancelSubscription() {
		super();
	}

	public cancelSubscription(long userId, long orderId, long gatewayId) {
		super();
		this.userId = userId;
		this.orderId = orderId;
		this.gatewayId = gatewayId;
	}

	@Override
	public String toString() {
		return userId + "," + orderId + "," + gatewayId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getOrderId() {
		return orderId;
	}

	public void setOrderId(long orderId) {
		this.orderId = orderId;
	}

	public long getGatewayId() {
		return gatewayId;
	}

	public void setGatewayId(long gatewayId) {
		this.gatewayId = gatewayId;
	}

}
