package com.nimble.webhooks.service;

import java.util.ArrayList;

import org.springframework.dao.DataIntegrityViolationException;

import com.nimble.webhooks.entity.CBShopifyOrders;
import com.nimble.webhooks.entity.ExternalConfig;



public interface IExternalConfigService {


	public ExternalConfig getExternalConfig(String name) throws DataIntegrityViolationException;

	public ArrayList<CBShopifyOrders> getCBShopifyOrderInfo();

	
}
