package com.nimble.webhooks.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IErrorDao;
import com.nimble.webhooks.entity.ErrorInfo;
import com.nimble.webhooks.service.IErrorService;

@Service
@Transactional
public class ErrorServiceImpl implements IErrorService{


	@Autowired
	IErrorDao errorDao;

	
	@Override
	public ArrayList<ErrorInfo> getAllErrorInfo() {
		return errorDao.getErrorInfo();
	}
	
	@Override
	public ErrorInfo updateErrorInfo(ErrorInfo errorinfo) {
		return errorDao.updateErrorInfo(errorinfo);
	}

	@Override
	public boolean AckErrorinfo(ErrorInfo errInfo) {
		return errorDao.AckErrorinfo(errInfo);
	}

}
