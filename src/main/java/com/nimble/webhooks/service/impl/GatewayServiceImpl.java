package com.nimble.webhooks.service.impl;

import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IGatewayDao;
import com.nimble.webhooks.dto.GatewayInfo;
import com.nimble.webhooks.service.IGatewayService;

@Service
@Transactional
public class GatewayServiceImpl implements IGatewayService {
	
	@Autowired
	IGatewayDao gatewayDao;

	@Override
	public long getgatewayByMonitorId(long userid, long monitortype_id) {
		return gatewayDao.getgatewayByMonitorId(userid, monitortype_id);
	}

	@Override
	public HashMap<String, String> getGatewayDetail(long gateway_id) {
		return gatewayDao.getGatewayDetail(gateway_id);
	}

	@Override
	public long getMonitorType(String cbPlanid) {
		return gatewayDao.getMonitorType(cbPlanid);
	}

	@Override
	public long getgatewayById(long gateway_id) {
		return gatewayDao.getgatewayById(gateway_id);
	}

	@Override
	public ArrayList<Long> getGatewayIdsByUserId(long user_id) {
		return gatewayDao.getGatewayIdsByUserId(user_id);
	}

	@Override
	public ArrayList<GatewayInfo> getGatewayInfo(String gatewayIds) {
		return gatewayDao.getGatewayInfo(gatewayIds);
	}
	
	@Override
	public ArrayList<Long> getPlanmappedGateway(String chargebeeId, String subscription_id) {
		return gatewayDao.getPlanmappedGateway(chargebeeId, subscription_id);
	}

	@Override
	public ArrayList<GatewayInfo> getUserGatewaySubInfo(String userId, boolean isUserBased) {
		return gatewayDao.getUserGatewaySubInfo(userId, isUserBased);
	}
	
	@Override
	public int getPlanbaseddevicecnt(String planId) {
		return gatewayDao.getPlanbaseddevicecnt(planId);
	}

}
