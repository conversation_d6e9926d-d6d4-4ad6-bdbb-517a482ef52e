package com.nimble.webhooks.service.impl;

import com.nimble.webhooks.dao.ISubscriptionMrrDao;
import com.nimble.webhooks.service.ISubscriptionMrrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SubscriptionMrrServiceImpl implements ISubscriptionMrrService {

    @Autowired
    private ISubscriptionMrrDao subscriptionMrrDao;

    @Transactional
    @Override
    public boolean updateMRR(String subId, long mrr) {

        return subscriptionMrrDao.updateMRR(subId, mrr);
    }
}
