package com.nimble.webhooks.service.impl;

import com.nimble.webhooks.dao.ISubscriptionMrrDao;
import com.nimble.webhooks.service.ISubscriptionMrrService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Enhanced MRR Service with concurrency control and idempotency
 */
@Service("enhancedSubscriptionMrrService")
public class EnhancedSubscriptionMrrServiceImpl implements ISubscriptionMrrService {

    private static final Logger log = LogManager.getLogger(EnhancedSubscriptionMrrServiceImpl.class);

    @Autowired
    private ISubscriptionMrrDao subscriptionMrrDao;

    // In-memory locks for subscription IDs to prevent concurrent processing
    private final ConcurrentHashMap<String, ReentrantLock> subscriptionLocks = new ConcurrentHashMap<>();
    
    // Cache to track recent MRR updates for idempotency (simple implementation)
    private final ConcurrentHashMap<String, MrrUpdateRecord> recentUpdates = new ConcurrentHashMap<>();
    
    private static class MrrUpdateRecord {
        final long mrr;
        final long timestamp;
        
        MrrUpdateRecord(long mrr) {
            this.mrr = mrr;
            this.timestamp = System.currentTimeMillis();
        }
        
        boolean isRecent() {
            return (System.currentTimeMillis() - timestamp) < 30000; // 30 seconds
        }
    }

    @Override
    @Transactional(
        isolation = Isolation.READ_COMMITTED,
        propagation = Propagation.REQUIRED,
        rollbackFor = Exception.class
    )
    public boolean updateMRR(String subId, long mrr) {

        log.info("Entered updateMRR :: subId: {}, mrr: {}", subId, mrr);
        if (subId == null || subId.trim().isEmpty()) {
            log.error("Invalid subscription ID: {}", subId);
            return false;
        }

        String normalizedSubId = subId.trim();
        
        // Get or create a lock for this subscription ID
        ReentrantLock lock = subscriptionLocks.computeIfAbsent(normalizedSubId, k -> new ReentrantLock());
        
        try {
            // Acquire lock to prevent concurrent updates for the same subscription
            lock.lock();
            log.info("Acquired lock for subscription: {}", normalizedSubId);
            
            // Check for recent duplicate updates (idempotency)
            MrrUpdateRecord recent = recentUpdates.get(normalizedSubId);
            if (recent != null && recent.isRecent() && recent.mrr == mrr) {
                log.info("Duplicate MRR update detected for subId: {}, mrr: {} - skipping", normalizedSubId, mrr);
                return true; // Return true as it's already processed
            }
            
            // Perform the actual update
            boolean result = performMrrUpdate(normalizedSubId, mrr);
            
            if (result) {
                // Cache this update for idempotency checking
                recentUpdates.put(normalizedSubId, new MrrUpdateRecord(mrr));
                log.info("MRR update successful and cached: subId={}, mrr={}", normalizedSubId, mrr);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Error in updateMRR with concurrency control: subId={}, mrr={}, error={}", 
                     normalizedSubId, mrr, e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        } finally {
            lock.unlock();
            log.debug("Released lock for subscription: {}", normalizedSubId);
            
            // Clean up old locks and cache entries periodically
            cleanupOldEntries();
        }
    }
    
    private boolean performMrrUpdate(String subId, long mrr) {
        try {
            return subscriptionMrrDao.updateMRR(subId, mrr);
        } catch (DataAccessException e) {
            log.error("Database error during MRR update: subId={}, mrr={}, error={}", 
                     subId, mrr, e.getMessage(), e);
            return false;
        }
    }
    
    private void cleanupOldEntries() {
        // Clean up every 100th call (simple approach)
        if (Math.random() < 0.01) {
            log.debug("Cleaning up old cache entries and locks");
            
            // Remove old cache entries
            recentUpdates.entrySet().removeIf(entry -> !entry.getValue().isRecent());
            
            // Remove unused locks (locks that are not currently held)
            subscriptionLocks.entrySet().removeIf(entry -> 
                !entry.getValue().isLocked() && !recentUpdates.containsKey(entry.getKey())
            );
        }
    }
}
