package com.nimble.webhooks.service.impl;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IExternalConfigDao;
import com.nimble.webhooks.entity.CBShopifyOrders;
import com.nimble.webhooks.entity.ExternalConfig;
import com.nimble.webhooks.service.IExternalConfigService;

@Service
public class ExternalConfigServiceImpl implements IExternalConfigService {

	@Autowired
	IExternalConfigDao extenalConfigDao;

	@Override
	@Transactional
	public ExternalConfig getExternalConfig(String name) throws DataIntegrityViolationException {
		return extenalConfigDao.getExternalConfig(name);
	}

	@Override
	@Transactional
	public ArrayList<CBShopifyOrders> getCBShopifyOrderInfo() {
		return extenalConfigDao.getCBShopifyOrderInfo();
	}

}
