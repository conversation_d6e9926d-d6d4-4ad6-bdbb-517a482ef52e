package com.nimble.webhooks.service.impl;

import java.sql.Timestamp;
import java.util.HashMap;

import javax.transaction.Transactional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.filters.enums.SortOrder;
import com.chargebee.models.PaymentSource;
import com.chargebee.models.Subscription;
import com.chargebee.models.Subscription.Status;
import com.chargebee.models.Subscription.SubscriptionListRequest;
import com.nimble.webhooks.dao.IRechargeDao;
import com.nimble.webhooks.dto.JKcalDetails;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.LatestRechargeSubscription;
import com.nimble.webhooks.entity.ReCBWebhookStatus;
import com.nimble.webhooks.entity.RechargeSubscription;
import com.nimble.webhooks.service.IRechargeService;
import com.nimble.webhooks.service.IUserService;

@Service
@Transactional
public class RechargeServiceImpl implements IRechargeService {
	private static final Logger log = LogManager.getLogger(RechargeServiceImpl.class);

	@Autowired
	IRechargeDao reDao;
	
	@Autowired
	IUserService userService;

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;
	
	@Override
	public boolean saveRechargeSubscription(RechargeSubscription reSub) {
		return reDao.saveRechargeSubscription(reSub);
	}

	@Override
	public boolean saveLatestRechargeSubscription(LatestRechargeSubscription reSub) {
		return reDao.saveLatestRechargeSubscription(reSub);
	}
	
	@Override
	public LatestRechargeSubscription getRechargeSubDetailsBySubID(String subid,String reCustomerId) {
		return reDao.getRechargeSubDetailsBySubID(subid, reCustomerId);
	}
	@Override
	public String getCBSubId(String re_subid) {
		return reDao.getCBSubId(re_subid);
	}

	@Override
	public HashMap<String,String> getReSubscriptionPlans(){
		return reDao.getReSubscriptionPlans();
	}
	
	@Override
	public boolean cancelRechargeSubscription(RechargeSubscription reSub) {
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			UserV4 user = userService.verifyAuthV4("recharge_custid", reSub.getCustomer_id());
			if (user != null) {

				if (!user.getChargebeeid().equalsIgnoreCase("NA")) {
					HashMap<String,String> planList = getReSubscriptionPlans();
					
					SubscriptionListRequest subList = com.chargebee.models.Subscription.list().customerId()
							.is(user.getChargebeeid()).status()
							.in(Status.ACTIVE, Status.NON_RENEWING, Status.IN_TRIAL);
					String planIdList ="";
					for(String plan : planList.keySet())
						planIdList = planIdList+plan+",";
//					
//					if(!planIdList.isEmpty()) {
//						planIdList = planIdList.substring(0,planIdList.length()-1);
//						subList = subList.planId().in(planIdList);
//					}
					
					subList = subList.sortByUpdatedAt(SortOrder.DESC);
					ListResult result = subList.request();

					if (!result.isEmpty()) {
						String cb_subid = "NA";
						
						for (ListResult.Entry subs : result) {
							PaymentSource ps = subs.paymentSource();
							if (ps == null && planIdList.contains(subs.subscription().planId())) {
								cb_subid = subs.subscription().id();
								Result res1 = Subscription.update(subs.subscription().id()).replaceCouponList(true).request();
								res1 = Subscription.cancel(subs.subscription().id()).request();
								//res1 = Customer.update(user.getChargebeeid()).autoCollection(AutoCollection.ON).request();
							}
						}
						//
						//int stat2 = updateReSubHistory(subId);
						LatestRechargeSubscription lrs = getRechargeSubDetailsBySubID(reSub.getSub_id(), reSub.getCustomer_id());
						if(lrs != null) {
							lrs.setCancelled_at(reSub.getCancelled_at());
							lrs.setUpdated_at(reSub.getUpdated_at());
							lrs.setStatus(reSub.getStatus());
							lrs.setCb_sub_status("Sub_Cancelled");
							lrs.setEvent_type(reSub.getEvent_type());
							boolean stat2 = saveLatestRechargeSubscription(lrs);
							log.info("cancelRechargeSubscription: updateReSubHistory:"+stat2);

						}					

						// update user with re_custid as NA
						String qry = "update user set recharge_custid='NA' where id="+ user.getId() + " ;";
						int stat2 = userService.executeQuery(qry);
						log.info("cancelRechargeSubscription: user update:"+stat2);
						
						// cancelled customers are inserted
						stat2 = saveReCancelCustomer(user.getId(), reSub.getCustomer_id());
						log.info("cancelRechargeSubscription: saveReCancelCustomer:"+stat2);
						
						//cancel event save in cb webhooks table
						ReCBWebhookStatus reCBSub = new ReCBWebhookStatus(user.getChargebeeid(), cb_subid, reSub.getSub_id(), "Sub_cancelled");
						boolean stat = saveRechargeCBSubStatus(reCBSub);
						log.info("saveRechargeCBSubStatus: "+stat);
					}
					else {
						// cancel subscription event before activate in CB
						LatestRechargeSubscription lrs = getRechargeSubDetailsBySubID(reSub.getSub_id(), reSub.getCustomer_id());
						if(lrs != null) {
							lrs.setCancelled_at(reSub.getCancelled_at());
							lrs.setUpdated_at(reSub.getUpdated_at());
							lrs.setStatus(reSub.getStatus());
							lrs.setCb_sub_status("NA");
							lrs.setEnable(false);
							lrs.setEvent_type(reSub.getEvent_type());
							boolean stat2 = saveLatestRechargeSubscription(lrs);
							log.info("cancelRechargeSubscription: updateReSubHistory:"+stat2);

						}
					}
				}
			}
		}catch (Exception e) {
			log.error("cancelRechargeSubscription: Excep:"+e.getLocalizedMessage());
			return false;
		}
		return true;
	}

	@Override
	public void updateCBSubsRenewalDt(String reSubId,String nxtRenewDt,String price) {
		Environment.configure(chargebeeSiteName, chargebeeSiteKey);
		try {
			Timestamp termEndsAt = Timestamp.valueOf(nxtRenewDt);
			System.out.println(nxtRenewDt);
			String cbsubId = getCBSubId(reSubId);
			
			if(!cbsubId.equalsIgnoreCase("NA"))
			{
				float priceCent = (Float.parseFloat(price))*100;
				log.info("priceCent:"+(int)priceCent);
				
				Result res1 = Subscription.update(cbsubId).planUnitPrice((int)priceCent).request();
				
				Result res2 = Subscription.changeTermEnd(cbsubId).termEndsAt(termEndsAt).request();
			}else {
				log.info("updateCBSubsRenewalDt: cbsubId:"+cbsubId);
			}

		}catch (Exception e) {
			log.error("updateCBSubsRenewalDt: "+e.getLocalizedMessage());
			//e.printStackTrace();
		}	

	}

	
	@Override
	public int saveReCancelCustomer(long userid, String re_cust_id) {
		return reDao.saveReCancelCustomer(userid, re_cust_id);
	}

	@Override
	public int updateReSubHistory(String re_sub_id) {
		return reDao.updateReSubHistory(re_sub_id);
	}

	@Override
	public LatestRechargeSubscription getRechargeSubDetailsByCusID(String reCustomerId) {
		return reDao.getRechargeSubDetailsByCusID(reCustomerId);
	}
	
	@Override
	public boolean saveRechargeCBSubStatus(ReCBWebhookStatus reSub) {
		return reDao.saveRechargeCBSubStatus(reSub);
	}
	
	@Override
	public boolean saveSmartBowlUserDetails(JKcalDetails jdetails) {
		return reDao.saveSmartBowlUserDetails(jdetails);
	}
}
