package com.nimble.webhooks.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.chargebee.Result;
import com.chargebee.models.Subscription;
import com.chargebee.models.enums.CreditOptionForCurrentTermCharges;
import com.nimble.webhooks.service.IErrorService;
import com.nimble.webhooks.util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IPlanDao;
import com.nimble.webhooks.dto.GatewayInfo;
import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.entity.ErrorInfo;
import com.nimble.webhooks.entity.GatewayFeature;
import com.nimble.webhooks.entity.PlanToMonitorType;
import com.nimble.webhooks.entity.PlantoPeriod;
import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.IGatewayService;
import com.nimble.webhooks.service.IPlanService;
import com.nimble.webhooks.service.IUserService;
import com.nimble.webhooks.util.IrisUtil;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import org.springframework.web.client.RestTemplate;

@Service
@Transactional
public class PlanServiceImpl implements IPlanService {

    private static final Logger log = LogManager.getLogger(PlanServiceImpl.class);

    @Autowired
    IChargebeeWebhookService chargebeeWebhookService;

    @Autowired
    SecretManagerService secretManagerService;

    @Value("${aws_sqs_secret_name}")
    private String SQS_SECRET_NAME;

    @Autowired
    IGatewayService gatewayService;

    @Autowired
    IUserService userService;

    @Autowired
    @Lazy
    IPlanService planService;

    @Autowired
    IPlanDao planDao;

    @Autowired
    private IErrorService errorService;

	@Value("${iris.services.amazonSQS.microserviceQueue.url}")
	private String amazonSQS_microserviceQueue_url;

	@Value("${microservice_url}")
	private String microservice_url;

	@Value("${microservice_api_call}")
	private boolean microserviceApiCall;

    @Value("${vetchat.cancellation_url}")
    private String vetchatCancellationUrl;

    @Value("${vetchat.username}")
    private String vetchatUsername;

    @Value("${vetchat.password}")
    private String vetchatPassword;

    @Autowired
    Helper _helper;
    
    @Autowired
    private IAsyncService async;

    @Override
    public JResponse assignComboPlan(UserV4 user, long gateway_id, String chargebee_planid,String subscription_id) {
        log.info("Entered into assignComboPlan :: user_id : "+user.getId()+" :: gateway_id : "+gateway_id+" :: chargebee_planid : "+chargebee_planid);
        JResponse response = new JResponse();
        try {

            PlantoPeriod planToPeriod = chargebeeWebhookService.getPlanAndPeriodId(chargebee_planid);
            if( planToPeriod == null ) {
                log.info("plan_to_period not found :: plan_name : "+chargebee_planid);
                response.put("Status", 0);
                response.put("Msg", "plan_to_period not found :: plan_name : "+chargebee_planid);
                return response;
            }
            PlanToMonitorType planToMonitorType = chargebeeWebhookService.getPlanToMonitorType( planToPeriod.getPlan_id() );
            if( planToMonitorType == null ) {
                log.info("plan_to_monitortype not found :: plan_id : "+planToPeriod.getPlan_id());
                response.put("Status", 0);
                response.put("Msg", "plan_to_monitortype not found :: plan_id : "+planToPeriod.getPlan_id());
                return response;
            }

            //Hashmap have key: monitor_type_id value: index no of monitorCountList ArrayList
            //Getting monitor type from plan_to_monitortype table column monitortype_id
            HashMap<Long, Integer> monitorTypeMap = new HashMap<>();

            //HashMap have available monitor count
            //Getting monitor count from plan_to_monitortype table column device_config
            HashMap<Integer, Integer> monitorCountMap = new HashMap<>();

            // Calculate and assign monitor type count from plan_to_monitortype table
            if( planToMonitorType != null  ) {
                int itr = 0;
                for( String str : planToMonitorType.getDevice_config().split("/")) {
                    monitorCountMap.put( itr++, Integer.parseInt(str) );
                }

                int index = 0;
                String[] monitorType = planToMonitorType.getMonitortype_id().split("/");
                for (String type : monitorType) {
                    if(type.contains(",")) {
                        String[] monitorTypeNested = type.split(",");
                        for (String typeNested : monitorTypeNested) {
                            monitorTypeMap.put( Long.parseLong( typeNested ) , index);
                        }
                    } else {
                        monitorTypeMap.put( Long.parseLong( type ) , index);
                    }
                    index++;
                }
            }

            // Getting gateway ids from user_id from usergateway table
            ArrayList<Long> gatewayIdList = gatewayService.getGatewayIdsByUserId( user.getId() );
            
            
/*
				When user already has device based plan with one active sub device, again when he purchases combo plan,
				existing plan should be cancelled based on prorate and credits will be applied.
*/
            Map<Integer, List<Long>> groupedDevCnt = monitorTypeMap.entrySet().stream().collect(Collectors.groupingBy(Map.Entry::getValue,                                // group by the value (Integer)
                Collectors.mapping(Map.Entry::getKey, Collectors.toList()) // collect the keys (Long)
            ));

            //Device based subscription
            updateCancelSubForCombo(user, false,  groupedDevCnt, monitorCountMap, subscription_id );

            //User based subscription
            updateCancelSubForCombo(user, true,  groupedDevCnt, monitorCountMap, subscription_id );
            
            ArrayList<Long> planGatewayList = gatewayService.getPlanmappedGateway( user.getChargebeeid(),subscription_id );

            // Getting gateway monitor_type_id from joining gateway and assertmodel table
            String gatewayIds = gatewayIdList.stream().filter(id -> !planGatewayList.contains(id)).map(String::valueOf).collect(Collectors.joining(","));

            ArrayList<GatewayInfo> gatewayInfoList = gatewayService.getGatewayInfo( gatewayIds );

            // Getting already assigned gateway_feature (group by gateway_id) for that user
            ArrayList<GatewayFeature> gatewayFeatureList  = planService.getGatewayFeatureByGatewayIds( gatewayIds );

            // creating gateway_feature hashmap for easy getting access from gateway_id (key: gateway_id value:GatewayFeature)
            Map<Long, GatewayFeature> gatewayFeatureMap = gatewayFeatureList.stream().collect(Collectors.toMap(GatewayFeature::getGateway_id, Function.identity()));

            // calling this function for calculating available monitor count from plan_to_period table
            checkAlreadyFeatureAssignedMonitor( monitorTypeMap, monitorCountMap, gatewayInfoList, gatewayFeatureMap, planToPeriod.getPlan_id() );

            AllProductSubscription subscription = chargebeeWebhookService.getAllProductSubscriptionBySubId(subscription_id);
            
            boolean oldSubAvail = false;
			List<AllSubscription> subscriptionAvail = chargebeeWebhookService.getSubscriptionByChargebeeId(user.getChargebeeid());
			if (subscriptionAvail != null) {
				oldSubAvail = true;
			}

            //if gateway_id is 0 we check all the gateway that user have and assign gateway_feature
            //else we have gateway_id we check and assign that purticular gateway_id only
            if( gateway_id == 0 ) {

                // creating this gatewayInfoListForRequest for calling TXN service for valid gateway to assign gateway_feature
                ArrayList<Long> gatewayInfoListForRequest = new ArrayList<>();

                for (GatewayInfo gatewayInfo : gatewayInfoList) {

                        //check monitor type available on the current plan by plan_to_monitortype table(monitortype_id)
                        if(monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ) != null) {
                            //if monitor type is available on the current plan,
                            //we check the available monitor count for the current plan from plan_to_monitortype table(device_config)
                            int count = monitorCountMap.get( monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ) );
                            if(count <= 0) {
                                log.info("gateway_id : "+ gatewayInfo.getGateway_id() +" :: monitor_type_id : "+ gatewayInfo.getMonitor_type_id() +" already have feature in gateway_feature");
                                continue;
                            } else {
                                // we reduce available monitor count
                                monitorCountMap.put( monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ), count-1 );

                                //if monitoe_type_id 1 we call TXN service api to create gateway_feature and re-scheduling
                                //else we added in gatewayInfoListForRequest to call TXN at the end
                                if( gatewayInfo.getMonitor_type_id() == 1 && !oldSubAvail) {
                                    callGatewayFeatureForPetMonitor( user, chargebee_planid, gatewayInfo.getGateway_id() , subscription_id);
                                    saveAllChargebeeSubs(gatewayInfo.getGateway_id(), gatewayInfo.getMonitor_type_id(), subscription);
                                } else if( gatewayInfo.getMonitor_type_id() != 1){
                                    gatewayInfoListForRequest.add(gatewayInfo.getGateway_id());
                                    saveAllChargebeeSubs(gatewayInfo.getGateway_id(), gatewayInfo.getMonitor_type_id(), subscription);
                                }
                            }
                        }
                }
                if( !gatewayInfoListForRequest.isEmpty() ) {
                    callGatewayFeatureForNonPetMonitor(user, planToPeriod ,chargebee_planid, gatewayInfoListForRequest, subscription_id);
                }

                if( monitorTypeMap.containsKey(11L) ) {
                	if(subscription.getMonitor_type() == 0) {
                		chargebeeWebhookService.updateComboPlanMonitorType(subscription);
                	}else {
                		 saveAllChargebeeSubs(0, 11, subscription);
                	}
                }

            } else {

                if( !gatewayInfoList.isEmpty() ) {
                    int index = 0;
                    for (GatewayInfo gatewayInfo : gatewayInfoList) {
                        if( gateway_id == gatewayInfo.getGateway_id() )
                            break;
                        index++;
                    }
                        //check monitor type available on the current plan by plan_to_monitortype table(monitortype_id)
                        if(monitorTypeMap.get( gatewayInfoList.get(0).getMonitor_type_id() ) != null) {

                            //if monitor type is available on the current plan,
                            //we check the available monitor count for the current plan from plan_to_monitortype table(device_config)
                            int count = monitorCountMap.get( monitorTypeMap.get( gatewayInfoList.get(index).getMonitor_type_id() ) );
                            if(count <= 0) {
                                log.info("No count left for this monitor type to assign feature. for this plan");
                            } else {
                                // we reduce available monitor count
                                monitorCountMap.put( monitorTypeMap.get( gatewayInfoList.get(index).getMonitor_type_id() ), count-1 );

                                //if monitoe_type_id 1 we call TXN service api to create gateway_feature and re-scheduling
                                //else we call TXN service api to create gateway_feature
                                if( gatewayInfoList.get(index).getMonitor_type_id() == 1  && !oldSubAvail) {
                                    saveAllChargebeeSubs(gatewayInfoList.get(index).getGateway_id(), gatewayInfoList.get(index).getMonitor_type_id(), subscription);
                                    callGatewayFeatureForPetMonitor(user, chargebee_planid, gateway_id, subscription_id);
                                } else if( gatewayInfoList.get(index).getMonitor_type_id() != 1){
                                    saveAllChargebeeSubs(gatewayInfoList.get(index).getGateway_id(), gatewayInfoList.get(index).getMonitor_type_id(), subscription);
                                    ArrayList<Long> gatewayArrList = new ArrayList<>();
                                    gatewayArrList.add( gateway_id );
                                    callGatewayFeatureForNonPetMonitor(user, planToPeriod ,chargebee_planid, gatewayArrList, subscription_id);
                                }
                            }
                        }
                }
            }

            response.put("Status", 1);
            response.put("Msg", "Success");

        } catch (Exception e) {
            log.error("Error in assignComboPlan :: Error : "+e.getLocalizedMessage());
            response.put("Status", 0);
            response.put("Msg", e.getMessage());
        }
        return response;
    }

    public boolean updateCancelSubForCombo(UserV4 user, boolean isAllCbSub, Map<Integer, List<Long>> groupedDevCnt, HashMap<Integer, Integer> monitorCountMap, String subscription_id) {
        log.info("Entered updateCancelSubForCombo : ");
        try {
            ArrayList<GatewayInfo> gateway_id_list = gatewayService.getUserGatewaySubInfo(user.getId() + "", isAllCbSub);
            Map<Long, List<GatewayInfo>> mtypeBasedGid = gateway_id_list.stream().collect(Collectors.groupingBy(GatewayInfo::getMonitor_type_id));

            for (Map.Entry<Integer, List<Long>> entry : groupedDevCnt.entrySet()) {
                int combo_device_cfg_cnt = 0, actual_user_based_gateway_cnt = 0, plan_based_device_cfg_cnt = 0;
                int idx_key = entry.getKey();
                combo_device_cfg_cnt = monitorCountMap.get(idx_key);
                List<Long> mtype_list = entry.getValue();

				for (Long mId : mtype_list) {
					if (mtypeBasedGid.get(mId) != null)
						actual_user_based_gateway_cnt += mtypeBasedGid.get(mId).size();
				}
				
				plan_based_device_cfg_cnt = gatewayService.getPlanbaseddevicecnt(gateway_id_list.get(0).getPlan_id());
				
                if (combo_device_cfg_cnt > 0 && actual_user_based_gateway_cnt > 0 && (combo_device_cfg_cnt == actual_user_based_gateway_cnt || actual_user_based_gateway_cnt < combo_device_cfg_cnt)) {
					for (Long mId : mtype_list) {
						if (mtypeBasedGid.get(mId) != null) {
							List<GatewayInfo> gt = mtypeBasedGid.get(mId);
							for (GatewayInfo gatewaySub : gt) {
								if (gatewaySub.getSub_status().equalsIgnoreCase("active")
										&& !gatewaySub.getSub_id().equalsIgnoreCase(subscription_id)) {
									cancelSubInCBAndUpdateStatus(gatewaySub.getSub_id(), isAllCbSub);
								}
							}
						}
                    }
                } else if(combo_device_cfg_cnt > 0 && actual_user_based_gateway_cnt > 0 && actual_user_based_gateway_cnt > combo_device_cfg_cnt) {
                    List<GatewayInfo> otherStatusList = gateway_id_list.stream().filter(g -> !g.getSub_status().equalsIgnoreCase("active")).collect(Collectors.toList());
                    List<GatewayInfo> activeSubList = gateway_id_list.stream().filter(g -> g.getSub_status().equalsIgnoreCase("active")).collect(Collectors.toList());

                    if(!isAllCbSub && !otherStatusList.isEmpty() && otherStatusList.size() >= combo_device_cfg_cnt) {
                        //No need to cancel
                    } 
                    else if(!isAllCbSub && ((!otherStatusList.isEmpty() && otherStatusList.size() < combo_device_cfg_cnt) || otherStatusList.isEmpty())) {
                        int no_of_subs_to_cancel = combo_device_cfg_cnt - otherStatusList.size();

                        for(int i=0; i<activeSubList.size() && i<no_of_subs_to_cancel && !activeSubList.isEmpty(); i++){
                            if (!activeSubList.get(i).getSub_id().equalsIgnoreCase(subscription_id))
                                cancelSubInCBAndUpdateStatus(activeSubList.get(i).getSub_id(), isAllCbSub);
                        }
                    }
                    else if(isAllCbSub && (!activeSubList.isEmpty() && actual_user_based_gateway_cnt > combo_device_cfg_cnt &&  actual_user_based_gateway_cnt > plan_based_device_cfg_cnt)) {
                   	 int no_of_subs_to_cancel = combo_device_cfg_cnt - otherStatusList.size();

                        for(int i=0; i<activeSubList.size() && i<no_of_subs_to_cancel && !activeSubList.isEmpty(); i++){
                            if (!activeSubList.get(i).getSub_id().equalsIgnoreCase(subscription_id))
                                cancelSubInCBAndUpdateStatus(activeSubList.get(i).getSub_id(), isAllCbSub);
                        }
                   }
                }
            }
        } catch (Exception e) {
            log.error("Exception in updateCancelSubForCombo : " + e.getLocalizedMessage());
        }
        return false;
    }

    private void cancelSubInCBAndUpdateStatus(String subId, boolean isAllCbSub) {
        log.info("Entered cancelSubInCBAndUpdateStatus");
        try {
            Result res = Subscription.cancel(subId).endOfTerm(false)
                             .creditOptionForCurrentTermCharges(CreditOptionForCurrentTermCharges.PRORATE).request();
            Subscription cancelSubscription = res.subscription();
            String subStatus = cancelSubscription.status().toString();

            if (subStatus.equalsIgnoreCase("cancelled")) {
                boolean updateStatus = chargebeeWebhookService.updateSubStatusinAllProductSub(subId, isAllCbSub);
                log.info("cancelSubInCBAndUpdateStatus Status : " + updateStatus);
                
                Thread.sleep(8000);
            }
        } catch (Exception e) {
            log.error("Error in cancelSubInCBAndUpdateStatus :: Error : "+e.getMessage());
        }
    }

    @Override
    public void saveAllChargebeeSubs(long gatewayId, long monitorTypeId, AllProductSubscription subscription) {
        log.info("Entered into saveAllChargebeeSubs :: gateway_id : "+gatewayId+" :: subscription_id : "+subscription.getSubscriptionId());
        try {
            if( subscription == null ) {
                log.info("subscription not found");
                return;
            }

            AllProductSubscription allSubscription = new AllProductSubscription(subscription);
            String curTime = new Helper().getCurrentTimeinUTC();
            allSubscription.setUpdatedIndb(curTime);
            allSubscription.setId(0);
            allSubscription.setGateway_id( gatewayId );
            allSubscription.setMonitor_type( monitorTypeId );

            chargebeeWebhookService.insertAllProductSubscriptionSQL(allSubscription);

        } catch (Exception e) {
            log.error("Error in saveAllChargebeeSubs :: Error : "+e.getMessage());
        }
    }

    private void checkAlreadyFeatureAssignedMonitor(HashMap<Long, Integer> monitorTypeMap, HashMap<Integer, Integer> monitorCountMap, ArrayList<GatewayInfo> gatewayInfoList, Map<Long, GatewayFeature> gatewayFeatureMap, long plan_id) {
        for (GatewayInfo gatewayInfo : gatewayInfoList) {

            if( gatewayFeatureMap.getOrDefault( gatewayInfo.getGateway_id() , new GatewayFeature() ).getPlan_id() == plan_id ) {
                if( monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ) != null ){
//                    int availableMonitorCount = monitorCountMap.get(monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ) );
//                    monitorCountMap.put( monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ), availableMonitorCount - 1 );
                    monitorCountMap.compute( monitorTypeMap.get( gatewayInfo.getMonitor_type_id() ), (k, availableMonitorCount) -> availableMonitorCount - 1);
                }
            }

        }

    }

    @Override
    public ArrayList<GatewayFeature> getGatewayFeatureByGatewayIds(String gatewayIds) {
        return planDao.getGatewayFeatureByGatewayIds(gatewayIds);
    }


    public void callGatewayFeatureForPetMonitor(UserV4 user, String chargebee_planid, long gatewayId, String subscription_id) {
        String msurl = microservice_url + "/v4.0/gatewayfeaturewithmonitor?" + "cbplan=" + chargebee_planid + "&chargbeeid="
                + user.getChargebeeid() + "&eventtype=subscription_created" + "&isupgrade=false" + "&cbemail="
                + user.getEmail() + "&gatewayid="+gatewayId+"&subId="+subscription_id+"&is_combo_plan=true";
        log.info("Call txnservice gatewayfeature API :" + msurl);
        String msPlanRes = _helper.httpPOSTRequest(msurl, null);
        log.info("Plan Url update : " + msPlanRes);
        try {
        	simActivation(user.getChargebeeid(),user.getEmail(),gatewayId);
        	log.info("Combo Sim activation  :" + user.getChargebeeid() +" :: "+user.getEmail());
        } catch (Exception e) {
			log.error("Error in sim activation  :" + e.getLocalizedMessage());
			async.asynSlackMessage("Error in sim activation combo plan", e.getLocalizedMessage());
		}
    }

    public void callGatewayFeatureForNonPetMonitor(UserV4 user, PlantoPeriod planToPeriod, String chargebee_planid, ArrayList<Long> gatewayList, String subscription_id) {
        String msurl = microservice_url + "/v5.0/gatewayfeature";
        log.info("Call txnservice gatewayfeature API :" + msurl);

        Map<String, Object> postParam = new HashMap<>();
        postParam.put("planid", planToPeriod.getPlan_id());
        postParam.put("cbsubid", subscription_id); // to do
        postParam.put("chargebeeid", user.getChargebeeid());
        postParam.put("periodid", planToPeriod.getSub_period_id());
        postParam.put("gatewayid", gatewayList);
        postParam.put("is_combo_plan", true);

        JSONObject bodyJson = new JSONObject(postParam);

        // Response from Txn_Service
        String msPlanRes = _helper.httpPOSTRequest(msurl, bodyJson.toString());
        log.info("Plan Url update : " + msPlanRes);
        String Code = msPlanRes;
    }

    @Override
    public boolean isVetChatPlan(String planId) {

        return planDao.isVetChatPlan(planId);
    }
    
    private String simActivation(String cbId,String cbEmail,long gatewayId) {
		String mailPacket = "";
		ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
		// for error Info
		Boolean checkFun = true;
		String errorDescription = "";
		try {
			String event_type = "subscription_created";
			String event_id = cbId+"_evcombo";

			try {

				if (microserviceApiCall) {
					whStatus.setEventProcess("activate verizon sim");
					whStatus.setEventStatus("calling micro service for activate sim ");
					whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
					String urlParams = "";
					String msurl = microservice_url + "/v3.0/activateverizonbygateway/?cbid=" + cbId + "&cbemail=" + cbEmail
							+ "&eventid=" + event_id + "&eventname=" + event_type + "&gatewayid="+gatewayId;
					log.info("Combo activateverizon :" + msurl);

					// Response from Txn_Service
					String microServiceRes = _helper.httpPOSTRequest(msurl, urlParams);

					JSONObject microServiceResJson = new JSONObject(microServiceRes);

					microServiceResJson = microServiceResJson.getJSONObject("response");

					int status = microServiceResJson.getInt("Status");
					// String msg = microServiceResJson.getString("Msg");

					if (status > 0) {
						whStatus.setStatus(true);
						whStatus.setActivateSimStatus(1);
						whStatus.setEventStatus("SIM activation successful ");
					} else {
						whStatus.setActivateSimStatus(0);
						whStatus.setEventStatus("SIM activation unsuccessful  ");
						checkFun = false;
						errorDescription += "SIM activation unsuccessful ";
					}

				} else {
					whStatus.setEventStatus("calling amazon SQS for activate sim ");
					whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

                    String amazonSQSAccessKey = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
                    String amazonSQSSecretKey = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
					AWSCredentials credentials = new BasicAWSCredentials(amazonSQSAccessKey, amazonSQSSecretKey);
					AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
					AmazonSQS sqs = AmazonSQSClientBuilder.standard().withCredentials(credentialsProvider)
							.withRegion(Regions.US_WEST_2).build();

					mailPacket = "nimble|waggle|chargebeewebhooks|" + event_type + "|" + event_id + "|" + cbId + "|"
							+ cbEmail + "#";

					log.info("Combo sendEmailDataToQueue - Packet ::: " + mailPacket);

					SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSQS_microserviceQueue_url,
							mailPacket);
					SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
					String sequenceNumber = sendMessageResult.getSequenceNumber();
					String messageId = sendMessageResult.getMessageId();

					log.info("Combo SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
				}

			} catch (Exception e) {
				whStatus.setEventStatus("Error in activate sim ");
				whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
				log.error("Error in simActivation : " + e.getLocalizedMessage());
				async.asynSlackMessage("Error in sim activation ",
						event_id + " : " + event_type + " : " + e.getLocalizedMessage());
				checkFun = false;

				errorDescription += " Error : " + e.getMessage();
				return "Exception - checkChargebeewebhooks :\n" + e.getLocalizedMessage();
			}

			// check and remove user_cancel_feedback
			async.checkAndRemoveUserCancelFeedBack(cbId);
			chargebeeWebhookService.saveWebHookStatus(whStatus);
		} catch (Exception e1) {
			// e1.printStackTrace();
			log.error("Json Error in simActivation" + e1.getLocalizedMessage());
			checkFun = false;
			errorDescription += " Error :" + e1.getLocalizedMessage();
		} finally {
			if (!checkFun) {
				try {
					ErrorInfo erinfo = new ErrorInfo();

					erinfo.setEvent_id(cbId+"_evcombo");

					erinfo.setDescription(errorDescription);

					erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

					erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

					erinfo.setFunction_type("Reactivation SIM");

					erinfo = errorService.updateErrorInfo(erinfo);

					log.error("update Error Info Status : " + erinfo == null);
				} catch (Exception e) {
					log.error(" Error in finally : simActivation" + e.getLocalizedMessage());
				}
			}
		}
		return "Success";
	}

	@Override
	public String getPlanConfig(String cbPlan) {
		return planDao.getPlanConfig(cbPlan);
	}

    @Override
    public void handleVetchatPlanCancellation(String planId, UserV4 user) {

        log.info("Entered into handleVetchatPlanCancellation :: user_email : {}", user.getEmail());
        try {
            boolean isVetChatPlan = planService.isVetChatPlan(planId);

            if(isVetChatPlan) {
                cancelVetChatPlan(user.getEmail());
            }
        } catch (Exception e) {
            log.error("Error in activateVetChatPlan : {}", e.getLocalizedMessage());
        }
    }

    private void cancelVetChatPlan(String email) {

        int maxRetries = 3;
        int delayMillis = 2000;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                Map<String, Object> postParam = new HashMap<>();
                postParam.put("email", email);
                postParam.put("status", "cancelled");
                postParam.put("role", "protect_users");

                JSONObject bodyJson = new JSONObject(postParam);

                String response = sendPostRequest(bodyJson);
                log.info("Response from VetChat API : {}", response);

                JSONObject responseJson = new JSONObject(response);
                boolean success = responseJson.optBoolean("success", false);

                if (success) {
                    break;
                } else {
                    log.info("Attempt {} failed with API response: {}", attempt, response);
                }
            } catch (Exception e) {
                log.error("Attempt {} threw exception: {}", attempt, e.getLocalizedMessage());
            }

            // Delay before next retry
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(delayMillis);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Sleep interrupted during retry delay");
                    break; // Exit loop if interrupted
                }
            } else {
                log.error("All retry attempts failed for cancelVetChatPlan");
            }
        }
    }

    private String sendPostRequest(JSONObject bodyJson) {

        log.info("Entered into sendPostRequest...");

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(vetchatUsername, vetchatPassword);

        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = bodyJson.toString();

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                vetchatCancellationUrl,
                HttpMethod.POST,
                request,
                String.class
        );

        return response.getBody();
    }
}
