package com.nimble.webhooks.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IUserDao;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AdditionBenefitsCancelReward;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.UserCancelFeedBack;
import com.nimble.webhooks.service.IUserService;


@Service
@Transactional
public class UserServiceImpl implements IUserService{

	@Autowired
	IUserDao userDao;
	
	
	@Override
	public UserV4 verifyAuthV4(String key,String value) {
		return userDao.verifyAuthV4(key,value);
	}


	@Override
	public UserV4 getUserByUNameOrEmail(String cbEmail) {
		return userDao.getUserByUNameOrEmail(cbEmail);
	}


	@Override
	public boolean updateProductSubscription(long user_id) {
		return userDao.updateProductSubscription(user_id);
	}


	@Override
	public int executeQuery(String qry) {
		return userDao.executeQuery(qry);
	}


	@Override
	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id) {
		return userDao.getUserCancelFeedBackByUserId(user_id);
	}


	@Override
	public boolean deteleUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack) {
		return userDao.deteleUserCancelFeedBack(userCancelFeedBack);
	}


	@Override
	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id) {
		return userDao.getAdditionBenefitsCancelReward(user_id);
	}


	@Override
	public boolean deteleAdditionBenefitsCancelReward(AdditionBenefitsCancelReward additionBenefitsCancelReward) {
		return userDao.deteleAdditionBenefitsCancelReward(additionBenefitsCancelReward);
	}


	@Override
	public long getUserByChargebeeId(String customerId) {
		return userDao.getUserByChargebeeId(customerId);
	}
	
	public String getQrcIdfromgatewayId(long gatewayid) {
		return userDao.getQrcIdfromgatewayId(gatewayid);
	}
	
	public String getOrderIdfromQrc(String qrc) {
		return userDao.getOrderIdfromQrc(qrc);
	}
	
	@Override
	public boolean updateProductSubscriptionByOrderId(long id, String orderId) {
		return userDao.updateProductSubscriptionByOrderId(id, orderId);
	}
	
	@Override
	public boolean updateDuplicateAllCBSubscription(AllSubscription prodSubs) {
		return userDao.updateDuplicateAllCBSubscription(prodSubs);
	}
	
	@Override
	public boolean updateDuplicateAllProductSubscription(AllProductSubscription prodSubs) {
		return userDao.updateDuplicateAllProductSubscription(prodSubs);
	}

	@Override
	public String getDeviceToken(long userId) {
		return userDao.getDeviceToken(userId);
	}
}
