package com.nimble.webhooks.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.chargebee.Result;
import com.chargebee.exceptions.InvalidRequestException;
import com.chargebee.models.Customer;
import com.chargebee.models.Invoice;
import com.chargebee.models.Order;
import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.entity.CBShopifyOrders;
import com.nimble.webhooks.entity.ErrorInfo;
import com.nimble.webhooks.helper.Email;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IErrorService;
import com.nimble.webhooks.service.IExternalConfigService;
import com.nimble.webhooks.service.IGatewayService;
import com.nimble.webhooks.service.ISubscriptionMrrService;
import com.nimble.webhooks.service.IUserService;
import com.nimble.webhooks.util.IrisUtil;
import com.nimble.webhooks.util.SecretManagerService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestTemplate;

import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.nimble.webhooks.dao.IChargebeeWebhooksDao;
import com.nimble.webhooks.dao.INiomDao;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.CBCancelHistory;
import com.nimble.webhooks.entity.CbActivateCancelSubStatus;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.entity.Coupon;
import com.nimble.webhooks.entity.Credits;
import com.nimble.webhooks.entity.DeviceSubscription;
import com.nimble.webhooks.entity.Invoices;
import com.nimble.webhooks.entity.PlanToMonitorType;
import com.nimble.webhooks.entity.PlantoPeriod;
import com.nimble.webhooks.entity.Subscription;
import com.nimble.webhooks.entity.UnpaidInvoices;
import com.nimble.webhooks.entity.UserSubscription;
import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.IPlanService;

@Service
public class ChargebeeWebhookServiceImpl implements IChargebeeWebhookService {

    private static final Logger log = LogManager.getLogger(ChargebeeWebhookServiceImpl.class);

    @Value("${chargebee.site.name}")
    private String chargebeeSiteName;

    @Value("${chargebee.site.key}")
    private String chargebeeSiteKey;

    @Value("${vetchat.activation_url}")
    private String vetchatUrl;

    @Value("${vetchat.username}")
    private String vetchatUsername;

    @Value("${vetchat.password}")
    private String vetchatPassword;

    @Value("${verizon_activation}")
    private boolean verizon_activation;

    @Value("${omitplan}")
    private String omitplan;

    @Autowired
    IChargebeeWebhooksDao chargebeeWebhooksDao;

    @Autowired
    INiomDao niomDao;

    @Autowired
    private IUserService userService;

    @Autowired
    IChargebeeWebhookService chargebeeWebhookService;

    @Autowired
    private ISubscriptionMrrService subscriptionMrrService;

    @Autowired
    private Email emailHelper;

    @Value("${addonids}")
    private String addonids;

    @Autowired
    private Configuration templates;

    @Autowired
    private IAsyncService async;

    @Autowired
    private IPlanService planService;

    @Autowired
    private IErrorService errorinfoService;

    @Autowired
    private SecretManagerService secretManagerService;

    @Autowired
    private IGatewayService gatewayService;

    @Autowired
    private IExternalConfigService externalConfigService;

    private Helper helper = new Helper();

    @Value("${aws_sqs_secret_name}")
    private String SQS_SECRET_NAME;

    @Value("${infobip.url}")
    private String infobip_url;

    @Value("${infobip.app.key}")
    private String infobip_app_key;

    @Value("${iris.services.amazonSQS.microserviceQueue.url}")
    private String amazonSQS_microserviceQueue_url;

    @Value("${InvoiceVoid.enable}")
    private boolean invoiceVoidEnable;

    @Value("${microservice_url}")
    private String microservice_url;

    @Value("${microservice_api_call}")
    private boolean microserviceApiCall;

    @Value("${Shopify.OrderCreate.Enable}")
    private boolean ShopifyOrderCreateEnable;

    @Value("${insertOrUpdateCredits}")
    private boolean insertOrUpdateCredits;

    @Value("${disableCancellation}")
    private boolean disableCancellation;

    @Value("${insertOrUpdateSubscription}")
    private boolean insertOrUpdateSubscription;

    @Override
    @Transactional
    public AllSubscription checkSubscriptionStatus(String subscriptionId) {
        return chargebeeWebhooksDao.checkSubscription(subscriptionId);
    }

    @Override
    @Transactional
    public String insertAllSubscription(AllSubscription subscription, boolean update) {
        String updatedStatus = "";
        if (update) {
            updatedStatus = chargebeeWebhooksDao.updateAllSubscription(subscription);
        } else {
            updatedStatus = chargebeeWebhooksDao.insertAllSubscription(subscription);
        }
        return updatedStatus;
    }

    @Override
    @Transactional
    public String insertHistorySubscription(Subscription subscription, boolean update) {
        String updatedStatus = "0";
        updatedStatus = chargebeeWebhooksDao.insertHistorySubscription(subscription);
        return updatedStatus;
    }

    @Override
    @Transactional
    public String insertCredit(Credits credits) {
        String updated = "2";
        Credits savedCredits = chargebeeWebhooksDao.checkCredit(credits.getChargebee_id());
        if (credits.getPromotional_credits() != 0.0 || credits.getRefundable_credits() != 0.0) {
            if (savedCredits == null) {
                updated = chargebeeWebhooksDao.saveCredits(credits);
            } else {
                credits.setId(savedCredits.getId());

                if (credits.getPromotional_credits() == 0.0) {
                    credits.setPromotional_credits(savedCredits.getPromotional_credits());
                }
                if (credits.getRefundable_credits() == 0.0) {
                    credits.setRefundable_credits(savedCredits.getRefundable_credits());
                }

                updated = chargebeeWebhooksDao.updateCredits(credits);
            }
        }
        return updated;
    }

    @Override
    @Transactional
    public String getPlanToPeriod(String planId) {
        return chargebeeWebhooksDao.getPlanToPeriod(planId);
    }

    @Override
    @Transactional
    public boolean checkAdditionalSubscription(String id) {
        try {

            Environment.configure(chargebeeSiteName, chargebeeSiteKey);
            ListResult result = com.chargebee.models.Subscription.list().customerId().is(id).request();
            for (ListResult.Entry data : result) {
                if (data.subscription().status().toString().equalsIgnoreCase("active"))
                    return false;
            }
        } catch (Exception e) {
            System.out.println("");
        }
        return true;
    }

    @Override
    @Transactional
    public CbActivateCancelSubStatus getUserDetails(String chargebeeId) {
        try {
            CbActivateCancelSubStatus cSubs = chargebeeWebhooksDao.getUserDetails(chargebeeId);
            return cSubs;
        } catch (Exception e) {
            System.out.println("");
            return null;
        }
    }

    @Override
    @Transactional("niomtransactionManager")
    public CbActivateCancelSubStatus getOrderDetails(CbActivateCancelSubStatus cSubs) {
        cSubs = niomDao.getOrderDetails(cSubs);
//		boolean orderMapStatus = niomDao.changeDeviceStatus(cSubs.getOrderId());
        return cSubs;
    }

    @Override
    @Transactional
    public boolean saveCancelActivateStatusData(CbActivateCancelSubStatus cSubs) {
        return chargebeeWebhooksDao.saveCancelActivateStatusData(cSubs);
    }

//	@Override
//	public int getVPMPlanTxnCount(String cb_planid) {
//		return chargebeeWebhooksDao.getVPMPlanTxnCount(cb_planid);
//	}

    @Override
    @Transactional
    public CbActivateCancelSubStatus getCancelSubStatus(String eventId) {
        return chargebeeWebhooksDao.getCancelSub(eventId);
    }

    @Override
    @Transactional
    public ChargebeeWebhooksStatus saveWebHookStatus(ChargebeeWebhooksStatus whStatus) {
        return chargebeeWebhooksDao.saveWebHookStatus(whStatus);
    }

    @Override
    @Transactional
    public ChargebeeWebhooksStatus webHookStatusIsAvailable(ChargebeeWebhooksStatus whStatus) {
        return chargebeeWebhooksDao.webHookStatusIsAvailable(whStatus);
    }

    @Override
    @Transactional
    public ChargebeeWebhooksStatus saveWebHookStatusByEventId(ChargebeeWebhooksStatus whStatus) {
        return chargebeeWebhooksDao.saveWebHookStatusByEventId(whStatus);
    }

    @Override
    @Transactional
    public boolean updateWebHookStatus(ChargebeeWebhooksStatus whStatus) {
        return chargebeeWebhooksDao.updateWebhookStatus(whStatus);
    }

    @Override
    @Transactional
    public String checkInvoiceInDB(String invoice_id) {
        return chargebeeWebhooksDao.checkInvoiceInDB(invoice_id);
    }

    @Override
    @Transactional
    public int updateInvoice(Invoices invoice) {
        return chargebeeWebhooksDao.updateInvoice(invoice);
    }

    @Override
    @Transactional
    public int insertInvoice(Invoices invoice) {
        return chargebeeWebhooksDao.insertInvoice(invoice);
    }

    @Override
    @Transactional
    public UserSubscription getUser_subscription(String chargebeeId) {
        return chargebeeWebhooksDao.getUser_subscription(chargebeeId);
    }

    @Override
    @Transactional
    public int saveOrUpdateUser_subscription(UserSubscription userSub) {
        return chargebeeWebhooksDao.saveOrUpdateUser_subscription(userSub);
    }

    @Override
    @Transactional
    public List<ChargebeeWebhooksStatus> getWebHookslist(Date fromDate) {
        return chargebeeWebhooksDao.getWebHookslist(fromDate);
    }

    @Override
    @Transactional
    public UnpaidInvoices checkUnpaidInvoice(String sub_id, String chargebeeId) {
        return chargebeeWebhooksDao.checkUnpaidInvoice(sub_id, chargebeeId);
    }

    @Override
    @Transactional
    public boolean saveOrUpdateUnpaidInvoice(UnpaidInvoices unpaidInvoice) {
        return chargebeeWebhooksDao.saveOrUpdateUnpaidInvoice(unpaidInvoice);
    }

    @Override
    @Transactional
    public UnpaidInvoices isUnpaidInvoice(String chargebeeId) {
        return chargebeeWebhooksDao.isUnpaidInvoice(chargebeeId);
    }

    @Override
    @Transactional
    public boolean saveRemoveAddon(String cus_id, String subId, String addon_id) {
        return chargebeeWebhooksDao.saveRemoveAddon(cus_id, subId, addon_id);
    }

    @Override
    @Transactional
    public int updateAllCBSubscription(String curr_code, String ex_rate, String subs_id, String state_code) {
        return chargebeeWebhooksDao.updateAllCBSubscription(curr_code, ex_rate, subs_id, state_code);
    }

    @Override
    @Transactional
    public boolean deleteAdditionalBenefits(String chargebeeid, String periodname) {
        return chargebeeWebhooksDao.deleteAdditionalBenefits(chargebeeid, periodname);
    }

    @Override
    @Transactional
    public boolean updateSubStatusInUserRetained(long user_id) {
        return chargebeeWebhooksDao.updateSubStatusInUserRetained(user_id);
    }

    @Override
    @Transactional
    public long getMonitorTypeByPlanId(String planId) {
        return chargebeeWebhooksDao.getMonitorTypeByPlanId(planId);
    }

    @Override
    @Transactional
    public AllProductSubscription checkProductSubscriptionStatus(String subscriptionId) {
        return chargebeeWebhooksDao.checkProductSubscriptionStatus(subscriptionId);
    }

    @Override
    @Transactional
    public String insertAllProductSubscription(AllProductSubscription subscription) {

        String updatedStatus = "";
        updatedStatus = chargebeeWebhooksDao.insertAllProductSubscription(subscription);

        return updatedStatus;
    }

    @Override
    @Transactional
    public PlantoPeriod getPlanAndPeriodId(String planName) {
        return chargebeeWebhooksDao.getPlanAndPeriodId(planName);
    }

    @Override
    @Transactional
    public long getUserByCbId(String cbId) {
        return chargebeeWebhooksDao.getUserByCbId(cbId);
    }

    @Override
    @Transactional
    public long getHostedPage(long userid, long monitortype_id) {
        return chargebeeWebhooksDao.getHostedPage(userid, monitortype_id);
    }

    @Override
    @Transactional
    public void deleteHostedPageId(long userid, long monitortype_id) {
        chargebeeWebhooksDao.deleteHostedPageId(userid, monitortype_id);
    }

    @Override
    @Transactional
    public PlantoPeriod getPlanToPeriodByMonitorId(long monitortype_id) {
        return chargebeeWebhooksDao.getPlanToPeriodByMonitorId(monitortype_id);
    }

    @Override
    @Transactional
    public boolean deleteGatewayFeature(long gateway_id) {
        return chargebeeWebhooksDao.deleteGatewayFeature(gateway_id);
    }

    @Override
    @Transactional
    public boolean insertOrUpdateCoupon(Coupon coupon) {
        return chargebeeWebhooksDao.insertOrUpdateCoupon(coupon);
    }

    @Override
    @Transactional
    public String getPlanVersionbyplanname(String planName) {
        return chargebeeWebhooksDao.getPlanVersionbyplanname(planName);
    }

    @Override
    @Transactional
    public boolean saveMigratedSubsStatus(String cus_id, String subId) {
        return chargebeeWebhooksDao.saveMigratedSubsStatus(cus_id, subId);
    }

    @Transactional
    public int saveOrUpdateDevice_subscription(DeviceSubscription deviceSub) {
        return chargebeeWebhooksDao.saveOrUpdateDevice_subscription(deviceSub);
    }

    @Override
    @Transactional
    public boolean savePauseHistorysStatus(String subId) {
        return chargebeeWebhooksDao.savePauseHistorysStatus(subId);
    }

    @Override
    @Transactional
    public DeviceSubscription getDevice_subscription(long gateway_id) {
        return chargebeeWebhooksDao.getDevice_subscription(gateway_id);
    }

    @Override
    @Transactional
    public String getResumedatebySubId(String subId) {
        return chargebeeWebhooksDao.getResumedatebySubId(subId);
    }

    @Override
    @Transactional
    public boolean deleteUserFeature(long userId) {
        return chargebeeWebhooksDao.deleteUserFeature(userId);
    }

    @Override
    @Transactional
    public void deleteFlexiPlanHistory(long gatewayId, String sub_id) {
        chargebeeWebhooksDao.deleteFlexiPlanHistory(gatewayId, sub_id);
    }

    @Override
    @Transactional
    public boolean saveCancelledSubscriptionHistory(CBCancelHistory cancelHistory) {

        return chargebeeWebhooksDao.saveCancelledSubscriptionHistory(cancelHistory);
    }

    @Override
    @Transactional
    public long getHostedPage(long userid) {
        return chargebeeWebhooksDao.getHostedPage(userid);
    }

    @Override
    @Transactional
    public String getPlanType(String cbPlanid) {
        return chargebeeWebhooksDao.getPlanType(cbPlanid);
    }

    @Override
    @Transactional
    public boolean updateComboPlanStatus(AllProductSubscription subs) {
        return chargebeeWebhooksDao.updateComboPlanStatus(subs);
    }

    @Override
    @Transactional
    public PlanToMonitorType getPlanToMonitorType(long planId) {
        return chargebeeWebhooksDao.getPlanToMonitorType(planId);
    }

    @Override
    @Transactional
    public AllProductSubscription getAllProductSubscriptionBySubId(String subscriptionId) {
        return chargebeeWebhooksDao.getAllProductSubscriptionBySubId(subscriptionId);
    }

    @Override
    @Transactional
    public String insertAllProductSubscriptionSQL(AllProductSubscription subscription) {
        String updatedStatus = "";
        updatedStatus = chargebeeWebhooksDao.insertAllProductSubscriptionSQL(subscription);
        return updatedStatus;
    }

    @Override
//    @Transactional
    public boolean updateMRRInAllCB(String subId, long mrr, boolean isAllCBSub) {
        return chargebeeWebhooksDao.updateMRRInAllCB(subId, mrr, isAllCBSub);
    }

    @Override
    @Transactional
    public String getSubExistIn(String subId) {
        return chargebeeWebhooksDao.getSubExistIn(subId);
    }

    @Override
    @Transactional
    public void handleVetchatPlanActivation(String planId, UserV4 user) {

        boolean isVetChatPlan = planService.isVetChatPlan(planId);
        if(isVetChatPlan) {
            activateVetChatPlan(user);
        }
    }

    private void activateVetChatPlan(UserV4 user) {

        log.info("Entered into activateVetChatPlan :: user_id : {}", user.getId());
        try {
            String deviceToken = userService.getDeviceToken(user.getId());
            Map<String, Object> postParam = populateRequestBody(user, deviceToken);
            JSONObject bodyJson = new JSONObject(postParam);

            String response = sendPostRequest(bodyJson);

            log.info("Response from VetChat API : {}", response);
        } catch (Exception e) {
            log.error("Error in activateVetChatPlan : {}", e.getLocalizedMessage());
        }
    }

    private String sendPostRequest(JSONObject bodyJson) {

        log.info("Entered into sendPostRequest...");

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(vetchatUsername, vetchatPassword);

        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = bodyJson.toString();

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.exchange(
                vetchatUrl,
                HttpMethod.POST,
                request,
                String.class
        );

        return response.getBody();
    }

    private static Map<String, Object> populateRequestBody(UserV4 user, String deviceToken) {

        Map<String, Object> postParam = new HashMap<>();

        postParam.put("first_name", user.getFirstname());
        postParam.put("last_name", user.getLastname());
        postParam.put("phone_no", user.getMobileno());
        postParam.put("zip_code", "12345");
        postParam.put("email", user.getEmail());
        postParam.put("dev_type", "mobile");
        postParam.put("app_version", "v1.0");
        postParam.put("status", "subscribed");
        postParam.put("type", "monthly");
        postParam.put("role", "protect_users");
        postParam.put("deviceToken", deviceToken);

        return postParam;
    }

	@Override
	@Transactional
	public boolean changeGatewayStatus(long gatewayId, boolean isEnable) {
		return chargebeeWebhooksDao.changeGatewayStatus(gatewayId, isEnable);
	}

    @Override
    @Transactional
    public boolean updateSubStatusinAllProductSub(String subscriptionId, boolean isAllCbSub) {
        return chargebeeWebhooksDao.updateSubStatusinAllProductSub(subscriptionId, isAllCbSub);
    }

    @Override
	@Transactional
	public boolean updateProductSubscriptionStatus(String subId, String subs_status) {
		return chargebeeWebhooksDao.updateProductSubscriptionStatus(subId, subs_status);
	}

	@Override
    @Transactional
	public boolean deleteGatewayFeatureBySubId(String sub_id) {
		return chargebeeWebhooksDao.deleteGatewayFeatureBySubId(sub_id);
	}

	@Override
    @Transactional
	public boolean updateComboPlanMonitorType(AllProductSubscription subs) {
		return chargebeeWebhooksDao.updateComboPlanMonitorType(subs);
	}

	@Override
    @Transactional
	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeId) {
		return chargebeeWebhooksDao.getSubscriptionByChargebeeId(chargebeeId);
	}
	
	@Override
    @Transactional
	public long findPetmonitorincomboPlan(String subId) {
		return chargebeeWebhooksDao.findPetmonitorincomboPlan(subId);
	}

	@Override
	@Transactional
	public boolean deleteVetChatSub(long mtype, String subid) {
		return chargebeeWebhooksDao.deleteVetChatSub(mtype, subid);
	}
	
	@Override
	@Transactional
	public boolean getduplicatewithactivesub(String subId, String chargbeeId, long gatewayId) {
		return chargebeeWebhooksDao.getduplicatewithactivesub(subId, chargbeeId, gatewayId);
	}
	
	@Override
	@Transactional
	public boolean getNonduplicateAllproductSubscription(String subId, String chargbeeId, long gatewayId,String email) {
		return chargebeeWebhooksDao.getNonduplicateAllproductSubscription(subId, chargbeeId, gatewayId,email);
	}

	@Override
	@Transactional
	public DeviceSubscription getDevice_subscription(String sub_id) {
		return chargebeeWebhooksDao.getDevice_subscription(sub_id);
	}

	@Override
	@Transactional
	public boolean updateDevicesubscriptionForV2Plan(DeviceSubscription deviceSub) {
		return chargebeeWebhooksDao.updateDevicesubscriptionForV2Plan(deviceSub);
	}

    @Override
    @Transactional
    public void chargebeeEventWebhookProcess(JSONObject res, ChargebeeWebhooksStatus whStatusOrigin) {

        ChargebeeWebhooksStatus whStatus = whStatusOrigin;
        try {
            if(whStatus.getChargebeeId().equalsIgnoreCase("NA") && res.getString("event_type").contains("subscription")) {
                log.info("User CBId is NA:"+res);
                async.asynSlackMessage("User CBId is NA:",res.getString("id") + " : " + res.getString("event_type") );
                return;
            }

            Environment.configure(chargebeeSiteName, chargebeeSiteKey);

            String event_id = "";
            String event_type = "";
            String eventProcess = "";
            String planId = "";
            UserV4 user = new UserV4();
            JSONObject content = null;
            JSONObject subscrip = null;
            String sub_event_type = "NA";

            if (res.getString("event_type").equalsIgnoreCase("subscription_deleted")
                    || res.getString("event_type").equalsIgnoreCase("subscription_paused"))
                res.put("event_type", "subscription_cancelled");

            try {
                event_id = res.getString("id");
                event_type = res.getString("event_type");
                eventProcess = "";
                content = res.getJSONObject("content");
            } catch (Exception e) {
                log.error("chargebeeEventWebhookProcess:Parse Packet : " + e.getLocalizedMessage());
                async.asynSlackMessage("Parse Packet ",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getLocalizedMessage());
                return;
            }

            long gateway_id = 0;
            try {
                if (res.getString("event_type").equalsIgnoreCase("mrr_updated")) {
                    log.info("mrr_updated content:"+content);
                    if (content.has("subscription")) {
                        subscrip = content.getJSONObject("subscription");
                        String subId = subscrip.getString("id");
                        if(subscrip.has("mrr")) {
                            long mrr =subscrip.getLong("mrr");

                            if(mrr>0) {
                                boolean stat = subscriptionMrrService.updateMRR(subId, mrr);
                                if(stat)
                                    whStatus.setEventStatus("mrr updated");
                                whStatus.setStatus(stat);
                                log.info("ASYN:updateMRR: "+mrr+":"+stat);
                            }
                        }else {
                            log.error("mrr not found subId:"+subId);
                        }
                    }
                    return;
                }
                if (res.getString("event_type").contains("coupon")) {
                    if (content.has("coupon")) {
                        Coupon coupon = new Coupon();
                        coupon.setCoupon_id(content.getJSONObject("coupon").getString("id"));
                        coupon.setCoupon_name(content.getJSONObject("coupon").getString("name"));

                        String discount_type = content.getJSONObject("coupon").getString("discount_type");
                        coupon.setDiscount_type(discount_type);

                        if (discount_type.equalsIgnoreCase("percentage"))
                            coupon.setDiscount_value(content.getJSONObject("coupon").getString("discount_percentage"));
                        else
                            coupon.setDiscount_value(content.getJSONObject("coupon").getString("discount_amount"));

                        coupon.setDuration_type(content.getJSONObject("coupon").getString("duration_type"));
                        coupon.setCoupon_status(content.getJSONObject("coupon").getString("status"));

                        if( res.getString("event_type").equalsIgnoreCase("coupon_deleted"))
                            coupon.setDeleted(true);
                        if(content.has("valid_till"))
                            coupon.setValid_till(toDate(content.getJSONObject("coupon").getInt("valid_till")));
                        else
                            coupon.setValid_till("1753-01-01 00:00:01");
                        coupon.setCreated_on(toDate(content.getJSONObject("coupon").getInt("created_at")));
                        coupon.setUpdated_on(toDate(content.getJSONObject("coupon").getInt("updated_at")));

                        boolean stat = chargebeeWebhookService.insertOrUpdateCoupon(coupon);
                        if(stat)
                            whStatus.setEventStatus("coupon updated");
                        whStatus.setStatus(stat);
                    }
                    return;
                }
            }catch (Exception e) {
                log.error("Error handling mrr_updated event : "+e.getLocalizedMessage());
            }

            if (event_type.contains("subscription")) {
                planId = content.getJSONObject("subscription").getString("plan_id");

                if (!planId.equalsIgnoreCase("chum")) {

                    UserSubscription usrSub = chargebeeWebhookService.getUser_subscription(whStatus.getChargebeeId());

                    DeviceSubscription deviceSub = null;

                    if (usrSub != null) {
                        try {
                            log.info("Process cancel flow");
                            boolean isDownGrade = false;
                            if (content.getJSONObject("subscription").has("addons")) {
                                JSONArray addons = content.getJSONObject("subscription").getJSONArray("addons");
                                for (int i = 0; i < addons.length(); i++) {
                                    JSONObject addon = (JSONObject) addons.get(i);
                                    String addon_id = addon.getString("id");

                                    if (addon_id.contains("downgrade")) {
                                        isDownGrade = true;
                                        log.info("downgrade addon found");
                                    }
                                }
                            }
                            log.info("Event id : " + event_id);
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Date today = sdf.parse(new Helper().getCurrentTimeinUTC());
                            Date startTime = usrSub.getCur_plan_dt();
                            log.info("last plan date : " + usrSub.getCur_plan_dt());

                            long difference = today.getTime() - startTime.getTime();
                            double daysBetween_active = Math.ceil((difference / (1000 * 60 * 60 * 24)));
                            log.info("daysBetween_active : " + daysBetween_active);

                            if (event_type.equalsIgnoreCase("subscription_changed") && isDownGrade
                                    && !content.has("invoice")) {
                                log.info("No invoice found");
                                return;
                            }

                            if (isDownGrade && daysBetween_active <= 7 && content.has("invoice")) {
                                log.info("plan purchased date is within 7 days");
//							String planId = content.getJSONObject("subscription").getString("plan_id");
                                String cbId = content.getJSONObject("customer").getString("id");
                                String email = content.getJSONObject("customer").getString("email");

                                JSONArray creditsArray = content.getJSONArray("credit_notes");
                                float refundableCredits = 0;
                                for (int i = 0; i < creditsArray.length(); i++) {
                                    JSONObject credits = creditsArray.getJSONObject(i);
                                    if (credits.has("reason_code")
                                            && credits.getString("reason_code").equalsIgnoreCase("subscription_change")) {
                                        refundableCredits = credits.getInt("amount_available") / 100;
                                    }
                                }

                                String firstName = content.getJSONObject("customer").getString("first_name");

                                Properties prop = new Properties();
                                File file = ResourceUtils.getFile("classpath:wagglehooks.properties");
                                prop.load(new FileInputStream(file));

                                String to_address = prop.getProperty("to_address");
                                String cc_address = prop.getProperty("cc_address");
                                String bcc_address = prop.getProperty("bcc_address");

                                String mailSub = "Reg Switch Plan : " + email;
                                String mailContent = "<p>Hi Team,</p>" + "<p>Find the user switch plan details</p>";
                                mailContent += "<p>Email           : " + email + "</p>";
                                mailContent += "<p>Chargebee Id    : " + cbId + "</p>";
                                mailContent += "<p>Current Plan    : " + planId + "</p>";
                                mailContent += "<p>Old Plan        : " + usrSub.getCur_planid() + "</p>";
                                mailContent += "<p>Credits         : " + refundableCredits + "</p>";

                                mailContent = mailContent + "<br><br>Thanks,<br> Waggle Hooks ";
                                emailHelper.SendEmail_SES(to_address, cc_address, bcc_address, mailSub, mailContent);

                                Template emailTemplate = (Template) templates.getTemplate("RefundEmail.ftl");
                                Map<String, String> emailParams = new HashMap<>();
                                String sub = "Confirmation of Waggle Pet Monitor Subscription Refund";
                                emailParams.put("Name", firstName);
                                emailParams.put("Year", String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));

                                ResponseEntity<String> newEmailContent = ResponseEntity
                                        .ok(FreeMarkerTemplateUtils.processTemplateIntoString(emailTemplate, emailParams));
                                String msg = newEmailContent.getBody();

                                emailHelper.SendEmail_SES(email, cc_address, bcc_address, sub, msg);

                                log.info("cancel flow process done");
                            }

                        } catch (Exception e) {
                            log.error("Error in process cancel flow :: Error : " + e.getLocalizedMessage());
                        }
                    }

                    // voidInvoice
                    if (invoiceVoidEnable && !res.get("event_type").equals("subscription_cancelled")
                            && (res.get("event_type").equals("subscription_created")
                            || res.get("event_type").equals("subscription_changed"))) {

                        JSONObject jres = res.getJSONObject("content");

                        if (jres.has("invoice")) {
                            JSONObject jInvoice = jres.getJSONObject("invoice");
                            if (jInvoice.getString("status").equals("paid")) {
                                boolean status = VoidInvoice(res);
                            }
                        }
                    }

                    long monitortype_id = chargebeeWebhookService.getMonitorTypeByPlanId(content.getJSONObject("subscription").getString("plan_id"));
                    String planver = chargebeeWebhookService.getPlanVersionbyplanname(content.getJSONObject("subscription").getString("plan_id"));
                    boolean avilDup = false;
                    String planName = content.getJSONObject("subscription").getString("plan_id");
                    String pType = chargebeeWebhookService.getPlanType(planName);

                    try {
                        boolean[] isValid = checkSimActivationAndVpm(res);
                        if (planver != null && !planver.equalsIgnoreCase("NA") && !planver.equalsIgnoreCase("V3")
                                && monitortype_id == 1) {
                            if (!event_type.contains("subscription_cancelled") && content.has("invoice") && isValid[0]
                                    && verizon_activation) {
                                simActivation(res, whStatus); // verizon sim activation
                            } else if (!event_type.contains("subscription_cancelled") && content
                                    .getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial")
                                    && isValid[0] && verizon_activation) {
                                simActivation(res, whStatus); // verizon sim activation
                            } else if (!event_type.contains("subscription_cancelled")
                                    && (event_type.contains("subscription_reactivated") && content.has("invoice"))
                                    || (event_type.contains("subscription_resumed"))) {
                                simActivation(res, whStatus); // verizon sim activation
                            }
                        }

                    } catch (Exception e) {
                        log.error("Error in sim activation  :" + e.getLocalizedMessage());
                        async.asynSlackMessage("Error in sim activation ", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }

                    // plan Vs pricing Functionality.
                    try {

                        // its used For Subs cancel and update
                        gateway_id = Updateplanvsfeature(res, whStatus, usrSub);
                        log.info("update plan vs Feature : gatewayid : " + gateway_id);
                        if(gateway_id>0 ) {

                            try {
                                boolean[] isValid = checkSimActivationAndVpm(res);

                                if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")
                                        && monitortype_id == 1) {
                                    if (!event_type.contains("subscription_cancelled") && content.has("invoice") && isValid[0]
                                            && verizon_activation) {
                                        simActivationGateway(res, whStatus,gateway_id); // verizon sim activation
                                    } else if (!event_type.contains("subscription_cancelled") && content
                                            .getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial")
                                            && isValid[0] && verizon_activation) {
                                        simActivationGateway(res, whStatus,gateway_id); // verizon sim activation
                                    } else if (!event_type.contains("subscription_cancelled")
                                            && (event_type.contains("subscription_reactivated") && content.has("invoice"))
                                            || (event_type.contains("subscription_resumed"))) {
                                        simActivationGateway(res, whStatus,gateway_id); // verizon sim activation
                                    }

                                    avilDup = chargebeeWebhookService.getduplicatewithactivesub(content.getJSONObject("subscription").getString("id"),content.getJSONObject("customer").getString("id"),gateway_id);
                                }

                            } catch (Exception e) {
                                log.error("Error in sim activation  :" + e.getLocalizedMessage());
                                async.asynSlackMessage("Error in sim activation ", res.getString("id") + " : "
                                        + res.getString("event_type") + " : " + e.getLocalizedMessage());
                            }

                            if (!event_type.contains("subscription_cancelled"))
                                chargebeeWebhookService.changeGatewayStatus(gateway_id, true);
                        }

                    } catch (Exception e) {
                        log.error("Error While update plan vs Feature:" + e.getLocalizedMessage());
                        async.asynSlackMessage("Error While update plan vs Feature", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }
                    if (content.getJSONObject("subscription").getString("plan_id") != null && !content.getJSONObject("subscription").getString("plan_id").trim().equalsIgnoreCase("")) {

                        if(content.getJSONObject("subscription").getString("plan_id").contains("flexi") &&
                                content.getJSONObject("subscription").getString("status").contains("cancel")){
                            chargebeeWebhookService.deleteFlexiPlanHistory(gateway_id,
                                    content.getJSONObject("subscription").getString("id"));
                        }
                    }

                    user = userService.verifyAuthV4("chargebeeid",
                            content.getJSONObject("customer").getString("id"));

                    /* Subscription reactivation addon removal */
                    try {
                        if (event_type.equalsIgnoreCase("subscription_reactivated")
                                || event_type.equalsIgnoreCase("subscription_changed")) {
                            if (content.getJSONObject("subscription").has("addons")) {
                                JSONArray addons = content.getJSONObject("subscription").getJSONArray("addons");
                                for (int i = 0; i < addons.length(); i++) {
                                    JSONObject addon = (JSONObject) addons.get(i);
                                    String addon_id = addon.getString("id");

                                    if (addonids.contains(addon_id)) {
                                        if (!addon.has("remaining_billing_cycles")) {
                                            log.info("Addon has no remaining_billing_cycles");
                                            String subId = content.getJSONObject("subscription").getString("id");
                                            Result res1 = com.chargebee.models.Subscription.update(subId)
                                                    .replaceAddonList(true).reactivate(false).request();
                                            log.info("Addon removed");
                                            String cus_id = content.getJSONObject("customer").getString("id");
                                            chargebeeWebhookService.saveRemoveAddon(cus_id, subId, addon_id);
                                        }
                                    }
                                }
                            }
                            try {

    							String subId = content.getJSONObject("subscription").getString("id");
    							if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3") && monitortype_id == 1) {
    								String cus_id = content.getJSONObject("customer").getString("id");

    								chargebeeWebhookService.saveMigratedSubsStatus(cus_id, subId);

    								chargebeeWebhookService.deleteUserFeature(user.getId());

    							}

    						} catch (Exception e) {
                                log.error("Error in subscription migrate status update : " + e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error in subscription reactivation addon removal : " + e.getMessage());
                    }

                    // subscription cancel
                    try {
                        // JSONObject customer = null;

                        if (event_type.equalsIgnoreCase("subscription_cancelled") && disableCancellation) {
                            log.info("Subscription Cancellation Process");
                            whStatus.setEventProcess("Subscription Cancellation");
                            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
                            whStatus.setEventStatus("Subscription Cancellation :  Cancel Subs");

                            String cbId = content.getJSONObject("customer").getString("id");
                            String email = content.getJSONObject("customer").getString("email");
                            // String planId =
                            // content.getJSONObject("subscription").get("plan_id").toString();
                            // String vpmplan = getExternalConfigValue("vpmplan");
//						if (vpmplan.contains(content.getJSONObject("subscription").get("plan_id").toString())) {
//							decreaseVpmCount(cbId, event_id, email,whStatus);
//						} else {

                            if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {
                                String subId = content.getJSONObject("subscription").getString("id");

                                if (pType != null && pType.equalsIgnoreCase("Combo-Plan")) {

                                    long gateway = chargebeeWebhookService.findPetmonitorincomboPlan(subId);
                                    deactivateDevicebasedGateway(cbId, event_id, email, whStatus, gateway);
                                }else if (pType != null && !pType.equalsIgnoreCase("Combo-Plan") && monitortype_id == 1 && !avilDup) {
                                    deactivateDevicebasedGateway(cbId, event_id, email, whStatus, gateway_id);
                                }

                            }else if(monitortype_id == 1){
                                deactivateGateway(cbId, event_id, email, whStatus);
                            }

//						}
                        }
                    } catch (Exception e) {
                        log.error("Error in subscription cancel :" + e.getLocalizedMessage());
                        async.asynSlackMessage("Error While subscription cancel :", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }

                    // update product subscription details
                    try {
                        log.info("Entered into update product_subscription details :: request id : " + res.getString("id"));

                        if (user != null) {
                            if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {

                                String qrcode =  userService.getQrcIdfromgatewayId(gateway_id);

                                if (qrcode != null) {
                                    String orderId = userService.getOrderIdfromQrc(qrcode);

                                    if (orderId != null) {
                                        boolean isUpdated = userService.updateProductSubscriptionByOrderId(user.getId(),
                                                orderId);
                                        log.info(" update product_subscription by order id status : " + isUpdated);
                                    }
                                }
                            }else {
                                boolean isUpdated = userService.updateProductSubscription(user.getId());
                                log.info(" update product_subscription status : " + isUpdated);
                            }

                        } else {
                            log.info("user not found for chargebee id : "
                                    + content.getJSONObject("customer").getString("id"));
                        }

                    } catch (Exception e) {
                        log.error("Error in product_subscription :" + e.getLocalizedMessage());
                        async.asynSlackMessage("Error While update product_subscription  :", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }

                    // Create Shopify Orders thru Chargebee
//				try {
//					if (ShopifyOrderCreateEnable)
//						CreateShopifyOrders(res);
//
//				} catch (Exception e) {
//					// TODO: handle exception
//					log.error(e.getLocalizedMessage());
//					asynSlackMessage("Error While Create Shopify Orders :", res.getString("id") + " : "
//							+ res.getString("event_type") + " : " + e.getLocalizedMessage());
//				}

                    // insert user_subscription and device suscription
                    try {
                        whStatus.setEventProcess(eventProcess);
                        boolean check = false;

                        check = saveOrUpdateUserSubscription(res, usrSub, gateway_id);
                        if(planver.equalsIgnoreCase("v2") || pType.equalsIgnoreCase("Combo-Plan")) {
                            String subId = content.getJSONObject("subscription").getString("id");
                            deviceSub = chargebeeWebhookService.getDevice_subscription(subId);
                            if(deviceSub != null) {
                                check = saveOrUpdateDeviceSubscription(res, deviceSub, deviceSub.getGateway_id(),planver,pType);
                            }
                        }
                        else if(gateway_id>0) { // For creating subscription in CB site, will not have gateway id
                            deviceSub = chargebeeWebhookService.getDevice_subscription(gateway_id);
                            check = saveOrUpdateDeviceSubscription(res, deviceSub, gateway_id,planver,pType);
                        }

                    } catch (Exception e) {
                        log.error(e.getLocalizedMessage());
                        async.asynSlackMessage("Error While Update user_subscription :", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }

                    eventProcess = "subcription_processing";
                    // Insert subscription functionality
                    try {
                        // insert or update subscription

                        if (insertOrUpdateSubscription) {
                            whStatus.setEventProcess(eventProcess);
                            whStatus = insertOrUpdateSubscription(res, whStatus, sub_event_type, gateway_id);

                            if(planver != null && planver.equalsIgnoreCase("V3") && avilDup && monitortype_id == 1) {
                                chargebeeWebhookService.getNonduplicateAllproductSubscription(content.getJSONObject("subscription").getString("id"),user.getChargebeeid(),gateway_id,user.getEmail());
                            }
                        }

//					if (event_type.equalsIgnoreCase("subscription_created") && content.has("invoice")) {
                        // boolean metadataUpdated = updateMetadataAndMergeUser( res, whStatus );
//					}

                    } catch (Exception e) {
                        log.error(e.getLocalizedMessage());
                        async.asynSlackMessage("Error While Update AllSubscription  :", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }

                }
            }
            else if (event_type.contains("credits") || event_type.contains("customer_changed")) {

                if (insertOrUpdateCredits) {
                    whStatus.setEventProcess("insert or update credits");
                    whStatus.setEventStatus("insert or update cretids ");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
                    insertOrUpdateCredits(content, whStatus);
                }

            } else if (event_type.contains("order_created")) {

                log.info("Entered Into CreateShopifyOrders   : " + res.getString("id") + " : "
                        + res.getString("event_type"));
                try {
                    log.info("ShopifyOrderCreateEnable : " + ShopifyOrderCreateEnable);
                    if (ShopifyOrderCreateEnable)
                        CreateShopifyOrders(res);

                } catch (Exception e) {
                    log.error("Error While Create Shopify Orders :" + e.getLocalizedMessage());
                    async.asynSlackMessage("Error While Create Shopify Orders :", res.getString("id") + " : "
                            + res.getString("event_type") + " : " + e.getLocalizedMessage());
                }
            }

            //vet chat plan activation/cancellation
            if(event_type.equalsIgnoreCase("subscription_created")) {
                log.info("Entered Into handleVetchatPlanActivation : username : {}", user != null ? user.getEmail() : null);
                chargebeeWebhookService.handleVetchatPlanActivation(planId, user);
            }
            if(event_type.equalsIgnoreCase("subscription_cancelled")) {
                log.info("Entered Into handleVetchatPlanCancellation : username : {}", user != null ? user.getEmail() : null);
                planService.handleVetchatPlanCancellation(planId, user);
            }

            infobibEventhandling(res, event_type, whStatus);
        } catch (Exception e) {
            log.error("chargebeeEventWebhookProcess Error :" + e.getLocalizedMessage());
            try {
                async.asynSlackMessage("Error While Update Chargebee Event()  :",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getLocalizedMessage());
            } catch (JSONException e1) {
                log.error("Error While Update Chargebee Event() :" + e.getLocalizedMessage());
            }
        } finally {
            boolean updateWebhookStatus = chargebeeWebhookService.updateWebHookStatus(whStatus);
            log.info("Event status Stored in DB : " + updateWebhookStatus == null ? "Not able to update in DB"
                    : updateWebhookStatus);
        }

    }

    private String toDate(long time) {
        Timestamp ts = new Timestamp(time * 1000);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(ts);
    }

    private boolean VoidInvoice(JSONObject res) {

        Boolean checkFun = true;
        String errorDescription = "";
        try {

            JSONObject content = res.getJSONObject("content");
            JSONObject sub = content.getJSONObject("subscription");
            String customerId = sub.getString("customer_id");
            String subId = sub.getString("id");

            Environment.configure(chargebeeSiteName, chargebeeSiteKey);

            ListResult invoiceList = Invoice.list().customerId().is(customerId).subscriptionId().is(subId).status()
                    .is(Invoice.Status.NOT_PAID).request();

            for (ListResult.Entry invoiceObj : invoiceList) {
                try {
                    Result result = Invoice.voidInvoice(invoiceObj.invoice().id()).request();
                    Invoice invo = result.invoice();
                    log.info("voidInvoice-id: " + invo.id());
                } catch (InvalidRequestException e) {
                    if (e.getMessage().toString().contains("credit note is applied")) {
                        async.asynSlackMessage("Error While Void Invoice : Customer have credit note in invoice ",
                                res.getString("id") + " : " + res.getString("event_type") + " :: customer id : "
                                        + customerId + " :: subscription id : " + subId + " :: invoice id : "
                                        + invoiceObj.invoice().id() + " :: Error msg : " + e.getLocalizedMessage());
                    } else {
                        async.asynSlackMessage("Error While Void Invoice :", res.getString("id") + " : "
                                + res.getString("event_type") + " : " + e.getLocalizedMessage());
                    }
                    checkFun = false;
                    errorDescription += e.getLocalizedMessage();
                }
            }

        } catch (Exception e) {
            log.error("Error While Void Invoice : " + e.getLocalizedMessage());
            try {
                async.asynSlackMessage("Error While Void Invoice :",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getLocalizedMessage());
            } catch (JSONException e1) {
                log.error("Error in Void Invoice : " + e.getLocalizedMessage());
            }
            checkFun = false;
            errorDescription += e.getLocalizedMessage();
        } finally {
            if (!checkFun) {
                try {
                    ErrorInfo erinfo = new ErrorInfo();

                    erinfo.setEvent_id(res.getString("id"));

                    erinfo.setDescription(errorDescription);

                    erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setFunction_type("VoidInvoice");

                    erinfo = errorinfoService.updateErrorInfo(erinfo);

                    log.info("update Error Info Status : " + erinfo == null);
                } catch (JSONException e) {
                    log.error("Json Error in Void Invoice : " + e.getLocalizedMessage());
                }
            }
        }
        return checkFun;
    }

    private boolean[] checkSimActivationAndVpm(JSONObject res) {

        boolean[] validToActivate = new boolean[2];
        try {
            JSONObject content = res.getJSONObject("content");
            // String event_id = res.getString("id");
            String event_type = res.getString("event_type");
            // String omitplan = om;
            // String vpmplan = getExternalConfigValue("vpmplan");
            String event_status = content.getJSONObject("subscription").getString("status");

            if ((!omitplan.contains(content.getJSONObject("subscription").getString("plan_id")))
                    && (event_status.equalsIgnoreCase("active") || event_status.equalsIgnoreCase("in_trial"))) {
                // !vpmplan.contains(content.getJSONObject("subscription").getString("plan_id"))&&

                if (content.getJSONObject("subscription").getBoolean("has_scheduled_changes") == false
                        && content.getJSONObject("subscription").has("addons")) {
                    JSONArray addons = content.getJSONObject("subscription").getJSONArray("addons");

                    for (int i = 0; i < addons.length(); i++) {
                        JSONObject addon = (JSONObject) addons.get(i);
                        String addon_id = addon.getString("id");

                        if (addonids.contains(addon_id))
                            validToActivate[0] = true;
                    }
                } else if (event_type.contains("subscription_resumed")
                        || event_type.contains("subscription_reactivated")) {
                    validToActivate[0] = true;
                }

            }
            validToActivate[1] = false; // currently not used, so by default set to false
//			else if (vpmplan.contains(content.getJSONObject("subscription").getString("plan_id"))) {
//				validToActivate[1] = true;
//			}

        } catch (Exception e) {
            log.error("Error in checkSimActivationAndVpm : " + e.getLocalizedMessage());
        }
        return validToActivate;
    }

    private String simActivation(JSONObject res, ChargebeeWebhooksStatus whStatus) {
        String cbId = "";
        String cbEmail = "";
        String mailPacket = "";

        // for error Info
        Boolean checkFun = true;
        String errorDescription = "";
        try {
            JSONObject content = res.getJSONObject("content");
            String event_id = res.getString("id");
            String event_type = res.getString("event_type");
            cbId = content.getJSONObject("customer").getString("id");
            cbEmail = content.getJSONObject("customer").getString("email");

            try {

                if (microserviceApiCall) {
                    whStatus.setEventProcess("activate verizon sim");
                    whStatus.setEventStatus("calling micro service for activate sim ");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
                    String urlParams = "";
                    String msurl = microservice_url + "/v3.0/activateverizon/?cbid=" + cbId + "&cbemail=" + cbEmail
                            + "&eventid=" + event_id + "&eventname=" + event_type;
                    log.info("activateverizon :" + msurl);

                    // Response from Txn_Service
                    String microServiceRes = helper.httpPOSTRequest(msurl, urlParams);

                    JSONObject microServiceResJson = new JSONObject(microServiceRes);

                    microServiceResJson = microServiceResJson.getJSONObject("response");

                    int status = microServiceResJson.getInt("Status");
                    // String msg = microServiceResJson.getString("Msg");

                    if (status > 0) {
                        whStatus.setStatus(true);
                        whStatus.setActivateSimStatus(1);
                        whStatus.setEventStatus("SIM activation successful ");
                    } else {
                        whStatus.setActivateSimStatus(0);
                        whStatus.setEventStatus("SIM activation unsuccessful  ");
                        checkFun = false;
                        errorDescription += "SIM activation unsuccessful ";
                    }

                } else {
                    whStatus.setEventStatus("calling amazon SQS for activate sim ");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

                    String amazonSQSAccessKey  =secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
                    String amazonSQSSecretKey = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
                    AWSCredentials credentials = new BasicAWSCredentials(amazonSQSAccessKey, amazonSQSSecretKey);
                    AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
                    AmazonSQS sqs = AmazonSQSClientBuilder.standard().withCredentials(credentialsProvider)
                            .withRegion(Regions.US_WEST_2).build();

                    mailPacket = "nimble|waggle|chargebeewebhooks|" + event_type + "|" + event_id + "|" + cbId + "|"
                            + cbEmail + "#";

                    log.info("sendEmailDataToQueue - Packet ::: " + mailPacket);

                    SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSQS_microserviceQueue_url,
                            mailPacket);
                    SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
                    String sequenceNumber = sendMessageResult.getSequenceNumber();
                    String messageId = sendMessageResult.getMessageId();

                    log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
                }

            } catch (Exception e) {
                whStatus.setEventStatus("Error in activate sim ");
                whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
                log.error("Error in simActivation : " + e.getLocalizedMessage());
                async.asynSlackMessage("Error in sim activation ",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getLocalizedMessage());
                checkFun = false;

                errorDescription += " Error : " + e.getMessage();
                return "Exception - checkChargebeewebhooks :\n" + e.getLocalizedMessage();
            }

            // check and remove user_cancel_feedback
            async.checkAndRemoveUserCancelFeedBack(cbId);

        } catch (JSONException e1) {
            // e1.printStackTrace();
            log.error("Json Error in simActivation" + e1.getLocalizedMessage());
            checkFun = false;
            errorDescription += " Error :" + e1.getLocalizedMessage();
        } finally {
            if (!checkFun) {
                try {
                    ErrorInfo erinfo = new ErrorInfo();

                    erinfo.setEvent_id(res.getString("id"));

                    erinfo.setDescription(errorDescription);

                    erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setFunction_type("Reactivation SIM");

                    erinfo = errorinfoService.updateErrorInfo(erinfo);

                    log.error("update Error Info Status : " + erinfo == null);
                } catch (JSONException e) {
                    log.error(" Error in finally : simActivation" + e.getLocalizedMessage());
                }
            }
        }
        return "Success";
    }

    private long Updateplanvsfeature(JSONObject res, ChargebeeWebhooksStatus whStatus, UserSubscription usrSub) {

        JSONObject subscription = new JSONObject();
        String customerEmail = new String();
        JSONArray addonList = new JSONArray();

        String cbId = "";
        boolean planUpgraded = false;
        Boolean vaildToSend = true;
        String planName = "NA";

        // for error Info
        Boolean checkFun = true;
        String errorDescription = "";

        Set<String> AddonSet = new HashSet<String>();

        HashMap<String, HashMap<String, String>> plans = new HashMap<String, HashMap<String, String>>();

        HashMap<String, HashMap<String, String>> addonInfo = new HashMap<String, HashMap<String, String>>();

        if (usrSub != null && !usrSub.getPlan_info().equals("NA"))
            plans = getMap(usrSub.getPlan_info());

        if (usrSub != null && !usrSub.getAddon_info().equals("NA"))
            addonInfo = getMap(usrSub.getAddon_info());

        long gateway_id = 0;

        try {
            JSONObject content = res.getJSONObject("content");

            // String event_id = res.getString("id");
            String event_type = res.getString("event_type");

            boolean isCancelled = event_type.equals("subscription_cancelled");

            customerEmail = content.getJSONObject("customer").getString("email");
            cbId = content.getJSONObject("customer").getString("id");
            subscription = content.getJSONObject("subscription");

            if (subscription.has("addons"))
                addonList = subscription.getJSONArray("addons");

            // String subStatus = subscription.getString("status");
            String subId = subscription.getString("id");

            planName = subscription.getString("plan_id");

            for (int i = 0; i < addonList.length(); i++) {
                JSONObject addon = (JSONObject) addonList.get(i);

                if (addon.getString("id").equalsIgnoreCase("setup_charges")
                        || addon.getString("id").equalsIgnoreCase("reactivation_charges")
                        || addon.getString("id").equalsIgnoreCase("reactivation-charges-onetime")
                        || addon.getString("id").equalsIgnoreCase("reactivation-charges-aud")
                        || addon.getString("id").equalsIgnoreCase("setup-charges-aud")
                        || addon.getString("id").equalsIgnoreCase("reactivation-charges-cad")
                        || addon.getString("id").equalsIgnoreCase("setup-charges-cad")) {
                    vaildToSend = true;
                    log.info("valid To Call Plan vs Featre Txn_Service API");
                } else if (addon.getString("id").equalsIgnoreCase("upgrade_charges")
                        || addon.getString("id").equalsIgnoreCase("upgrade-charges-aud")
                        || addon.getString("id").equalsIgnoreCase("upgrade-charges-cad")) {
//					planUpgraded = true;
                    vaildToSend = true;
                    log.info("valid To Call Plan vs feature Txn_Service API, with planUpgraded State");
                } else {
                    log.info("Addon Add into list : " + addon.getString("id"));
                    AddonSet.add(addon.getString("id"));
                }
            }

            if (content.has("invoice")) {
                JSONObject invoice = content.getJSONObject("invoice");
                JSONArray line_Item = invoice.getJSONArray("line_items");

                for (int i = 0; i < line_Item.length(); i++) {
                    JSONObject lineItem = line_Item.getJSONObject(i);

                    if (lineItem.getString("entity_type").equals("addon"))
                        AddonSet.add(lineItem.getString("entity_id"));
                }
            }

            /* get gateway id from hosted page */
            long userid = chargebeeWebhookService.getUserByCbId(cbId);
            long monitortype_id = chargebeeWebhookService.getMonitorTypeByPlanId(planName);
            String pType = chargebeeWebhookService.getPlanType(planName);
            if (pType != null && pType.equalsIgnoreCase("Combo-Plan"))
                gateway_id = chargebeeWebhookService.getHostedPage(userid);
            else
                gateway_id = chargebeeWebhookService.getHostedPage(userid, monitortype_id);
            log.info("Got gateway_id : " + gateway_id);

            if (gateway_id == 0) {
                gateway_id = gatewayService.getgatewayByMonitorId(userid, monitortype_id);
                log.info("gateway_id by monitorid : " + gateway_id);
            }

            String output = "NA";
            if (vaildToSend) {
                String Code = "";
                HashMap<String, String> planList = plans.get(subId);

                PlantoPeriod planToPeriodGlobal = chargebeeWebhookService.getPlanAndPeriodId(planName);
                if( planToPeriodGlobal == null ) {
                    log.info("plan_to_period not found :: plan_name : "+planName);
                    return 0;
                }
                PlanToMonitorType planToMonitorType = chargebeeWebhookService.getPlanToMonitorType( planToPeriodGlobal.getPlan_id() );
                if( planToMonitorType == null ) {
                    log.info("plan_to_monitortype not found :: plan_id : "+planToPeriodGlobal.getPlan_id());
                    return 0;
                }

                if (monitortype_id == 1) { // call userfeature for pet monitor

                    String planver = chargebeeWebhookService.getPlanVersionbyplanname(planName);

                    String msurl = "NA";

                    if(planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {

                        AllProductSubscription subProObj = chargebeeWebhookService
                                .checkProductSubscriptionStatus(subId);

                        if (subProObj != null) {
                            if (gateway_id == 0) {
                                gateway_id = subProObj.getGateway_id();
                            }
                        }

                        processProductSubscription( res, whStatus,"subscription_processing", gateway_id );

                        if( planToMonitorType.getPlan_id() != null && planToMonitorType.getPlan_id().getPlan_type().equalsIgnoreCase("Combo-Plan") ) {
                            if( isCancelled ) {
                                boolean status = chargebeeWebhookService.updateProductSubscriptionStatus(subId,"cancelled");
                                log.info("changed status cancelled in product_status : " + status);
                                boolean featureStatus = chargebeeWebhookService.deleteGatewayFeatureBySubId(subId);
                                log.info("changed status disable in gateway_feature : " + featureStatus);
                            } else {
                                UserV4 user = userService.verifyAuthV4("email", customerEmail);
                                if(user == null)
                                    return 0;
                                JResponse planServiceResponse = planService.assignComboPlan(user, 0, planName,subId);
                                log.info("assign feature for combo plan response : "+planServiceResponse.toString());
                            }
                        } else {
                            msurl = microservice_url + "/v4.0/gatewayfeaturewithmonitor?" + "cbplan=" + planName + "&chargbeeid="
                                    + cbId + "&eventtype=" + event_type + "&isupgrade=" + planUpgraded + "&cbemail="
                                    + customerEmail + "&gatewayid="+gateway_id+"&subId="+subId;
                            log.info("Call txnservice gatewayfeature API :" + msurl);
                        }

                    }else {
                        msurl = microservice_url + "/v4.0/userfeature?" + "cbplan=" + planName + "&chargbeeid="
                                + cbId + "&eventtype=" + event_type + "&isupgrade=" + planUpgraded + "&cbemail="
                                + customerEmail;
                        log.info("Call txnservice userfeature API :" + msurl);
                    }

                    if (planList == null || (!planList.get("PlanId").toLowerCase().equals(planName)
                            || ((!isCancelled && planList.get("Status").toLowerCase().equals("cancelled"))
                            || (isCancelled
                            && !planList.get("Status").toLowerCase().equals("cancelled")))) || event_type.contains("subscription_resumed")) {
                        String msPlanRes = helper.httpPOSTRequest(msurl, null);
                        log.info("Plan Url update : " + msPlanRes);

                        if (msPlanRes != null) {
                            JSONObject ms = new JSONObject(msPlanRes);
                            JSONObject resp = ms.getJSONObject("response");
                            if (resp.has("Code"))
                                Code = resp.getString("Code");
                        } else {
                            Code += " MAIN PLAN : SERVICE_UNAVAIABLE";

                            checkFun = false;
                            errorDescription += Code;
                        }
                    } else {
                        Code = "Already Updated";
                    }

                    output = "[" + Code + "]";

                    for (String Addon : AddonSet) {

                        if (addonInfo.containsKey(Addon)) {

                            HashMap<String, String> addonList1 = addonInfo.get(Addon);

                            log.info("RecurringType : " + addonList1.get("RecurringType") + " ");

                            if (addonList1.get("RecurringType").equals("Recurring")) {

                                if (((!isCancelled && addonList1.get("Status").toLowerCase().equals("cancelled"))
                                        || (isCancelled
                                        && !addonList1.get("Status").toLowerCase().equals("cancelled")))) {

                                    log.info("Addon : " + Addon);
                                } else {
                                    log.info("Addon Already Exists : " + Addon);
                                    continue;
                                }
                            }
                        }

                        String msAddonurl = "NA";
                        if(planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {
                            msAddonurl = microservice_url + "/v4.0/gatewayfeatureaddon?" + "cbaddon=" + Addon
                                    + "&chargbeeid=" + cbId + "&eventtype=" + event_type + "&cbemail=" + customerEmail + "&gatewayid="+gateway_id+"&subId="+subId;
                            log.info("Call txnservice gatewayfeature API :" + msurl);
                        }else {
                            msAddonurl = microservice_url + "/v4.0/userfeatureaddon?" + "cbaddon=" + Addon
                                    + "&chargbeeid=" + cbId + "&eventtype=" + event_type + "&cbemail=" + customerEmail;
                            log.info("Call txnservice userfeature API :" + msurl);
                        }


                        log.info("activateverizon :" + msAddonurl);

                        // Response from Txn_Service
                        String msAddonRes = helper.httpPOSTRequest(msAddonurl, null);
                        log.info("Addon Url update : " + msAddonRes);
                        String CodeA = "| Addon PLAN : SERVICE_UNAVAIABLE";

                        if (msAddonRes != null) {
                            JSONObject ms = new JSONObject(msAddonRes);
                            JSONObject resp = ms.getJSONObject("response");
                            if (resp.has("Code"))
                                CodeA = resp.getString("Code");
                        } else {
                            checkFun = false;
                            errorDescription += Code;
                        }

                        output = output + ",(" + CodeA + ")";
                    }

                    whStatus.setPlanfeatureStatus(output);

                    if (output.contains("Failure")) {
                        async.asynSlackMessage("Plan VS Feature : Update UserFeature",
                                res.getString("id") + " : " + res.getString("event_type") + " : " + output);

                        checkFun = false;
                        errorDescription += "Plan VS Feature : Update UserFeature";
                        return gateway_id;
                    }
                    if (output.contains("Success")) {

                        chargebeeWebhookService.deleteHostedPageId(userid, monitortype_id);

                    }
                    log.info("Plan And Addon Status : " + output);

                } else { // call gatewayfeature for non-petmonitor products
                    String msurl = microservice_url + "/v5.0/gatewayfeature";
                    log.info("Call txnservice gatewayfeature API :" + msurl);

                    if (planList == null || (!planList.get("PlanId").toLowerCase().equals(planName)
                            || ((!isCancelled && planList.get("Status").toLowerCase().equals("cancelled"))))) {

                        if( planToMonitorType.getPlan_id() != null && planToMonitorType.getPlan_id().getPlan_type().equalsIgnoreCase("Combo-Plan") ) {
                            UserV4 user = userService.verifyAuthV4("email", customerEmail);
                            if(user == null)
                                return 0;
                            JResponse planServiceResponse = planService.assignComboPlan(user, 0, planName,subId);
                            log.info("assign feature for combo plan response : "+planServiceResponse.toString());
                        } else {

                            PlantoPeriod planToPeriod = chargebeeWebhookService.getPlanAndPeriodId(planName);
                            Map<String, Object> postParam = new HashMap<>();
                            postParam.put("planid", planToPeriod.getPlan_id());
                            postParam.put("cbsubid", subId);
                            postParam.put("chargebeeid", cbId);
                            postParam.put("periodid", planToPeriod.getSub_period_id());
                            postParam.put("gatewayid", Arrays.asList(gateway_id));

                            JSONObject bodyJson = new JSONObject(postParam);

                            // Response from Txn_Service
                            String msPlanRes = helper.httpPOSTRequest(msurl, bodyJson.toString());
                            log.info("Plan Url update : " + msPlanRes);
                            Code = msPlanRes;
                        }
                    } else if (isCancelled) {
                        AllProductSubscription subProObj = chargebeeWebhookService
                                .checkProductSubscriptionStatus(subId);
                        gateway_id = subProObj.getGateway_id();
                        PlantoPeriod planToPeriod = chargebeeWebhookService.getPlanToPeriodByMonitorId(monitortype_id);
                        if (planToPeriod.getPlan_id() == 0) {
                            boolean status = chargebeeWebhookService.deleteGatewayFeature(gateway_id);
                            log.info("planid:0, Deleted status : " + status);
                            Code = "Success";
                        } else {

                            if( planToMonitorType.getPlan_id() != null && planToMonitorType.getPlan_id().getPlan_type().equalsIgnoreCase("Combo-Plan") ) {
                                boolean status = chargebeeWebhookService.updateProductSubscriptionStatus(subId,"cancelled");
                                log.info("changed status cancelled in product_status : " + status);
                                boolean featureStatus = chargebeeWebhookService.deleteGatewayFeatureBySubId(subId);
                                log.info("changed status disable in gateway_feature : " + featureStatus);
                            }

                            Map<String, Object> postParam = new HashMap<>();
                            postParam.put("planid", planToPeriod.getPlan_id());
                            postParam.put("cbsubid", "NA");
                            postParam.put("chargebeeid", cbId);
                            postParam.put("periodid", planToPeriod.getSub_period_id());
                            postParam.put("gatewayid", Arrays.asList(gateway_id));

                            JSONObject bodyJson = new JSONObject(postParam);

                            // Response from Txn_Service
                            String msPlanRes = helper.httpPOSTRequest(msurl, bodyJson.toString());
                            log.info("Plan Url update : " + msPlanRes);
                            Code = msPlanRes;
                        }
                    } else {
                        Code = "Already Updated";
                    }

                    output = "[" + Code + "]";

                    whStatus.setPlanfeatureStatus(output);

                    if (!output.contains("Success")) {
                        async.asynSlackMessage("Plan VS Feature : Update GatewayFeature",
                                res.getString("id") + " : " + res.getString("event_type") + " : " + output);

                        checkFun = false;
                        errorDescription += "Plan VS Feature : Update GatewayFeature";
                        return gateway_id;
                    } else {
                        if (!isCancelled) {
                            chargebeeWebhookService.deleteHostedPageId(userid, monitortype_id);
                        }
                    }
                    log.info("Plan And Addon Status : " + output);
                }
            }

        } catch (Exception e) {
            try {
                async.asynSlackMessage("Plan VS Feature : Update GatewayFeature",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getMessage());
            } catch (JSONException e1) {
                log.error("Updateplanvsfeature:Json Error:" + e1.getLocalizedMessage());
            }
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

            checkFun = false;
            errorDescription += " : " + e.getLocalizedMessage();
            log.error("Exception - checkChargebeewebhooks : " + e.getLocalizedMessage());
            return gateway_id;
        } finally {
            if (!checkFun) {
                try {
                    ErrorInfo erinfo = new ErrorInfo();

                    erinfo.setEvent_id(res.getString("id"));

                    erinfo.setDescription(errorDescription);

                    erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setFunction_type("Update User Feature");

                    erinfo = errorinfoService.updateErrorInfo(erinfo);

                    log.error("update Error Info Status : " + erinfo == null);
                } catch (JSONException e) {
                    log.error("Updateplanvsfeature: finally:" + e.getLocalizedMessage());
                }
            }
        }
        return gateway_id;
    }

    private String simActivationGateway(JSONObject res, ChargebeeWebhooksStatus whStatus,long gatewayId) {
        String cbId = "";
        String cbEmail = "";
        String mailPacket = "";

        // for error Info
        Boolean checkFun = true;
        String errorDescription = "";
        try {
            JSONObject content = res.getJSONObject("content");
            String event_id = res.getString("id");
            String event_type = res.getString("event_type");
            cbId = content.getJSONObject("customer").getString("id");
            cbEmail = content.getJSONObject("customer").getString("email");

            try {

                if (microserviceApiCall) {
                    whStatus.setEventProcess("activate verizon sim");
                    whStatus.setEventStatus("calling micro service for activate sim ");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

                    String urlParams = "";
                    String msurl = microservice_url + "/v3.0/activateverizonbygateway/?cbid=" + cbId + "&cbemail=" + cbEmail
                            + "&eventid=" + event_id + "&eventname=" + event_type + "&gatewayid="+gatewayId;
                    log.info("activateverizon :" + msurl);

                    // Response from Txn_Service
                    String microServiceRes = helper.httpPOSTRequest(msurl, urlParams);

                    JSONObject microServiceResJson = new JSONObject(microServiceRes);

                    microServiceResJson = microServiceResJson.getJSONObject("response");

                    int status = microServiceResJson.getInt("Status");
                    // String msg = microServiceResJson.getString("Msg");

                    if (status > 0) {
                        whStatus.setStatus(true);
                        whStatus.setActivateSimStatus(1);
                        whStatus.setEventStatus("SIM activation successful ");
                    } else {
                        whStatus.setActivateSimStatus(0);
                        whStatus.setEventStatus("SIM activation unsuccessful  ");
                        checkFun = false;
                        errorDescription += "SIM activation unsuccessful ";
                    }

                } else {
                    whStatus.setEventStatus("calling amazon SQS for activate sim ");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

                    String amazonSQSAccessKey = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_access_key");
                    String amazonSQSSecretKey = secretManagerService.getSecretValue(SQS_SECRET_NAME, "aws_sqs_secret_key");
                    AWSCredentials credentials = new BasicAWSCredentials(amazonSQSAccessKey, amazonSQSSecretKey);
                    AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
                    AmazonSQS sqs = AmazonSQSClientBuilder.standard().withCredentials(credentialsProvider)
                            .withRegion(Regions.US_WEST_2).build();

                    mailPacket = "nimble|waggle|chargebeewebhooks|" + event_type + "|" + event_id + "|" + cbId + "|"
                            + cbEmail + "#";

                    log.info("sendEmailDataToQueue - Packet ::: " + mailPacket);

                    SendMessageRequest sendMessageRequest = new SendMessageRequest(amazonSQS_microserviceQueue_url,
                            mailPacket);
                    SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
                    String sequenceNumber = sendMessageResult.getSequenceNumber();
                    String messageId = sendMessageResult.getMessageId();

                    log.info("SendMessage succeed with messageId " + messageId + ",sequence number " + sequenceNumber);
                }

            } catch (Exception e) {
                whStatus.setEventStatus("Error in activate sim ");
                whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
                log.error("Error in simActivation : " + e.getLocalizedMessage());
                async.asynSlackMessage("Error in sim activation ",
                        res.getString("id") + " : " + res.getString("event_type") + " : " + e.getLocalizedMessage());
                checkFun = false;

                errorDescription += " Error : " + e.getMessage();
                return "Exception - checkChargebeewebhooks :\n" + e.getLocalizedMessage();
            }

            // check and remove user_cancel_feedback
            async.checkAndRemoveUserCancelFeedBack(cbId);

        } catch (JSONException e1) {
            // e1.printStackTrace();
            log.error("Json Error in simActivation" + e1.getLocalizedMessage());
            checkFun = false;
            errorDescription += " Error :" + e1.getLocalizedMessage();
        } finally {
            if (!checkFun) {
                try {
                    ErrorInfo erinfo = new ErrorInfo();

                    erinfo.setEvent_id(res.getString("id"));

                    erinfo.setDescription(errorDescription);

                    erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                    erinfo.setFunction_type("Reactivation SIM");

                    erinfo = errorinfoService.updateErrorInfo(erinfo);

                    log.error("update Error Info Status : " + erinfo == null);
                } catch (JSONException e) {
                    log.error(" Error in finally : simActivation" + e.getLocalizedMessage());
                }
            }
        }
        return "Success";
    }

    private Boolean deactivateDevicebasedGateway(String chargebeeId, String eventId, String email,
                                                 ChargebeeWebhooksStatus whStatus, long gatewayId) {
        log.info(" Entered disable gateway gatewayId :" + gatewayId);

        // for error Info
        Boolean checkFun = true;
        String errorDescription = "";
        try {
            CbActivateCancelSubStatus cSubs = new CbActivateCancelSubStatus();

            cSubs.setEventId(eventId);
            cSubs.setEmail(email);
            cSubs.setChargebeeId(chargebeeId);
            cSubs.setAction("cancellation");

            cSubs.setUserId("NA");
            boolean savedCancelActivateStatus = chargebeeWebhookService.saveCancelActivateStatusData(cSubs);
            log.info(" saved cancel or activate status : " + savedCancelActivateStatus);

            if (!savedCancelActivateStatus) {
                checkFun = false;
                errorDescription += " Failure :  saveCancelActivateStatusData";
            }

            boolean checkSimStatus = disableGatewayFeature(whStatus,gatewayId);

            if (!checkSimStatus) {
                checkFun = false;
                errorDescription += "| Failure :  disableUser";
            }

        } catch (Exception e) {
            log.error("deactivateGateway: " + e.getLocalizedMessage());
            whStatus.setEventStatus("Error in deactivateGateway");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
            checkFun = false;
            errorDescription += " Failure :  deactivateGateway " + e.getLocalizedMessage();

        } finally {
            if (!checkFun) {
                ErrorInfo erinfo = new ErrorInfo();

                erinfo.setEvent_id(eventId);

                erinfo.setDescription(errorDescription);

                erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                erinfo.setFunction_type("Deactivation SIM");

                erinfo = errorinfoService.updateErrorInfo(erinfo);

                log.error("update Error Info Status : " + erinfo == null);
            }
        }
        return checkFun;
    }

    private Boolean deactivateGateway(String chargebeeId, String eventId, String email,
                                      ChargebeeWebhooksStatus whStatus) {
        log.info(" Entered disable gateway eventId :" + eventId);

        // for error Info
        Boolean checkFun = true;
        String errorDescription = "";
        try {
            CbActivateCancelSubStatus cSubs = new CbActivateCancelSubStatus();

            cSubs.setEventId(eventId);
            cSubs.setEmail(email);
            cSubs.setChargebeeId(chargebeeId);
            cSubs.setAction("cancellation");

            cSubs.setUserId("NA");
            boolean savedCancelActivateStatus = chargebeeWebhookService.saveCancelActivateStatusData(cSubs);
            log.info(" saved cancel or activate status : " + savedCancelActivateStatus);

            if (!savedCancelActivateStatus) {
                checkFun = false;
                errorDescription += " Failure :  saveCancelActivateStatusData";
            }

            boolean checkSimStatus = disableUser(whStatus);

            if (!checkSimStatus) {
                checkFun = false;
                errorDescription += "| Failure :  disableUser";
            }

        } catch (Exception e) {
            log.error("deactivateGateway: " + e.getLocalizedMessage());
            whStatus.setEventStatus("Error in deactivateGateway");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
            checkFun = false;
            errorDescription += " Failure :  deactivateGateway " + e.getLocalizedMessage();

        } finally {
            if (!checkFun) {
                ErrorInfo erinfo = new ErrorInfo();

                erinfo.setEvent_id(eventId);

                erinfo.setDescription(errorDescription);

                erinfo.setCreatedon(IrisUtil.getCurrentTimeUTC());

                erinfo.setUpdatedon(IrisUtil.getCurrentTimeUTC());

                erinfo.setFunction_type("Deactivation SIM");

                erinfo = errorinfoService.updateErrorInfo(erinfo);

                log.error("update Error Info Status : " + erinfo == null);
            }
        }
        return checkFun;
    }

    private boolean saveOrUpdateUserSubscription(JSONObject res, UserSubscription usrSub, long gateway_id) {

        String customerId = "";
        try {
            JSONObject content = res.getJSONObject("content");
            JSONObject customer = content.getJSONObject("customer");
            customerId = customer.getString("id");

            if (usrSub == null) {
                usrSub = UpdateObject(res, new UserSubscription(), gateway_id);
            } else {
                usrSub = UpdateObject(res, usrSub, gateway_id);

                UserSubscription usrSub1 = chargebeeWebhookService.getUser_subscription(customerId);
                if (usrSub1 != null)
                    usrSub.setId(usrSub1.getId());
            }

            long userid = userService.getUserByChargebeeId(customerId);
            usrSub.setUser_id(userid);

            chargebeeWebhookService.saveOrUpdateUser_subscription(usrSub);

            return true;
        } catch (Exception e) {
            log.error("saveOrUpdateUserSubscription : " + e.getLocalizedMessage());
            return false;
        }
    }

    private boolean saveOrUpdateDeviceSubscription(JSONObject res, DeviceSubscription deviceSub, long gateway_id, String planVer, String pType ) {

        String customerId = "";
        try {
            JSONObject content = res.getJSONObject("content");
            JSONObject customer = content.getJSONObject("customer");
            customerId = customer.getString("id");

            if (deviceSub == null) {
                deviceSub = UpdateDeviceObject(res, new DeviceSubscription(), gateway_id);

                HashMap<String, String> gatewayDetail = gatewayService.getGatewayDetail(gateway_id);

                if(gatewayDetail != null) {
                    try {
                        Timestamp instal = Timestamp.valueOf(gatewayDetail.containsKey("installed_date") ? gatewayDetail.get("installed_date"):"1753-01-01 00:00:00");
                        deviceSub.setInstal_date(instal);
                        deviceSub.setIsgps(Boolean.valueOf(gatewayDetail.containsKey("isgps") ? gatewayDetail.get("isgps"): "0"));
                        deviceSub.setSales_channel(gatewayDetail.containsKey("order_channel") ? gatewayDetail.get("order_channel"): "NA");
                    }catch (Exception e) {
                        log.error("UpdateDeviceObject : " + e.getLocalizedMessage());
                    }
                }
            } else {
                deviceSub = UpdateDeviceObject(res, deviceSub, gateway_id);

                DeviceSubscription deviceSub1 = chargebeeWebhookService.getDevice_subscription(gateway_id);
                if (deviceSub1 != null)
                    deviceSub.setId(deviceSub1.getId());
            }

            long userid = userService.getUserByChargebeeId(customerId);
            deviceSub.setUser_id(userid);
            deviceSub.setGateway_id(gateway_id);
            chargebeeWebhookService.saveOrUpdateDevice_subscription(deviceSub);

            if(planVer.equalsIgnoreCase("v2"))
                chargebeeWebhookService.updateDevicesubscriptionForV2Plan(deviceSub);

            return true;
        } catch (Exception e) {
            log.error("saveOrUpdateUserSubscription : " + e.getLocalizedMessage());
            return false;
        }
    }

    public ChargebeeWebhooksStatus insertOrUpdateSubscription(JSONObject res, ChargebeeWebhooksStatus whStatus,
                                                              String sub_event_type, long gateway_id) {
        String sub_id = "";
        String plan_id = "";
        try {
            AllSubscription subscription = new AllSubscription();

            AllProductSubscription subProduct = new AllProductSubscription();

            JSONObject content = res.getJSONObject("content");
            JSONObject customer = null;
            String event_type = res.getString("event_type");
            try {
                customer = content.getJSONObject("customer");

//				if(sub_event_type.equalsIgnoreCase("sub_upgraded"))
//					subscription.setEvent_type(sub_event_type);
//				else
//					subscription.setEvent_type(event_type);

                if (customer.has("meta_data")) {
                    JSONObject metaData = customer.getJSONObject("meta_data");
                    subscription.setMetaData(metaData.toString());
                } else {
                    subscription.setMetaData("");
                }

            } catch (JSONException e) {
                log.error("Error in insertOrUpdateSubscription:metadata:" + e.getLocalizedMessage());
            }
            Credits credits = new Credits();
            JSONObject sub = content.getJSONObject("subscription");
            sub_id = sub.getString("id");
            plan_id = sub.getString("plan_id");
            String pType = chargebeeWebhookService.getPlanType(plan_id);

            if (!plan_id.equalsIgnoreCase("chum")) {
                if (content.has("invoice")) {
                    JSONObject invoiceJSON = content.getJSONObject("invoice");
                    String state_code = "NA";
                    if (invoiceJSON.has("billing_address")) {
                        JSONObject billJSON = invoiceJSON.getJSONObject("billing_address");

                        if (billJSON.has("state_code"))
                            state_code = billJSON.getString("state_code");
                    }
                    subscription.setState_code(state_code);
                    subscription.setCurrency_code(invoiceJSON.getString("currency_code"));
                    subscription.setExchange_rate(Float.parseFloat(invoiceJSON.getString("exchange_rate")));
                } else {
                    String curr_code = "USD";
                    if (sub.has("currency_code"))
                        curr_code = sub.getString("currency_code");

                    JSONObject cxJSON = content.getJSONObject("customer");
                    String state_code = "NA";

                    if (cxJSON.has("billing_address")) {
                        JSONObject billJSON = cxJSON.getJSONObject("billing_address");

                        if (billJSON.has("state_code"))
                            state_code = billJSON.getString("state_code");
                    }

                    subscription.setState_code(state_code);
                    subscription.setCurrency_code(curr_code);
                }

                String allSubUpdateStatus = "2";
                String historySubsUpdateStatus = "2";
                String creditsUpdateStatus = "2";
                String invoiceUpdateStatus = "2";

                subscription.setSubscriptionId(sub.getString("id"));
                subscription.setPlanId(sub.getString("plan_id"));
                subscription.setSubscriptionStatus(sub.getString("status"));
                try {
                    if (sub.has("mrr"))
                        subscription.setMrr(sub.getLong("mrr"));
                    if(sub.getString("status").equalsIgnoreCase("cancelled")) //some time mrr event not received after cancelling, so manually setting to 0
                        subscription.setMrr(0);
                } catch (Exception e) {
                    subscription.setMrr(0);
                }

                try {
                    subscription.setSubscriptionStartedAt(toDate(sub.getInt("started_at")));
                    subscription.setSubscriptionCreatedAt(toDate(sub.getInt("created_at")));
                } catch (Exception e) {
                    subscription.setSubscriptionCreatedAt(toDate(sub.getInt("started_at")));
                }

                try {
                    if (sub.getString("status").equalsIgnoreCase("in_trial")) {
                        subscription.setSubscriptionActivatedAt(toDate(sub.getInt("trial_start")));
                        subscription.setSubscriptionStartedAt(toDate(sub.getInt("trial_start")));
                    } else {
                        subscription.setSubscriptionActivatedAt(toDate(sub.getInt("activated_at")));
                        subscription.setSubscriptionStartedAt(toDate(sub.getInt("current_term_start")));

                    }
                } catch (Exception e) {
                    subscription.setSubscriptionActivatedAt(toDate(sub.getInt("started_at")));
                }

                subscription.setUpdatedDate(toDate(sub.getInt("updated_at")));
                subscription.setUpdatedIndb(currentDate());
                subscription.setPlanAmount(String.valueOf(sub.getInt("plan_amount")));
                subscription.setEnable(1);

                long monitorType = 1;
                if (subscription.getPlanId() != null && !subscription.getPlanId().equalsIgnoreCase("NA")
                        && !subscription.getPlanId().equals("")) {
                    if ((pType != null && pType.equalsIgnoreCase("Combo-Plan")) ||subscription.getPlanId().contains("flexi")) {
                        if(gateway_id>0)
                            monitorType = gatewayService.getgatewayById(gateway_id);
                        else {
                            String mTypeIds = planService.getPlanConfig(subscription.getPlanId());
                            if(!subscription.getPlanId().contains("flexi") && mTypeIds.contains("11")){
                                monitorType = 11;
                            }
                        }

                    }
                    else
                        monitorType = chargebeeWebhookService.getMonitorTypeByPlanId(subscription.getPlanId());
                    subscription.setMonitor_type(monitorType);
                }

                try {
                    if (subscription.getSubscriptionStatus() != null) {
                        String res_date = chargebeeWebhookService.getResumedatebySubId(subscription.getSubscriptionId());
                        if (res_date != null) {
                            subscription.setResume_date(res_date);
                        }else {
                            subscription.setResume_date("1753-01-01 00:00:00");
                        }
                    }
                } catch (Exception e) {
                    subscription.setResume_date("1753-01-01 00:00:00");
                }

                try {
                    subscription.setQuantity(sub.getLong("plan_quantity"));
                } catch (Exception e) {
                    subscription.setQuantity(0);
                }

                if (sub.getBoolean("deleted"))
                    subscription.setIsDeleted(1);
                else
                    subscription.setIsDeleted(0);

                if (subscription.getPlanId().contains("chum")
                        && !subscription.getPlanId().equalsIgnoreCase("chum-plus-monthly"))
                    subscription.setSubscriptionStatus("Inactive");

                String planPeriod = subscription.getPlanId();
                planPeriod = chargebeeWebhookService.getPlanToPeriod(subscription.getPlanId());
                subscription.setPlanPeriod(planPeriod);

                try {
                    subscription.setNextBillingAt(toDate(sub.getLong("next_billing_at")));
                } catch (Exception e) {
                    subscription.setNextBillingAt("1753-01-01 00:00:00");
                }

                try {
                    subscription.setSubscriptionCancelledAt(toDate(sub.getInt("cancelled_at")));
                } catch (Exception e) {
                    subscription.setSubscriptionCancelledAt("1753-01-01 00:00:00");
                }

                try {
                    subscription.setTrialStart(toDate(sub.getInt("trial_start")));
                } catch (Exception e) {
                    subscription.setTrialStart("1753-01-01 00:00:00");
                }

                try {
                    subscription.setTrialEnd(toDate(sub.getInt("trial_end")));
                } catch (Exception e) {
                    subscription.setTrialEnd("1753-01-01 00:00:00");
                }

                try {
                    JSONArray addons = sub.getJSONArray("addons");
                    String addonStr = "";
                    for (int itr = 0; itr < addons.length(); itr++) {
                        JSONObject o = addons.getJSONObject(itr);
                        addonStr += o.getString("id") + "," + o.getString("quantity") + ",";
                    }
                    addonStr = addonStr.substring(0, addonStr.lastIndexOf(','));
                    subscription.setAddons(addonStr);
                } catch (Exception e) {
                    subscription.setAddons("NA");
                }

                subscription.setCustomerId(customer.getString("id"));
                try {
                    subscription.setBillingEmail(customer.getString("email"));
                } catch (Exception e) {
                    subscription.setBillingEmail("NA");
                }
                credits.setChargebee_id(customer.getString("id"));
                try {
                    credits.setPromotional_credits(customer.getInt("promotional_credits") / 100.0);
                } catch (Exception e) {
                    credits.setPromotional_credits(0);
                }

                try {
                    credits.setRefundable_credits(customer.getInt("refundable_credits") / 100.0);
                } catch (Exception e) {
                    credits.setRefundable_credits(0);
                }

                try {
                    boolean isSubscriptionAvailable = false;

                    if (monitorType == 1) {

                        if(subscription.getSubscriptionStatus() != null &&
                                (event_type.equalsIgnoreCase("subscription_renewed") ||
                                        event_type.equalsIgnoreCase("subscription_resumed")) &&
                                (subscription.getSubscriptionStatus().equalsIgnoreCase("active") ||
                                        subscription.getSubscriptionStatus().equalsIgnoreCase("non_renewing") ||
                                        subscription.getSubscriptionStatus().equalsIgnoreCase("in_trial"))) {
                            try {
                                chargebeeWebhookService.savePauseHistorysStatus(subscription.getSubscriptionId());

                                subscription.setSubscriptionStartedAt(currentDate());

                            } catch (Exception e) {
                                log.error("Error in pause hisroty update : " + e.getLocalizedMessage());
                            }
                        }

                        String planver = chargebeeWebhookService.getPlanVersionbyplanname(subscription.getPlanId());

                        //Remove vetchat plan
                        if((pType.equalsIgnoreCase("Combo-Plan")||(subscription.getPlanId().contains("flexi")))
                                && subscription.getSubscriptionStatus().equalsIgnoreCase("cancelled")) {
                            chargebeeWebhookService.deleteVetChatSub(11, subscription.getSubscriptionId());
                        }
                        if (planver != null && !planver.equalsIgnoreCase("NA") && planver.equalsIgnoreCase("V3")) {

                            AllProductSubscription subProObj = chargebeeWebhookService
                                    .checkProductSubscriptionStatus(subscription.getSubscriptionId());

                            subProduct = setDataAllProdSub(subscription);

                            if (subProObj != null) {
                                if (gateway_id == 0) {
                                    gateway_id = subProObj.getGateway_id();
                                }
                                subProduct.setId(subProObj.getId());

                                if (!subProObj.getPlanPeriod().equalsIgnoreCase(subscription.getPlanPeriod())
                                        && subscription.getSubscriptionStatus().equalsIgnoreCase("ACTIVE")
                                        && !subscription.getPlanPeriod().equalsIgnoreCase("NA")) {
                                    chargebeeWebhookService.savePauseHistorysStatus(subscription.getSubscriptionId());
                                    try {
                                        if (!subscription.getSubscriptionId().substring(0, 3).equalsIgnoreCase("RE-")) {
                                            com.chargebee.models.Subscription
                                                    .removeScheduledPause(subscription.getSubscriptionId()).request();
                                        }
                                    } catch (Exception e) {
                                        log.error("Error in resume sub update : " + e.getLocalizedMessage());
                                    }
                                }
                            }
                            if((pType.equalsIgnoreCase("Combo-Plan")||(subscription.getPlanId().contains("flexi"))) && subProObj != null && subProObj.getMonitor_type() != 11)
                                subProduct.setGateway_id(subProObj.getGateway_id());
                            else
                                subProduct.setGateway_id(gateway_id);
                            log.info("gateway_id : " + gateway_id);

                            chargebeeWebhookService.insertAllProductSubscription(subProduct);
                            if(gateway_id>0)
                                async.updateDuplicateAllProductSubscription(subProduct);

                            if((subscription.getPlanId().contains("flexi") || (pType != null && pType.equalsIgnoreCase("Combo-Plan")))
                                    && !sub.getString("status").toLowerCase().contains("cancelled")) {
                                // For flexi plan & combo plan : assign emergency fund
                                String mTypeIds = planService.getPlanConfig(subscription.getPlanId());
                                if(mTypeIds.contains("11"))
                                    planService.saveAllChargebeeSubs(0, 11, subProduct);
                            }

                            if(sub.getString("status").toLowerCase().contains("cancel")
                                    && saveCancelledSubscriptionHistory(subProduct)) {
                                log.info("Successfully saved the cancelled subscription history");
                            }
                            else {
                                log.info("Failed to save the cancelled subscription history");
                            }

                        } else {

                            AllSubscription subObj = chargebeeWebhookService
                                    .checkSubscriptionStatus(subscription.getSubscriptionId());

                            if (subObj != null) {
                                if (!subObj.getPlanPeriod().equalsIgnoreCase(subscription.getPlanPeriod())
                                        && subscription.getSubscriptionStatus().equalsIgnoreCase("ACTIVE")
                                        && !subscription.getPlanPeriod().equalsIgnoreCase("NA")) {
                                    // Workarround - During subscription payment page- user puts app in background.
                                    // app not able to call generate coupon
                                    // to handle this we are removing existing user credits eg: Halfyearly plan
                                    // upgraded to yearly. but yearly credit not updated
                                    // means. we will remove half-yearly credit. irisservice will generate credit
                                    // for yearly
                                    chargebeeWebhookService.deleteAdditionalBenefits(subscription.getCustomerId(),
                                            subscription.getPlanPeriod());

                                    chargebeeWebhookService.savePauseHistorysStatus(subscription.getSubscriptionId());
                                    try {
                                        if (!subscription.getSubscriptionId().substring(0, 3).equalsIgnoreCase("RE-")) {
                                            com.chargebee.models.Subscription
                                                    .removeScheduledPause(subscription.getSubscriptionId()).request();
                                        }
                                    } catch (Exception e) {
                                        log.error("Error in resume sub update : " + e.getLocalizedMessage());
                                    }
                                }
                                subscription.setId(subObj.getId());

                                if (subscription.getCurrency_code().equalsIgnoreCase("NA")) {
                                    subscription.setCurrency_code(subObj.getCurrency_code());
                                    subscription.setExchange_rate(subObj.getExchange_rate());
                                    subscription.setState_code(subObj.getState_code());
                                } else if (!content.has("invoice")) {
                                    subscription.setExchange_rate(subObj.getExchange_rate());
                                }
                                isSubscriptionAvailable = true;

                            }

                            allSubUpdateStatus = chargebeeWebhookService.insertAllSubscription(subscription,
                                    isSubscriptionAvailable);
                            async.updateDuplicateAllCBSubscription(subscription);

                            if(sub.getString("status").toLowerCase().contains("cancel")
                                    && saveCancelledSubscriptionHistory(subscription)) {
                                log.info("Successfully saved the cancelled subscription history");
                            }
                            else {
                                log.info("Failed to save the cancelled subscription history");
                            }

                        }
                    } else {
                        AllProductSubscription subProObj = chargebeeWebhookService
                                .checkProductSubscriptionStatus(subscription.getSubscriptionId());

                        subProduct = setDataAllProdSub(subscription);

                        if (subProObj != null) {
                            if (gateway_id == 0) {
                                gateway_id = subProObj.getGateway_id();
                            }
                            subProduct.setId(subProObj.getId());
                        }

                        if (pType != null && pType.equalsIgnoreCase("Combo-Plan") && !sub.getString("status").toLowerCase().contains("cancelled")) {
                            // To assign emergency fund
                            String mTypeIds = planService.getPlanConfig(subscription.getPlanId());
                            if(mTypeIds.contains("11"))
                                planService.saveAllChargebeeSubs(0, 11, subProduct);
                        }

                        subProduct.setGateway_id(gateway_id);
                        log.info("gateway_id : " + gateway_id);

                        chargebeeWebhookService.insertAllProductSubscription(subProduct);
                        if(gateway_id>0)
                            async.updateDuplicateAllProductSubscription(subProduct);

                        if(sub.getString("status").toLowerCase().contains("cancel")
                                && saveCancelledSubscriptionHistory(subProduct)) {
                            log.info("Successfully saved the cancelled subscription history");
                        }
                        else {
                            log.info("Failed to save the cancelled subscription history");
                        }
                    }

                    Subscription subHistory = setData(subscription);

                    historySubsUpdateStatus = chargebeeWebhookService.insertHistorySubscription(subHistory,
                            isSubscriptionAvailable);

                    whStatus.setEventStatus("Insert or Update Subscription completed.");
                    whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

                } catch (Exception e) {
                    log.error(e.getLocalizedMessage());
                }
                try {
                    creditsUpdateStatus = chargebeeWebhookService.insertCredit(credits);

                } catch (Exception e) {
                    log.error("insertCredit : " + e.getLocalizedMessage());
                }

                if (subscription.getPlanId().contains("flexi") || (pType != null && pType.equalsIgnoreCase("Combo-Plan"))) {
                    chargebeeWebhookService.updateComboPlanStatus(subProduct);
                }

                whStatus.setSubscriptionInsert(allSubUpdateStatus + "," + historySubsUpdateStatus + ","
                        + creditsUpdateStatus + "," + invoiceUpdateStatus);
            }else
                whStatus.setSubscriptionInsert("chum ignored");
        } catch (Exception e) {
            log.error("Error in insertOrUpdateSubscription:"+sub_id + e.getLocalizedMessage());
        }

        return whStatus;
    }

    private void insertOrUpdateCredits(JSONObject content, ChargebeeWebhooksStatus whStatus) {
        try {
            JSONObject customer = content.getJSONObject("customer");
            Credits credits = new Credits();
            credits.setChargebee_id(customer.getString("id"));

            String allSubUpdateStatus = "2";
            String historySubsUpdateStatus = "2";
            String creditsUpdateStatus = "2";
            String invoiceUpdateStatus = "2";

            try {
                credits.setPromotional_credits(customer.getInt("promotional_credits") / 100.0);
            } catch (JSONException e) {
                credits.setPromotional_credits(0);
            }

            try {
                credits.setRefundable_credits(customer.getInt("refundable_credits") / 100.0);
            } catch (JSONException e) {
                credits.setRefundable_credits(0);
            }

            creditsUpdateStatus = chargebeeWebhookService.insertCredit(credits);
            whStatus.setEventStatus("insert or update credits successful");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

            if (creditsUpdateStatus.equalsIgnoreCase("1"))
                whStatus.setStatus(true);

            whStatus.setSubscriptionInsert(allSubUpdateStatus + "," + historySubsUpdateStatus + ","
                    + creditsUpdateStatus + "," + invoiceUpdateStatus);

        } catch (Exception e) {
            log.error("Error in insertOrUpdate Credits :" + e.getLocalizedMessage());
            whStatus.setEventStatus("Error in insert or update credits ");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
        }
    }

    private void CreateShopifyOrders(JSONObject res) {

        try {

            JSONObject content = res.getJSONObject("content");
            // JSONArray addonList = null;

            JSONObject orders = content.getJSONObject("order");

            Environment.configure(chargebeeSiteName, chargebeeSiteKey);
            Result cbresult = Customer.retrieve(orders.getString("customer_id")).request();
            Customer customer1 = cbresult.customer();

            String customerEmail = customer1.email();

//			if (orders.has("order_line_items"))
//				addonList = subscription.getJSONArray("addons");

            ArrayList<CBShopifyOrders> cbShopifyOrdList = externalConfigService.getCBShopifyOrderInfo();

            Map<String, CBShopifyOrders> resultsMap = new HashMap<String, CBShopifyOrders>();
            for (CBShopifyOrders ord : cbShopifyOrdList)
                resultsMap.put(ord.getCbtag().toLowerCase(), ord);

            if (orders.has("order_line_items")) {
                for (int i = 0; i < orders.getJSONArray("order_line_items").length(); i++) {
                    JSONObject addon = (JSONObject) orders.getJSONArray("order_line_items").get(i);

                    boolean iserror = false;
                    String slackMsg = "";

                    while (resultsMap.containsKey(addon.getString("entity_id").toLowerCase())) {

                        try {
                            CBShopifyOrders shopifyOrd = resultsMap.get(addon.getString("entity_id").toLowerCase());

                            String customerReq = CreateCustomerContent(cbresult, res);
                            if (customerReq.contains("Error:")) {
                                log.error("ShopifyOrderCreateEnable : " + ShopifyOrderCreateEnable);
                                slackMsg += "customer Request : " + customerReq + "\r\n";
                                iserror = true;
                            }

                            String customerRes = helper.ShopifyCreateOrUpdateRequest("customers.json", customerReq, 1);
                            if (customerRes.contains("Error:")) {

                                try {
                                    if (customerRes.contains("Error:422")) {
                                        String customerReqest = helper.ShopifygetURL(
                                                "customers/search.json?query=email:" + customer1.email().trim());

                                        JSONObject cus = new JSONObject(customerReqest);
                                        JSONObject customer = (JSONObject) cus.getJSONArray("customers").get(0);

                                        JSONObject cusjs = new JSONObject();
                                        cusjs.put("customer", customer);
                                        customerRes = cusjs.toString();

                                        // customerRes = _helper.ShopifyCreateOrUpdateRequest(
//												"customers/" + customer.getLong("id") + ".json", customerReq, 2);

                                    } else {
                                        slackMsg += "customer Response (warn): " + customerRes + "\r\n";
                                        customerRes = "{\"customer\":{\r\n\"email\": \"" + customerEmail + "\"\r\n}}";
                                    }
                                } catch (Exception e) {
                                    slackMsg += "customer Response : " + customerRes + "\r\n" + e.getLocalizedMessage();
                                }

                                iserror = true;
                            }

                            String variantsRes = helper
                                    .ShopifygetURL("variants/" + shopifyOrd.getVariantId() + ".json");
                            if (variantsRes.contains("Error:")) {
                                slackMsg += "variants Response " + variantsRes + "\r\n";
                                iserror = true;
                            }

                            String ordersReq = CreateOrderContent(variantsRes, customerRes, shopifyOrd, customer1,
                                    addon, orders);
                            if (ordersReq.contains("Error:")) {
                                slackMsg += "Orders Request : " + ordersReq + "\r\n";
                                iserror = true;
                            }

                            String orderRes = helper.ShopifyCreateOrUpdateRequest("orders.json", ordersReq, 1);
                            if (ordersReq.contains("Error:")) {
                                slackMsg += "Orders Response : " + orderRes + "\r\n";
                                iserror = true;
                            } else {
                                log.info("Order  Inserted :"
                                        + new JSONObject(orderRes).getJSONObject("order").getInt("id"));
                                slackMsg += " : Order  Inserted :"
                                        + new JSONObject(orderRes).getJSONObject("order").getInt("id");
                                iserror = true;
                            }
                            break;
                        } catch (Exception e) {
                            break;
                        } finally {

                            if (iserror) {
                                async.asynSlackMessage("Create Shopify Orders : ",
                                        res.getString("id") + " : " + res.getString("event_type") + " : " + slackMsg);
                            }

                        }
                    }

                }
            }

        } catch (Exception e) {
            log.info("Create Shopify Orders : " + e.getLocalizedMessage());
        }
    }

    private void infobibEventhandling(JSONObject res, String event_type, ChargebeeWebhooksStatus whStatus) {

        try {
            if (event_type.equalsIgnoreCase("customer_created")) {
                String url = infobip_url + "/people/2/persons";

                JSONObject content = res.getJSONObject("content");
                String customerEmail = content.getJSONObject("customer").getString("email");
                String customerFirstname = content.getJSONObject("customer").getString("first_name");
                String customerLastname = content.getJSONObject("customer").getString("last_name");
                String customerphone = content.getJSONObject("customer").getString("phone");
                String customerId = content.getJSONObject("customer").getString("id");
                String formattedNumber = customerphone.replaceAll("[^0-9]", "");

                JSONObject requestBody = new JSONObject();
                requestBody.put("externalId", customerId);
                requestBody.put("firstName", customerFirstname);
                requestBody.put("lastName", customerLastname);

                JSONObject contactInformation = new JSONObject();

                JSONArray emailArray = new JSONArray();
                JSONObject emailObject = new JSONObject();
                emailObject.put("address", customerEmail);
                emailArray.put(emailObject);
                contactInformation.put("email", emailArray);

                // Creating phone array
                JSONArray phoneArray = new JSONArray();
                JSONObject phoneObject = new JSONObject();
                phoneObject.put("number", formattedNumber);
                phoneArray.put(phoneObject);
                contactInformation.put("phone", phoneArray);

                requestBody.put("contactInformation", contactInformation);

                String response1 = helper.httpPOSTRequestV3(url, infobip_app_key, requestBody.toString());

            } else {
                String convertEvent = event_type.replace("_", "");

                JSONObject content = res.getJSONObject("content");
                String url = "";
                String subId = "";
                String planId = "";
                UserV4 user = userService.verifyAuthV4("chargebeeid",whStatus.getChargebeeId());
                if (user != null) {
                    String customerId = user.getEmail();
                    url = infobip_url + "/peopleevents/2/persons/" + customerId + "/definitions/" + convertEvent + "/events?identifierType=EMAIL";

                    JSONObject properties = new JSONObject();
                    JSONObject requestBody = new JSONObject();

                    if (!event_type.equalsIgnoreCase("card_expired") && !event_type.equalsIgnoreCase("card_updated") && !event_type.equalsIgnoreCase("card_expired_reminder")) {
                        if (!content.isNull("subscription")) {
                            JSONObject subscrip = content.getJSONObject("subscription");
                            subId = subscrip.getString("id");
                            planId = subscrip.getString("plan_id");
                        }
                        properties.put("subscriptionId", subId);
                        properties.put("planName", planId);
                        properties.put("createdDate", IrisUtil.getCurrentTimeUTC());
                    }

                    requestBody.put("properties", properties);
                    String response1 = helper.httpPOSTRequestV3(url, infobip_app_key, requestBody.toString());
                }
            }
        } catch (Exception e) {
            log.error("Exception in infobibEventhandling :" + e.getLocalizedMessage());
        }
    }

    private HashMap<String, HashMap<String, String>> getMap(String plan_info) {
        HashMap<String, HashMap<String, String>> objectH = new HashMap<String, HashMap<String, String>>();

        try {
            JSONObject jobject = new JSONObject(plan_info);

            Iterator<String> keysItr = jobject.keys();
            while (keysItr.hasNext()) {
                String key = keysItr.next();
                JSONObject value = jobject.getJSONObject(key);

                HashMap<String, String> ObjTv = new HashMap<String, String>();

                Iterator<String> keysItr1 = value.keys();
                while (keysItr1.hasNext()) {
                    String key1 = keysItr1.next();
                    String value1 = value.getString(key1);

                    ObjTv.put(key1, value1);
                }

                objectH.put(key, ObjTv);
            }

        } catch (Exception e) {
            log.error("getMap:" + e.getLocalizedMessage());
        }
        return objectH;
    }

    private void processProductSubscription(JSONObject res, ChargebeeWebhooksStatus whStatus, String eventProcess, long gateway_id) {
        // Insert subscription functionality
        String event_id = "NA";
        String event_type = "NA";
        try {
            // insert or update subscription
            event_id = res.getString("id");
            event_type = res.getString("event_type");
            if (insertOrUpdateSubscription) {
                whStatus.setEventProcess(eventProcess);
                whStatus = insertOrUpdateSubscription(res, whStatus, "subscription_created", gateway_id);

            }

        } catch (Exception e) {
            log.error(e.getLocalizedMessage());
            async.asynSlackMessage("Error While Update AllSubscription  :", event_id + " : "
                    + event_type + " : " + e.getLocalizedMessage());
        }
    }

    private boolean disableGatewayFeature(ChargebeeWebhooksStatus whStatus,long gatewayId) {
        try {
            whStatus.setEventProcess("Initiate Cancellation");
            whStatus.setEventStatus("Initiate Txn_Service Request to disable gateway and DeAct SIM");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

            log.info("Call Txn_Service for disable user :: eventid = " + whStatus.getEventId());
            String urlParams = "";
            String msurl = microservice_url + "/v3.0/cancelsubscriptionbygateway?cbid=" + whStatus.getChargebeeId() + "&eventid="
                    + whStatus.getEventId() + "&gatewayId=" + gatewayId;
            log.info("disableUser :" + msurl);

            // Response from Txn_Service
            String microServiceRes = helper.httpPOSTRequest(msurl, urlParams);

            if (microServiceRes != null) {
                JSONObject microServiceResJson = new JSONObject(microServiceRes);

                microServiceResJson = microServiceResJson.getJSONObject("response");

                // int status = microServiceResJson.getInt("Status");
                int code = microServiceResJson.getInt("Code");
                // String msg = microServiceResJson.getString("Msg");

                if (code == 1 || code == 3) {
                    whStatus.setDeactivateSimStatus(1);
                    whStatus.setStatus(true);
                    whStatus.setEventStatus("Disable gateway and DeAct SIM successful");
                } else {
                    whStatus.setDeactivateSimStatus(0);
                    whStatus.setEventStatus("Disable gateway and DeAct SIM unsuccessful");
                }
            } else {
                return false;
            }

        } catch (Exception e) {
            log.error("disableUser:" + e.getLocalizedMessage());
            whStatus.setEventStatus("Error in calling Txn_Service for disable user");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
            return false;
        }
        return true;
    }

    private boolean disableUser(ChargebeeWebhooksStatus whStatus) {
        try {
            whStatus.setEventProcess("Initiate Cancellation");
            whStatus.setEventStatus("Initiate Txn_Service Request to disable gateway and DeAct SIM");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());

            log.info("Call Txn_Service for disable user :: eventid = " + whStatus.getEventId());
            String urlParams = "";
            String msurl = microservice_url + "/v3.0/cancelsubscription?cbid=" + whStatus.getChargebeeId() + "&eventid="
                    + whStatus.getEventId();
            log.info("disableUser :" + msurl);

            // Response from Txn_Service
            String microServiceRes = helper.httpPOSTRequest(msurl, urlParams);

            if (microServiceRes != null) {
                JSONObject microServiceResJson = new JSONObject(microServiceRes);

                microServiceResJson = microServiceResJson.getJSONObject("response");

                // int status = microServiceResJson.getInt("Status");
                int code = microServiceResJson.getInt("Code");
                // String msg = microServiceResJson.getString("Msg");

                if (code == 1 || code == 3) {
                    whStatus.setDeactivateSimStatus(1);
                    whStatus.setStatus(true);
                    whStatus.setEventStatus("Disable gateway and DeAct SIM successful");
                } else {
                    whStatus.setDeactivateSimStatus(0);
                    whStatus.setEventStatus("Disable gateway and DeAct SIM unsuccessful");
                }
            } else {
                return false;
            }

        } catch (Exception e) {
            log.error("disableUser:" + e.getLocalizedMessage());
            whStatus.setEventStatus("Error in calling Txn_Service for disable user");
            whStatus.setUpdatedOn(IrisUtil.getUtcDateTime());
            return false;
        }
        return true;
    }

    private UserSubscription UpdateObject(JSONObject res, UserSubscription userSubscription, long gateway_id) {

        try {
            JSONObject content = res.getJSONObject("content");
            String event_type = res.getString("event_type");
            JSONObject customer = null;
            JSONObject sub = content.getJSONObject("subscription");
            customer = content.getJSONObject("customer");

            HashMap<String, HashMap<String, String>> plans = new HashMap<String, HashMap<String, String>>();

            HashMap<String, HashMap<String, String>> addonList = new HashMap<String, HashMap<String, String>>();

            Set<String> subAddon = new HashSet<String>();

            String subStatus = sub.getString("status");
            String cbsub_id = sub.getString("id");
            int unit_price = Integer.parseInt(sub.getString("plan_unit_price"));

            userSubscription.setCbsub_id(cbsub_id);
            userSubscription.setSub_status(subStatus);

            if (userSubscription.getPlan_info() != null && !userSubscription.getPlan_info().equals("NA"))
                plans = getMap(userSubscription.getPlan_info());

            if (userSubscription.getPlan_info() != null && !userSubscription.getAddon_info().equals("NA"))
                addonList = getMap(userSubscription.getAddon_info());

            userSubscription.setChargebeeid(customer.getString("id"));
            if ((!omitplan.contains(content.getJSONObject("subscription").getString("plan_id")))
                    && (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("active") || content
                    .getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial"))) {

                String status = content.getJSONObject("subscription").getString("status");
                if (!plans.isEmpty()) {
                    if (plans.containsKey(cbsub_id)) {
                        Map<String, String> subMap = plans.get(cbsub_id);
                        if (!status.equalsIgnoreCase(subMap.get("Status").toString())
                                || (subMap.containsKey("unit_price")
                                && unit_price != Integer.parseInt(subMap.get("unit_price")))) {
                            userSubscription.setPrev_plan(userSubscription.getCur_planid());
                            userSubscription.setPrev_plan_start_dt(userSubscription.getCur_plan_dt());
                            userSubscription.setPrev_plan_end_dt(userSubscription.getNext_renew_dt());
                        } else {
                            userSubscription.setPrev_plan(userSubscription.getPrev_plan());
                            userSubscription.setPrev_plan_start_dt(userSubscription.getPrev_plan_start_dt());
                            userSubscription.setPrev_plan_end_dt(userSubscription.getPrev_plan_end_dt());
                        }
                    } else {
                        userSubscription.setPrev_plan(userSubscription.getCur_planid());
                        userSubscription.setPrev_plan_start_dt(userSubscription.getCur_plan_dt());
                        userSubscription.setPrev_plan_end_dt(userSubscription.getNext_renew_dt());
                    }
                }
                userSubscription
                        .setNext_renew_dt(new Timestamp(new Date(sub.getInt("next_billing_at") * 1000L).getTime()));
                userSubscription.setCur_planid(sub.getString("plan_id"));

                if (userSubscription.getFirst_planid().equals("NA")) {
                    userSubscription
                            .setFirst_plan_dt(new Timestamp(new Date(sub.getInt("created_at") * 1000L).getTime()));
                    userSubscription.setFirst_planid(sub.getString("plan_id"));
                }

                if (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial")) {
                    userSubscription
                            .setCur_plan_dt(new Timestamp(new Date(sub.getInt("trial_start") * 1000L).getTime()));
                    userSubscription.setFree_trial_applied(true);
                } else {
                    userSubscription.setCur_plan_dt(
                            new Timestamp(new Date(sub.getInt("current_term_start") * 1000L).getTime()));
                }
            }

            if ((!omitplan.contains(content.getJSONObject("subscription").getString("plan_id")))
                    && (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("active") || content
                    .getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial"))) {

                userSubscription.setVpmcur_plan_dt(new Timestamp(new Date(sub.getInt("created_at") * 1000L).getTime()));
                userSubscription.setVpmcur_planid(sub.getString("plan_id"));

                if (userSubscription.getVpmfirst_planid().equalsIgnoreCase("NA")) {
                    userSubscription
                            .setVpmfirst_plan_dt(new Timestamp(new Date(sub.getInt("created_at") * 1000L).getTime()));
                    userSubscription.setVpmfirst_planid(sub.getString("plan_id"));
                }

                if (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial"))
                    userSubscription.setVpmfree_trial_applied(true);

            }

            // Json Object
            String Key = content.getJSONObject("subscription").getString("id");
            HashMap<String, String> value = new HashMap<String, String>();
            if (plans.containsKey(Key))
                value = plans.get(Key);

            value.put("SubId", content.getJSONObject("subscription").getString("id"));
            value.put("UpdatedAt", toDate(sub.getInt("updated_at")).toString());
            value.put("PlanId", sub.getString("plan_id").toString());
            value.put("Status", subStatus);
            value.put("unit_price", unit_price + "");
            if (event_type.equalsIgnoreCase("subscription_cancelled")) {
                value.put("is_deleted", "1");
            }
            if (gateway_id != 0) {
                value.put("gateway_id", gateway_id + "");
            }

            plans.put(Key, value);

            try {
                JSONArray addons = new JSONArray();
                if (sub.has("addons"))
                    addons = sub.getJSONArray("addons");

                // String addonStr = "";
                for (int itr = 0; itr < addons.length(); itr++) {
                    JSONObject addon = addons.getJSONObject(itr);

                    subAddon.add(addon.getString("id"));

                    // VPM Addon As Plan
//					if (vpmplan.contains(addon.getString("id"))
//							&& (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("active")
//									|| content.getJSONObject("subscription").getString("status")
//											.equalsIgnoreCase("in_trial"))) {
//
//						userSubscription
//								.setVpmcur_plan_dt(new Timestamp(new Date(sub.getInt("created_at") * 1000L).getTime()));
//						userSubscription.setVpmcur_planid(addon.getString("id"));
//
//						if (userSubscription.getVpmfirst_planid().equalsIgnoreCase("NA")) {
//							userSubscription.setVpmfirst_plan_dt(
//									new Timestamp(new Date(sub.getInt("created_at") * 1000L).getTime()));
//							userSubscription.setVpmfirst_planid(addon.getString("id"));
//						}
//
//						if (content.getJSONObject("subscription").getString("status").equalsIgnoreCase("in_trial"))
//							userSubscription.setVpmfree_trial_applied(true);
//
//					}

                    // Json Object
                    String addonKey = addon.getString("id");
                    HashMap<String, String> addonvalue = new HashMap<String, String>();
                    if (addonList.containsKey(addonKey))
                        addonvalue = addonList.get(addonKey);

                    addonvalue.put("SubId", content.getJSONObject("subscription").getString("id"));
                    addonvalue.put("UpdatedAt", toDate(sub.getInt("updated_at")).toString());
                    addonvalue.put("AddonId", addon.getString("id"));
                    addonvalue.put("Status", subStatus);
                    addonvalue.put("RecurringType", "Recurring");
                    addonvalue.put("Count", "0");

                    addonList.put(addonKey, addonvalue);

                }

                // get Addon from invoice

                if (content.has("invoice")) {
                    JSONObject invoice = content.getJSONObject("invoice");
                    JSONArray line_Item = invoice.getJSONArray("line_items");

                    for (int i = 0; i < line_Item.length(); i++) {
                        JSONObject lineItem = line_Item.getJSONObject(i);

                        if (lineItem.getString("entity_type").equals("addon")) {

                            // Json Object
                            String AddonKey = lineItem.getString("entity_id");
                            HashMap<String, String> addonvalue = new HashMap<String, String>();

                            if (addonList.containsKey(AddonKey)) {

                                addonvalue = addonList.get(AddonKey);

                                String count = addonvalue.getOrDefault("Count", "0");
                                int countInt = Integer.parseInt(count);
                                addonvalue.put("Count", String.valueOf(++countInt));

                            } else {
                                addonvalue.put("Count", "1");
                            }

                            addonvalue.put("SubId", content.getJSONObject("subscription").getString("id"));
                            addonvalue.put("UpdatedAt", toDate(sub.getInt("updated_at")).toString());
                            addonvalue.put("AddonId", lineItem.getString("entity_id"));
                            addonvalue.put("Status", "Active");

                            if (subAddon.contains(AddonKey))
                                addonvalue.put("RecurringType", "Recurring");
                            else
                                addonvalue.put("RecurringType", "Non-Recurring");

                            addonList.put(AddonKey, addonvalue);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error While Updating Addon List :" + e.getLocalizedMessage());
            }

            JSONObject planInfo = new JSONObject(plans);
            JSONObject addonInfo = new JSONObject(addonList);

            userSubscription.setPlan_info(planInfo.toString());
            userSubscription.setAddon_info(addonInfo.toString());

        } catch (Exception e) {
            log.error(" Error While Updating Object user Subscription : " + e.getMessage());
        }
        return userSubscription;
    }

    private DeviceSubscription UpdateDeviceObject(JSONObject res, DeviceSubscription deviceSubscription, long gateway_id) {

        try {
            JSONObject content = res.getJSONObject("content");
            String event_type = res.getString("event_type");
            JSONObject customer = null;
            JSONObject sub = content.getJSONObject("subscription");
            customer = content.getJSONObject("customer");
            DeviceSubscription devSubBeforeUpdate = null;

            if(deviceSubscription != null)
                devSubBeforeUpdate = (DeviceSubscription)deviceSubscription.clone();

            String subStatus = sub.getString("status");
            String cbsub_id = sub.getString("id");
            String plan_id = sub.getString("plan_id");
            deviceSubscription.setSub_id(cbsub_id);
            deviceSubscription.setSub_status(subStatus);
            deviceSubscription.setChargebeeid(customer.getString("id"));
            long cur_mtype = gatewayService.getMonitorType(plan_id);

            if (!omitplan.contains(plan_id)){
                if(subStatus.equalsIgnoreCase("active")) {
                    String period = chargebeeWebhookService.getPlanToPeriod(plan_id);
                    Timestamp activateTs = new Timestamp(new Date(sub.getInt("activated_at") * 1000L).getTime());
                    Timestamp startTs = new Timestamp(new Date(sub.getInt("started_at") * 1000L).getTime());
                    deviceSubscription.setCur_plan_activated_date(activateTs);
                    deviceSubscription.setCur_plan_startdt(startTs);
                    deviceSubscription.setPlan_id(plan_id);
                    deviceSubscription.setCur_period(period);
                    deviceSubscription.setSub_status(subStatus);
                    Timestamp nxtTS = new Timestamp(new Date(sub.getInt("next_billing_at") * 1000L).getTime());
                    deviceSubscription.setNext_renewal_dt(nxtTS);
                    deviceSubscription.setCur_plan_enddt(nxtTS);

                    if(!(plan_id.equalsIgnoreCase(devSubBeforeUpdate.getPlan_id())) && devSubBeforeUpdate.getMtype_id()==cur_mtype
                            && !(devSubBeforeUpdate.getPlan_id().equalsIgnoreCase("na"))) {
                        deviceSubscription.setPrev_planid(devSubBeforeUpdate.getPlan_id());
                        deviceSubscription.setPrev_period(devSubBeforeUpdate.getCur_period());
                        deviceSubscription.setPrev_plan_startdt(devSubBeforeUpdate.getCur_plan_activated_date());
                        deviceSubscription.setPrev_plan_enddt(startTs);
                    }

                    if(devSubBeforeUpdate.getSub_status().equalsIgnoreCase("cancelled")) {
                        Timestamp defaultts = Timestamp.valueOf("1753-01-01 00:00:00");
                        Timestamp ts = Timestamp.valueOf("1753-01-01 00:00:00");
                        deviceSubscription.setCancel_date(ts);
                        deviceSubscription.setCur_plan_enddt(ts);

                        //if(devSubBeforeUpdate.getMtype_id()==cur_mtype) {
                        deviceSubscription.setPrev_planid(devSubBeforeUpdate.getPlan_id());
                        deviceSubscription.setPrev_period(devSubBeforeUpdate.getCur_period());
                        deviceSubscription.setPrev_plan_startdt(devSubBeforeUpdate.getCur_plan_activated_date());
                        deviceSubscription.setPrev_plan_enddt(devSubBeforeUpdate.getCancel_date());
                        //}
                    }

                    //This will be updated in device sub tool for DA
                    if(deviceSubscription.getFirst_plan().equalsIgnoreCase("NA") ||
                            deviceSubscription.getFirst_period().equalsIgnoreCase("NA")||
                            deviceSubscription.getPaid_subscription_date().toString().contains("1753-01-01"))
                    {
                        deviceSubscription.setFirst_plan(plan_id);
                        deviceSubscription.setFirst_period(period);
                        deviceSubscription.setPaid_subscription_date(startTs);
                    }

                }else if (subStatus.equalsIgnoreCase("in_trial")) {
                    deviceSubscription.setTrial_start_date(new Timestamp(new Date(sub.getInt("trial_start") * 1000L).getTime()));

                    if(deviceSubscription.getFirst_plan().equalsIgnoreCase("NA")) {
                        String period = chargebeeWebhookService.getPlanToPeriod(plan_id);

                        deviceSubscription.setFirst_plan(plan_id);
                        deviceSubscription.setFirst_period(period);
                        deviceSubscription.setPlan_id(plan_id);
                        deviceSubscription.setCur_period(period);

                    }
                }else if (subStatus.equalsIgnoreCase("cancelled")) {
                    Timestamp canTS = new Timestamp(new Date(sub.getInt("cancelled_at")*1000L).getTime());
                    Timestamp defaultts = Timestamp.valueOf("1753-01-01 00:00:00");

                    deviceSubscription.setCancel_date(canTS);
                    deviceSubscription.setCur_plan_enddt(canTS);
                    deviceSubscription.setNext_renewal_dt(defaultts);
                    //if(devSubBeforeUpdate.getMtype_id()==cur_mtype) {
                    deviceSubscription.setPrev_planid(deviceSubscription.getPlan_id());
                    deviceSubscription.setPrev_period(deviceSubscription.getCur_period());
                    deviceSubscription.setPrev_plan_startdt(deviceSubscription.getCur_plan_activated_date());
                    deviceSubscription.setPrev_plan_enddt(deviceSubscription.getCancel_date());
                    //}
                }else if (subStatus.equalsIgnoreCase("non_renewing")) {
                    Timestamp canTS = new Timestamp(new Date(sub.getInt("cancelled_at")*1000L).getTime());
                    deviceSubscription.setCancel_date(canTS);
                    deviceSubscription.setCur_plan_enddt(canTS);

                }
            }

        } catch (Exception e) {
            //e.printStackTrace();
            log.error(" Error While Updating UpdateDeviceObject : " + e.getMessage());
        }
        return deviceSubscription;
    }

    private String currentDate() {
        Calendar currDateCal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(currDateCal.getTime());
    }

    private AllProductSubscription setDataAllProdSub(AllSubscription subscription) {
        try {
            AllProductSubscription subs = new AllProductSubscription( subscription.getSubscriptionId(),
                    subscription.getPlanId(), subscription.getSubscriptionStatus(),
                    subscription.getSubscriptionCreatedAt(), subscription.getSubscriptionStartedAt(),
                    subscription.getSubscriptionActivatedAt(), subscription.getSubscriptionCancelledAt(),
                    subscription.getPlanAmount(), subscription.getCustomerId(), subscription.getBillingEmail(),
                    subscription.getAddons(), subscription.getTrialStart(), subscription.getTrialEnd(),
                    subscription.getNextBillingAt(), subscription.getUpdatedIndb(), subscription.getIsDeleted(),
                    subscription.getMetaData(), subscription.getEnable(), subscription.getUpdatedDate(),
                    subscription.getPlanPeriod(), subscription.getEvent_type(), subscription.getCurrency_code(),
                    subscription.getExchange_rate(), subscription.getState_code(), subscription.getMonitor_type(),
                    subscription.getMrr(),subscription.getResume_date());
            return subs;
        } catch (Exception e) {
            log.error("setData :" + e.getLocalizedMessage());
            return null;
        }
    }

    private boolean saveCancelledSubscriptionHistory(AllProductSubscription subscription) {

        CBCancelHistory cancelHistory = new CBCancelHistory();

        cancelHistory.setChargebeeId(subscription.getCustomerId());
        cancelHistory.setCancelledDate(subscription.getSubscriptionCancelledAt());
        cancelHistory.setSubscriptionId(subscription.getSubscriptionId());
        cancelHistory.setBillingEmail(subscription.getBillingEmail());
        cancelHistory.setPlanId(subscription.getPlanId());
        cancelHistory.setPlanPeriod(subscription.getPlanPeriod());
        cancelHistory.setCreatedOn(helper.getCurrentTimeinUTC());

        return chargebeeWebhookService.saveCancelledSubscriptionHistory(cancelHistory);
    }

    private Subscription setData(AllSubscription subscription) {
        try {
            Subscription subs = new Subscription(null, subscription.getSubscriptionId(), subscription.getPlanId(),
                    subscription.getSubscriptionStatus(), subscription.getSubscriptionCreatedAt(),
                    subscription.getSubscriptionStartedAt(), subscription.getSubscriptionActivatedAt(),
                    subscription.getSubscriptionCancelledAt(), subscription.getPlanAmount(),
                    subscription.getCustomerId(), subscription.getBillingEmail(), subscription.getAddons(),
                    subscription.getTrialStart(), subscription.getTrialEnd(), subscription.getNextBillingAt(),
                    subscription.getUpdatedIndb(), subscription.getIsDeleted(), subscription.getMetaData(),
                    subscription.getEnable(), subscription.getUpdatedDate(), subscription.getPlanPeriod(),
                    subscription.getEvent_type());
            return subs;
        } catch (Exception e) {
            log.error("setData :" + e.getLocalizedMessage());
            return null;
        }
    }

    private String CreateCustomerContent(Result cbresult, JSONObject res) {
        String orderContent = "NA";
        try {
            JSONObject response = res.getJSONObject("content");
            JSONObject order = response.getJSONObject("order");

            Customer cus = cbresult.customer();
            Order ord = new Order(order.toString());

            Order.ShippingAddress shipAddress = null;
            Order.BillingAddress billingAddress = null;

            String email = "";
            String phone = "+19876543210";
            String add1 = "";
            String add2 = "NA";
            String fname = "";
            String lname = "";
            String zip = "";
            String city = "";
            String state = "";
            String country = "";

            if (cus.email() != null)
                email = cus.email().trim();

            if (cus.phone() != null)
                phone = cus.phone();

            if (ord != null) {
                shipAddress = ord.shippingAddress();

                billingAddress = ord.billingAddress();

                if (shipAddress != null) {

                    if (shipAddress.line1() != null)
                        add1 = shipAddress.line1().trim();

                    if (shipAddress.line2() != null)
                        add2 = shipAddress.line2().trim();

                    if (shipAddress.firstName() != null) {
                        fname = shipAddress.firstName().trim();
//						bill_Fname = bill_Fname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (shipAddress.lastName() != null) {
                        lname = shipAddress.lastName().trim();
//						bill_lname = bill_lname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (shipAddress.zip() != null)
                        zip = shipAddress.zip();
                    if (shipAddress.city() != null)
                        city = shipAddress.city();
                    if (shipAddress.state() != null)
                        state = shipAddress.state();
                    if (shipAddress.country() != null)
                        country = shipAddress.country();

                } else if (billingAddress != null) {

                    if (billingAddress.line1() != null)
                        add1 = billingAddress.line1().trim();

                    if (billingAddress.line2() != null)
                        add2 = billingAddress.line2().trim();

                    if (billingAddress.firstName() != null) {
                        fname = billingAddress.firstName().trim();
//						bill_Fname = bill_Fname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (billingAddress.lastName() != null) {
                        lname = billingAddress.lastName().trim();
//						bill_lname = bill_lname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (billingAddress.zip() != null)
                        zip = billingAddress.zip();
                    if (billingAddress.city() != null)
                        city = billingAddress.city();
                    if (billingAddress.state() != null)
                        state = billingAddress.state();
                    if (billingAddress.country() != null)
                        country = billingAddress.country();
                }
            }

            orderContent = "{\r\n" + "    \"customer\": {\r\n" + "        \"first_name\": \"" + fname + "\",\r\n"
                    + "        \"last_name\": \"" + fname + "\",\r\n" + "        \"email\": \"" + email + "\",\r\n"
                    + "        \"verified_email\": true,\r\n" + "        \"addresses\": [\r\n" + "            {\r\n"
                    + "                \"address1\": \"" + add1 + "\",\r\n";

            if (!add2.equals("NA"))
                orderContent = orderContent + " \"address2\": \"" + add2 + "\",\r\n";

            orderContent = orderContent + " \"city\": \"" + city + "\",\r\n" + "                \"province\": \""
                    + state + "\",\r\n" + "                \"phone\": \"" + phone + "\",\r\n"
                    + "                \"zip\": \"" + zip + "\",\r\n" + "                \"last_name\": \"" + lname
                    + "\",\r\n" + "                \"first_name\": \"" + fname + "\",\r\n"
                    + "                \"country\": \"" + country + "\"\r\n" + "            }\r\n" + "        ]\r\n"
                    + "    }\r\n" + "}";

        } catch (Exception e) {
            return "Error: While Create Customer Request : " + e.getLocalizedMessage();
        }
        return orderContent;
    }

    private String CreateOrderContent(String variantsRes, String customerRes, CBShopifyOrders shopifyOrd,
                                      Customer customer1, JSONObject addon, JSONObject orders) {
        String orderContent = "NA";
        try {

            JSONObject variRes = new JSONObject(variantsRes);
            JSONObject variant = variRes.getJSONObject("variant");

            JSONObject custRes = new JSONObject(customerRes);
            JSONObject cus = custRes.getJSONObject("customer");

            // JSONObject add = new JSONObject(customerRes);
            // JSONObject customer = add.getJSONObject("customer");
            // JSONObject Add1 = (JSONObject) customer.getJSONArray("addresses").get(0);

            String prd_title = "NA";
            long variant_id = 0;
            String price = "NA";

            String[] address = ordersAddress(orders.toString(), customer1.email(), customer1.phone());

            if (variant != null) {

                if (variant.has("title"))
                    prd_title = variant.getString("title").trim();

                if (variant.has("id"))
                    variant_id = variant.getLong("id");

                if (variant.has("price")) {
//					price = variant.getString("price").trim();
                    int amount = addon.getInt("amount");
                    amount = amount / 100;
                    price = String.valueOf(amount);
                }

            }

            orderContent = "{\r\n" + "    \"order\": {\r\n" + "        \"line_items\": [\r\n" + "            {\r\n"
                    + "                \r\n" + "                \"variant_id\": " + variant_id + ",\r\n"
                    + "                \"quantity\": 1,\r\n" + "                \"price\": \"" + price + "\"\r\n"
                    + "            }\r\n" + "        ],\r\n" + "";

            orderContent = orderContent + "\"customer\": " + cus + ",\r\n";

            if (shopifyOrd.isFulfillment())
                orderContent = orderContent + "\"fulfillment_status\": \"fulfilled\",\r\n";

            if (shopifyOrd.getDiscount() > 0)
                orderContent = orderContent + "\"discount_codes\":[{\"code\":\"Niom\",\"amount\":\""
                        + shopifyOrd.getDiscount() + "\",\"type\":\"percentage\"}],";

            if (!shopifyOrd.getTags().equalsIgnoreCase("NA"))
                orderContent = orderContent + "\"tags\": \"" + shopifyOrd.getTags() + "\",\r\n";

            orderContent = orderContent + "\"financial_status\": \"paid\",\"note\":\"" + customer1.id() + "\"\r\n ";

            if (address[0] != null)
                orderContent = orderContent + ",\"shipping_address\":" + new JSONObject(address[0]) + " ";

            if (address[1] != null)
                orderContent = orderContent + ",\"billing_address\":" + new JSONObject(address[1]);

            orderContent = orderContent
                    + ", \"shipping_lines\": [{\"carrier_identifier\": null,\"code\": \"Free Shipping (3-7 Biz Days)\",\"delivery_category\": null,\"discounted_price\": \"0.00\",\"discounted_price_set\": {\"shop_money\": {\"amount\": \"0.00\",\"currency_code\": \"USD\"},\"presentment_money\": {\"amount\": \"0.00\",\"currency_code\": \"USD\"}},\"phone\": null,\"price\": \"0.00\",\"price_set\": {\"shop_money\": {\"amount\": \"0.00\",\"currency_code\": \"USD\"},\"presentment_money\": {\"amount\": \"0.00\",\"currency_code\": \"USD\"}},\"requested_fulfillment_service_id\": null,\"source\": \"shopify\",\"title\": \"Free Shipping (3-7 Biz Days)\",\"tax_lines\": [],\"discount_allocations\": []}]"
                    + "   }\r\n" + "}";

        } catch (Exception e) {
            return "Error: While Create Order Request " + e.getLocalizedMessage();
        }
        return orderContent;
    }

    private String[] ordersAddress(String order, String ord_email, String ord_phone) {
        try {

            Order ord = new Order(order);

            Order.ShippingAddress shipAddress = null;
            Order.BillingAddress billingAddress = null;

            String email = "";
            String phone = "+19876543210";

            String ship_add1 = "";
            String ship_add2 = "NA";
            String ship_fname = "";
            String ship_lname = "";
            String ship_zip = "";
            String ship_city = "";
            String ship_state = "";
            String ship_country = "";

            String bill_add1 = "";
            String bill_add2 = "NA";
            String bill_fname = "";
            String bill_lname = "";
            String bill_zip = "";
            String bill_city = "";
            String bill_state = "";
            String bill_country = "";

            if (ord_email != null)
                email = ord_email;

            if (ord_phone != null)
                phone = ord_phone;

            if (ord != null) {
                shipAddress = ord.shippingAddress();

                billingAddress = ord.billingAddress();

                if (shipAddress != null) {

                    if (shipAddress.line1() != null)
                        ship_add1 = shipAddress.line1().trim();

                    if (shipAddress.line2() != null)
                        ship_add2 = shipAddress.line2().trim();

                    if (shipAddress.firstName() != null) {
                        ship_fname = shipAddress.firstName().trim();
//					bill_Fname = bill_Fname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (shipAddress.lastName() != null) {
                        ship_lname = shipAddress.lastName().trim();
//					bill_lname = bill_lname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (shipAddress.zip() != null)
                        ship_zip = shipAddress.zip();
                    if (shipAddress.city() != null)
                        ship_city = shipAddress.city();
                    if (shipAddress.state() != null)
                        ship_state = shipAddress.state();
                    if (shipAddress.country() != null)
                        ship_country = shipAddress.country();
                }

                if (billingAddress != null) {

                    if (billingAddress.line1() != null)
                        bill_add1 = billingAddress.line1().trim();

                    if (billingAddress.line2() != null)
                        bill_add2 = billingAddress.line2().trim();

                    if (billingAddress.firstName() != null) {
                        bill_fname = billingAddress.firstName().trim();
//					bill_Fname = bill_Fname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (billingAddress.lastName() != null) {
                        bill_lname = billingAddress.lastName().trim();
//					bill_lname = bill_lname.replaceAll("[^a-zA-Z0-9\\s]", "");
                    }

                    if (billingAddress.zip() != null)
                        bill_zip = billingAddress.zip();
                    if (billingAddress.city() != null)
                        bill_city = billingAddress.city();
                    if (billingAddress.state() != null)
                        bill_state = billingAddress.state();
                    if (billingAddress.country() != null)
                        bill_country = billingAddress.country();
                }
            }

            HashMap<String, Object> address = new HashMap<String, Object>();
            HashMap<String, Object> bill_address = new HashMap<String, Object>();

            address.put("address1", ship_add1);
            if (!ship_add2.equals("NA"))
                address.put("address2", ship_add2);
            address.put("city", ship_city);
            address.put("province", ship_state);
            address.put("phone", phone);
            address.put("zip", ship_zip);
            address.put("last_name", ship_lname);
            address.put("first_name", ship_fname);
            address.put("country", ship_country);

            bill_address.put("address1", bill_add1);
            if (!bill_add2.equals("NA"))
                bill_address.put("address2", bill_add2);
            bill_address.put("city", bill_city);
            bill_address.put("province", bill_state);
            bill_address.put("phone", phone);
            bill_address.put("zip", bill_zip);
            bill_address.put("last_name", bill_lname);
            bill_address.put("first_name", bill_fname);
            bill_address.put("country", bill_country);

            String[] stAdd = new String[2];

            if (shipAddress != null) {
                stAdd[0] = new JSONObject(address).put("phone", phone).toString();
            }

            if (billingAddress != null) {
                stAdd[1] = new JSONObject(bill_address).put("phone", phone).toString();
            }
            return stAdd;
        } catch (Exception e) {
            log.error("ordersAddress:" + e.getLocalizedMessage());
        }
        return null;
    }

    private boolean saveCancelledSubscriptionHistory(AllSubscription subscription) {

        CBCancelHistory cancelHistory = new CBCancelHistory();

        cancelHistory.setChargebeeId(subscription.getCustomerId());
        cancelHistory.setCancelledDate(subscription.getSubscriptionCancelledAt());
        cancelHistory.setSubscriptionId(subscription.getSubscriptionId());
        cancelHistory.setBillingEmail(subscription.getBillingEmail());
        cancelHistory.setPlanId(subscription.getPlanId());
        cancelHistory.setPlanPeriod(subscription.getPlanPeriod());
        cancelHistory.setCreatedOn(helper.getCurrentTimeinUTC());

        return chargebeeWebhookService.saveCancelledSubscriptionHistory(cancelHistory);
    }

	@Override
	@Transactional
	public boolean saveSubsStatusFromcombomigration(String subId, long gateway_id) {
		return chargebeeWebhooksDao.saveSubsStatusFromcombomigration(subId, gateway_id);
	}
}
