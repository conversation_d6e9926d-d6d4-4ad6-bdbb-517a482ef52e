package com.nimble.webhooks.service.impl;

import com.nimble.webhooks.dao.IChargebeeWebhooksDao;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service to handle webhook idempotency with atomic operations
 */
@Service
public class IdempotentWebhookService {

    private static final Logger log = LogManager.getLogger(IdempotentWebhookService.class);

    @Autowired
    private IChargebeeWebhooksDao chargebeeWebhooksDao;

    /**
     * Atomically check and save webhook status to prevent duplicate processing
     * 
     * @param eventId The webhook event ID
     * @param eventType The webhook event type
     * @param subId The subscription ID (for MRR events)
     * @param mrr The MRR value (for MRR events)
     * @return WebhookProcessingResult indicating if processing should continue
     */
    @Transactional(
        isolation = Isolation.SERIALIZABLE,
        propagation = Propagation.REQUIRES_NEW,
        rollbackFor = Exception.class
    )
    public WebhookProcessingResult checkAndReserveWebhookProcessing(
            String eventId, String eventType, String subId, Long mrr) {
        
        try {
            // First, check if this exact webhook has already been processed
            ChargebeeWebhooksStatus existingStatus = new ChargebeeWebhooksStatus();
            existingStatus.setEventId(eventId);
            existingStatus.setEventType(eventType);
            
            ChargebeeWebhooksStatus existing = chargebeeWebhooksDao.webHookStatusIsAvailable(existingStatus);
            
            if (existing != null && existing.isStatus()) {
                log.info("Webhook already processed: eventId={}, eventType={}", eventId, eventType);
                return new WebhookProcessingResult(false, existing, "Already processed");
            }
            
            // For MRR updates, also check if the same MRR value was recently updated
            if ("mrr_updated".equalsIgnoreCase(eventType) && subId != null && mrr != null) {
                if (isRecentMrrUpdateForSubscription(subId, mrr)) {
                    log.info("Recent MRR update detected for same subscription: subId={}, mrr={}", subId, mrr);
                    return new WebhookProcessingResult(false, existing, "Recent MRR update exists");
                }
            }
            
            // Create new webhook status record to reserve processing
            ChargebeeWebhooksStatus newStatus = createWebhookStatus(eventId, eventType, subId);
            ChargebeeWebhooksStatus saved = chargebeeWebhooksDao.saveWebHookStatus(newStatus);
            
            if (saved != null) {
                log.info("Webhook processing reserved: eventId={}, eventType={}", eventId, eventType);
                return new WebhookProcessingResult(true, saved, "Processing reserved");
            } else {
                log.error("Failed to save webhook status: eventId={}, eventType={}", eventId, eventType);
                return new WebhookProcessingResult(false, null, "Failed to reserve processing");
            }
            
        } catch (DataIntegrityViolationException e) {
            // This means another thread already inserted the same webhook
            log.info("Concurrent webhook processing detected: eventId={}, eventType={} - {}", 
                    eventId, eventType, e.getMessage());
            return new WebhookProcessingResult(false, null, "Concurrent processing detected");
            
        } catch (Exception e) {
            log.error("Error in webhook idempotency check: eventId={}, eventType={}, error={}", 
                     eventId, eventType, e.getMessage(), e);
            throw e;
        }
    }
    
    private boolean isRecentMrrUpdateForSubscription(String subId, Long mrr) {
        // This would require a custom query to check recent MRR updates
        // For now, we'll implement a simple time-based check
        try {
            // Check if there's a recent webhook for the same subscription with same MRR
            // This is a simplified implementation - you might want to add a custom DAO method
            return false; // Placeholder - implement based on your requirements
        } catch (Exception e) {
            log.warn("Error checking recent MRR updates: subId={}, mrr={}, error={}", 
                    subId, mrr, e.getMessage());
            return false;
        }
    }
    
    private ChargebeeWebhooksStatus createWebhookStatus(String eventId, String eventType, String subId) {
        ChargebeeWebhooksStatus status = new ChargebeeWebhooksStatus();
        status.setEventId(eventId);
        status.setEventType(eventType);
        status.setEventProcess("Reserved for processing");
        status.setEventStatus("Processing");
        status.setChargebeeId(subId != null ? subId : "NA");
        status.setStatus(false); // Will be set to true when processing completes
        
        // Set timestamps
        String currentTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
        status.setCreatedOn(currentTime);
        status.setUpdatedOn(currentTime);
        
        return status;
    }
    
    /**
     * Result class for webhook processing decision
     */
    public static class WebhookProcessingResult {
        private final boolean shouldProcess;
        private final ChargebeeWebhooksStatus webhookStatus;
        private final String reason;
        
        public WebhookProcessingResult(boolean shouldProcess, ChargebeeWebhooksStatus webhookStatus, String reason) {
            this.shouldProcess = shouldProcess;
            this.webhookStatus = webhookStatus;
            this.reason = reason;
        }
        
        public boolean shouldProcess() {
            return shouldProcess;
        }
        
        public ChargebeeWebhooksStatus getWebhookStatus() {
            return webhookStatus;
        }
        
        public String getReason() {
            return reason;
        }
    }
}
