package com.nimble.webhooks.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.ICheckserviceDao;
import com.nimble.webhooks.entity.Testtable;
import com.nimble.webhooks.service.ICheckserviceService;

@Service
public class CheckserviceServiceImpl implements ICheckserviceService {

	@Autowired
	ICheckserviceDao checkserviceDao;
	

	@Override
	@Transactional
	public Testtable testSaveOrUpdatequery(Testtable testtable) {
		return checkserviceDao.DBSaveOrUpdatequery(testtable);
	}

	@Override
	@Transactional
	public Testtable testDeletequery(Testtable testtable) {
		return checkserviceDao.DBDeletequery(testtable);
	}

	@Override
	@Transactional
	public Testtable testselectquery(Testtable testtable) {
		return checkserviceDao.DBselectquery(testtable);
	}


}
