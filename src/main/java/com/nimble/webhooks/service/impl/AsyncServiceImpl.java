package com.nimble.webhooks.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;

import com.nimble.webhooks.service.ISubscriptionMrrService;
import com.nimble.webhooks.util.SecretManagerService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.ResourceUtils;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.chargebee.Environment;
import com.chargebee.ListResult;
import com.chargebee.Result;
import com.chargebee.exceptions.InvalidRequestException;
import com.chargebee.models.CreditNote;
import com.chargebee.models.Customer;
import com.chargebee.models.Invoice;
import com.chargebee.models.Order;
import com.chargebee.models.Order.BillingAddress;
import com.chargebee.models.Order.ShippingAddress;
import com.nimble.webhooks.dao.INiomDao;
import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AdditionBenefitsCancelReward;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.CBCancelHistory;
import com.nimble.webhooks.entity.CBShopifyOrders;
import com.nimble.webhooks.entity.CbActivateCancelSubStatus;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.entity.Coupon;
import com.nimble.webhooks.entity.Credits;
import com.nimble.webhooks.entity.DeviceSubscription;
import com.nimble.webhooks.entity.ErrorInfo;
import com.nimble.webhooks.entity.Invoices;
import com.nimble.webhooks.entity.PlanToMonitorType;
import com.nimble.webhooks.entity.PlantoPeriod;
import com.nimble.webhooks.entity.Subscription;
import com.nimble.webhooks.entity.UnpaidInvoices;
import com.nimble.webhooks.entity.UserCancelFeedBack;
import com.nimble.webhooks.entity.UserSubscription;
import com.nimble.webhooks.helper.Email;
import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.IErrorService;
import com.nimble.webhooks.service.IExternalConfigService;
import com.nimble.webhooks.service.IGatewayService;
import com.nimble.webhooks.service.IPlanService;
import com.nimble.webhooks.service.IUserService;
import com.nimble.webhooks.util.IrisUtil;

import freemarker.template.Configuration;
import freemarker.template.Template;

@Service
public class AsyncServiceImpl implements IAsyncService {

	@Autowired
	Helper _helper = new Helper();

	private static final Logger log = LogManager.getLogger(AsyncServiceImpl.class);

	@Value("${chargebee.site.name}")
	private String chargebeeSiteName;

	@Value("${chargebee.site.key}")
	private String chargebeeSiteKey;

	@Value("${moengage.app.id}")
	private String moengage_app_id;

	@Value("${moengage.basic.auth}")
	private String moengage_basic_auth;

	@Value("${moengage.event.url}")
	private String moengage_event_url;

	@Value("${retain_user_add_on}")
	private String retain_user_add_on;
	
	@Value("${infobip.url}")
	private String infobip_url;
	
	@Value("${infobip.app.key}")
	private String infobip_app_key;

	@Autowired
	IChargebeeWebhookService chargebeeWebhookService;

	@Autowired
	IErrorService errorinfoService;

	@Autowired
	IExternalConfigService externalConfigService;

	@Autowired
	IUserService userService;

	@Async("asyncExecutor")
	public void asynSlackMessage(String title, String message) {
		_helper.sendtoSlack(title, message);
	}

	private String toDate(long time) {
		Timestamp ts = new Timestamp(time * 1000);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
		return sdf.format(ts);
	}

	@Override
	public void checkAndRemoveUserCancelFeedBack(String chargebeeId) {
		log.info("Entered into checkAndRemoveUserCancelFeedBack :: chargebee_id : " + chargebeeId);
		try {

			UserV4 user = null;
			try {
				user = userService.verifyAuthV4("chargebeeid", chargebeeId);

				if (user == null) {
					log.info("user not found for chargebee_id : " + chargebeeId);
					return;
				}

				log.info("user_id : " + user.getId());
			} catch (Exception e) {
				log.info("Invalid chargebee_id :: chargebee_id : " + chargebeeId + " Error : "
						+ e.getLocalizedMessage());
				return;
			}

			UserCancelFeedBack userCancelFeedBack = null;
			userCancelFeedBack = userService.getUserCancelFeedBackByUserId(user.getId());
			boolean deleted_status = false;
			if (userCancelFeedBack != null) {
				deleted_status = userService.deteleUserCancelFeedBack(userCancelFeedBack);
				log.info("user_cancel_feedback deleted status : " + deleted_status);
			}

			AdditionBenefitsCancelReward additionBenefitsCancelReward = userService
					.getAdditionBenefitsCancelReward(user.getId());
			if (additionBenefitsCancelReward != null) {
				deleted_status = userService.deteleAdditionBenefitsCancelReward(additionBenefitsCancelReward);
				log.info("addition_benefits_cancel_reward deleted status : " + deleted_status);
			}

		} catch (Exception e) {
			log.error("Error in checkAndRemoveUserCancelFeedBack :: Error : " + e.getLocalizedMessage());
		}
	}

	@Override
	@Async("asyncExecutor")
	public void chargebeeEventWebhookInvoiceProcess(JSONObject res, ChargebeeWebhooksStatus whStatusOrigin) {
		log.info("Entered into chargebeeEventWebhookInvoiceProcess....");
		ChargebeeWebhooksStatus whStatus = whStatusOrigin;
		try {
			Environment.configure(chargebeeSiteName, chargebeeSiteKey);

			whStatus.setEventProcess("Initiate Invoice");
			whStatus.setEventStatus("Invoice Processing");

			String event_id = "";
			String event_type = "";
			// String eventProcess = "";
			JSONObject content = null;

			String allSubUpdateStatus = "2";
			String historySubsUpdateStatus = "2";
			String creditsUpdateStatus = "2";
			String invoiceUpdateStatus = "2";
			String subStatus = "NA";

			try {
				event_id = res.getString("id");
				event_type = res.getString("event_type");
				content = res.getJSONObject("content");
			} catch (Exception e) {
				log.error(e.getLocalizedMessage());
				return;
			}
			
			try {
				infobibEventhandling(res, event_type, whStatus);
			} catch (Exception e) {
				log.error(e.getLocalizedMessage());
			}

			if (event_type.contains("invoice")) {

				Invoices invoice = new Invoices();

				JSONObject invoiceJSON = content.getJSONObject("invoice");

				String chargebeeId = invoiceJSON.getString("customer_id");

				invoice.setInvoice_id(invoiceJSON.getString("id"));
				invoice.setChargebeeId(chargebeeId);

				String subId = invoiceJSON.getString("subscription_id");
				String currency = invoiceJSON.getString("currency_code");
				String exRate = invoiceJSON.getString("exchange_rate");

				invoice.setSub_id(subId);
				invoice.setInvoice_date(toDate(invoiceJSON.getInt("date")));

				invoice.setCurrency_code(currency);
				invoice.setExchange_rate(Float.parseFloat(exRate));
				String state_code = "NA";
				if (invoiceJSON.has("billing_address")) {
					JSONObject billJSON = invoiceJSON.getJSONObject("billing_address");

					if (billJSON.has("state_code"))
						state_code = billJSON.getString("state_code");
					invoice.setState_code(state_code);
				}

				chargebeeWebhookService.updateAllCBSubscription(currency, exRate, subId, state_code);

				String isDeleted = invoiceJSON.get("deleted").toString();

				if (isDeleted.equalsIgnoreCase("true")) {
					invoice.setIsDeleted(1);
				} else {
					invoice.setIsDeleted(0);
				}

				String addOns = new String();
				if (invoiceJSON.has("line_items")) {

					JSONArray lineItemsArray = invoiceJSON.getJSONArray("line_items");

					for (int itr = 0; itr < lineItemsArray.length(); itr++) {

						JSONObject lineItems = (JSONObject) lineItemsArray.get(itr);

						if (lineItems.getString("entity_type").equalsIgnoreCase("plan")) {
							String planPeriod = chargebeeWebhookService
									.getPlanToPeriod(lineItems.getString("entity_id"));
							invoice.setPlanperiod(planPeriod);
							invoice.setPlanName(lineItems.getString("entity_id"));
							invoice.setStartDate(toDate(lineItems.getInt("date_from")));
							invoice.setEndDate(toDate(lineItems.getInt("date_to")));
							if (!lineItems.getString("entity_id").equalsIgnoreCase("chum")
									|| lineItems.getInt("amount") > 0)
								subStatus = "Active";
							else
								subStatus = "Inactive";
						} else if (lineItems.getString("entity_type").equalsIgnoreCase("addon")) {
							addOns += lineItems.getString("entity_id") + ":" + lineItems.getInt("quantity") + ",";
						}

					}

					if (addOns.isEmpty())
						addOns = "NA:0";
					else
						addOns = addOns.substring(0, addOns.lastIndexOf(','));

					invoice.setAddons(addOns);
				}

				if (invoiceJSON.has("credits_applied")) {
					int creditAmt = invoiceJSON.getInt("credits_applied");
					invoice.setCredits_applied(creditAmt);
				}

				int issued_amt = 0;
				int amount_refunded = 0;

				if (invoiceJSON.has("issued_credit_notes")) {

					JSONArray issued_cnArray = invoiceJSON.getJSONArray("issued_credit_notes");

					for (int itr = 0; itr < issued_cnArray.length(); itr++) {

						JSONObject cn = (JSONObject) issued_cnArray.get(itr);

						if (cn.get("cn_status").toString().equalsIgnoreCase("refunded")
								&& (int) cn.get("cn_total") > 0) {
							issued_amt += cn.getDouble("cn_total");
							Result result = CreditNote.retrieve(cn.getString("cn_id")).request();
							CreditNote crNote = result.creditNote();
							amount_refunded += Math.floor(crNote.amountRefunded());
						}
					}

					invoice.setIssued_amt(issued_amt);
					invoice.setRefund_amt(amount_refunded);
				}

				invoice.setInvoice_Status(invoiceJSON.getString("status"));
				invoice.setAmountPaid(invoiceJSON.get("amount_paid").toString());
				invoice.setPlan_amt(invoiceJSON.getInt("total"));
				UserV4 user = userService.verifyAuthV4("chargebeeid", chargebeeId);

				if (user != null) {
					invoice.setBilling_Email(user.getEmail());

					if ((event_type.equalsIgnoreCase("invoice_generated")
							|| event_type.equalsIgnoreCase("invoice_updated")) && addOns != null
							&& addOns.contains(retain_user_add_on)) {
						updateSubStatusInUserRetained(user.getId());
					}
				}

				if (invoiceJSON.has("line_item_discounts")) {

					JSONArray lineItemsDiscountArray = invoiceJSON.getJSONArray("line_item_discounts");

					for (int itr = 0; itr < lineItemsDiscountArray.length(); itr++) {

						JSONObject lineItemsDiscount = (JSONObject) lineItemsDiscountArray.get(itr);

						if (lineItemsDiscount.has("coupon_id")) {
							invoice.setCouponCode(lineItemsDiscount.getString("coupon_id"));
							invoice.setCouponAmount(lineItemsDiscount.getInt("discount_amount"));
						}
					}
				}

				// unpaid invoicehistory
				UnpaidInvoices upInvoice = chargebeeWebhookService.checkUnpaidInvoice(invoice.getSub_id(),
						invoice.getChargebeeId());
//				if (invoice.getInvoice_Status().equalsIgnoreCase("paid"))
//					upInvoice = chargebeeWebhookService.isUnpaidInvoice(invoice.getChargebeeId());

//				if (upInvoice == null) {
//					upInvoice = chargebeeWebhookService.checkUnpaidInvoice(invoice.getSub_id(),
//							invoice.getChargebeeId());
//				}

				if (upInvoice == null) {
					if (invoice.getInvoice_Status().equalsIgnoreCase("payment_due")
							|| invoice.getInvoice_Status().equalsIgnoreCase("not_paid")
							|| invoice.getInvoice_Status().equalsIgnoreCase("voided"))
						upInvoice = setUnpaidInvoiceData(null, invoice, subStatus);
				} else {
					upInvoice = setUnpaidInvoiceData(upInvoice, invoice, subStatus);
				}

				if (upInvoice != null)
					chargebeeWebhookService.saveOrUpdateUnpaidInvoice(upInvoice);

				String invoiceInDB = chargebeeWebhookService.checkInvoiceInDB(invoice.getInvoice_id());
				int invoiceSaved = 0;
				if (invoiceInDB.equalsIgnoreCase("NA")) {
					invoiceSaved = chargebeeWebhookService.insertInvoice(invoice);
					whStatus.setEventStatus("Insert Invoice");
				} else {
					invoice.setId(Integer.parseInt(invoiceInDB));
					invoiceSaved = chargebeeWebhookService.updateInvoice(invoice);
					whStatus.setEventStatus("Update Invoice");
				}

				if (invoiceSaved > 0) {
					whStatus.setEventProcess("Invoice saved in DB");
					whStatus.setStatus(true);
				}
				whStatus.setSubscriptionInsert(allSubUpdateStatus + "," + historySubsUpdateStatus + ","
						+ creditsUpdateStatus + "," + invoiceSaved);
			} else if (event_type.equalsIgnoreCase("payment_succeeded")) {
				log.info("event_type : " + event_type);
				String url = moengage_event_url;
				url = url.replace("app_id_cus", moengage_app_id);
				String basicAuth = moengage_basic_auth;
				basicAuth = Base64.getEncoder().encodeToString(basicAuth.getBytes()).toString();

				JSONObject customer = content.getJSONObject("customer");
				JSONObject subscription = content.getJSONObject("subscription");

				JSONObject jsonData = new JSONObject();
				JSONArray actionList = new JSONArray();
				JSONObject jsonAction = new JSONObject();
				JSONObject jsonAttributes = new JSONObject();

				jsonAttributes.put("plan_id", subscription.get("plan_id").toString());

				jsonAction.put("action", "payment_succeeded");
				jsonAction.put("attributes", jsonAttributes);

				actionList.put(jsonAction);

				jsonData.put("actions", actionList);
				jsonData.put("type", "event");
				jsonData.put("customer_id", customer.get("email").toString());

				String response = _helper.httpPOSTRequestV2(url, basicAuth, jsonData.toString());
			}

		} catch (Exception e) {
			whStatus.setEventStatus("Error in chargebeeEventWebhookInvoiceProcess");
			log.error("Error in chargebeeEventWebhookInvoiceProcess " + e.getLocalizedMessage());
			return;
		} finally {
			boolean updateWebhookStatus = chargebeeWebhookService.updateWebHookStatus(whStatus);
			log.info("Event status Stored in DB : " + updateWebhookStatus == null ? "Not able to update in DB"
					: updateWebhookStatus);
		}
	}

	private void updateSubStatusInUserRetained(long user_id) {
		log.info("Entered into updateSubStatusInUserRetained :: user_id : " + user_id);
		try {
			boolean status = chargebeeWebhookService.updateSubStatusInUserRetained(user_id);
			log.info("update_sub_status_user_retained updated status : " + status);
		} catch (Exception e) {
			log.error("Error in updateSubStatusInUserRetained :: Error : " + e.getLocalizedMessage());
		}
	}

	private UnpaidInvoices setUnpaidInvoiceData(UnpaidInvoices invoiceInDB, Invoices invoices, String subStatus) {
		log.info("setUnpaidInvoiceData...");
		UnpaidInvoices upInvoice = new UnpaidInvoices();
		try {
			upInvoice.setInvoiceStatus(invoices.getInvoice_Status());

			if (invoiceInDB != null) {
				upInvoice.setId(invoiceInDB.getId());
				if (invoiceInDB.getInvoiceStatus().equalsIgnoreCase("payment_due")
						&& invoices.getInvoice_Status().equalsIgnoreCase("paid")) {
					upInvoice.setDue_date(invoiceInDB.getDue_date());
					upInvoice.setPaid_date(invoices.getInvoice_date());
					upInvoice.setPaidstatus(1);
					upInvoice.setInvoiceStatus("payment_due");
					if (subStatus.equalsIgnoreCase("inactive")) {
						return null;
					}
				} else if (invoiceInDB.getPaidstatus() == 1
						&& invoices.getInvoice_Status().equalsIgnoreCase("payment_due")) {
					upInvoice.setDue_date(invoices.getInvoice_date());
					upInvoice.setPaidstatus(0);
				} else if (invoiceInDB.getInvoiceStatus().equalsIgnoreCase("payment_due")
						&& invoices.getInvoice_Status().equalsIgnoreCase("payment_due")) {
					upInvoice.setDue_date(invoices.getInvoice_date());
					upInvoice.setPaidstatus(0);
				} else if (invoiceInDB.getInvoiceStatus().equalsIgnoreCase("payment_due")
						&& invoices.getInvoice_Status().equalsIgnoreCase("not_paid")) {
					upInvoice.setDue_date(invoiceInDB.getDue_date());
					upInvoice.setUnpaid_date(invoices.getInvoice_date());
					upInvoice.setPaidstatus(0);
					subStatus = "Inactive";
				} else if (invoiceInDB.getInvoiceStatus().equalsIgnoreCase("not_paid")
						&& invoices.getInvoice_Status().equalsIgnoreCase("paid")) {
					upInvoice.setDue_date(invoiceInDB.getDue_date());
					upInvoice.setUnpaid_date(invoiceInDB.getUnpaid_date());
					upInvoice.setPaid_date(invoices.getInvoice_date());
					upInvoice.setPaidstatus(1);
					upInvoice.setInvoiceStatus("not_paid");
					subStatus = "Active";
				} else if (invoices.getInvoice_Status().equalsIgnoreCase("voided")) {
					upInvoice.setDue_date(invoiceInDB.getDue_date());
					upInvoice.setUnpaid_date(invoiceInDB.getUnpaid_date());
					upInvoice.setPaid_date(invoiceInDB.getPaid_date());
					upInvoice.setInvoiceStatus("not_paid");
					upInvoice.setPaidstatus(invoiceInDB.getPaidstatus());
					subStatus = "voided";
				}
			} else {
				if (invoices.getInvoice_Status().equalsIgnoreCase("payment_due")) {
					upInvoice.setDue_date(invoices.getInvoice_date());
					subStatus = "Active";
				} else if (invoices.getInvoice_Status().equalsIgnoreCase("not_paid")) {
					upInvoice.setUnpaid_date(invoices.getInvoice_date());
					subStatus = "Inactive";
				}
			}

			upInvoice.setInvoice_id(invoices.getInvoice_id());
			if (invoices.getInvoice_Status().equalsIgnoreCase("voided"))
				upInvoice.setInvoice_id(invoiceInDB.getInvoice_id());
			upInvoice.setPlanid(invoices.getPlanName());
			upInvoice.setBilling_Email(invoices.getBilling_Email());
			upInvoice.setSub_id(invoices.getSub_id());
			upInvoice.setChargebeeId(invoices.getChargebeeId());
			upInvoice.setUpdated_date(IrisUtil.getCurrentTimeUTC());
			upInvoice.setStatus(subStatus);
		} catch (Exception e) {
			log.error("setUnpaidInvoiceData:" + e.getLocalizedMessage());
		}
		return upInvoice;
	}
	
	public void infobibEventhandling(JSONObject res, String event_type, ChargebeeWebhooksStatus whStatus) {

		try {
			if (event_type.equalsIgnoreCase("customer_created")) {
				String url = infobip_url + "/people/2/persons";

				JSONObject content = res.getJSONObject("content");
				String customerEmail = content.getJSONObject("customer").getString("email");
				String customerFirstname = content.getJSONObject("customer").getString("first_name");
				String customerLastname = content.getJSONObject("customer").getString("last_name");
				String customerphone = content.getJSONObject("customer").getString("phone");
				String customerId = content.getJSONObject("customer").getString("id");
				String formattedNumber = customerphone.replaceAll("[^0-9]", "");
				
				JSONObject requestBody = new JSONObject();
		        requestBody.put("externalId", customerId);
		        requestBody.put("firstName", customerFirstname);
		        requestBody.put("lastName", customerLastname);

		        JSONObject contactInformation = new JSONObject();
		        
		        JSONArray emailArray = new JSONArray();
		        JSONObject emailObject = new JSONObject();
		        emailObject.put("address", customerEmail);
		        emailArray.put(emailObject);
		        contactInformation.put("email", emailArray);

		        // Creating phone array
		        JSONArray phoneArray = new JSONArray();
		        JSONObject phoneObject = new JSONObject();
		        phoneObject.put("number", formattedNumber);
		        phoneArray.put(phoneObject);
		        contactInformation.put("phone", phoneArray);
		        
		        requestBody.put("contactInformation", contactInformation);

				String response1 = _helper.httpPOSTRequestV3(url, infobip_app_key, requestBody.toString());

			} else {
		        String convertEvent = event_type.replace("_", "");
		        
				JSONObject content = res.getJSONObject("content");
				String url = "";
				String subId = "";
				String planId = "";
				UserV4 user = userService.verifyAuthV4("chargebeeid",whStatus.getChargebeeId());
				if (user != null) {
				    String customerId = user.getEmail();
				    url = infobip_url + "/peopleevents/2/persons/" + customerId + "/definitions/" + convertEvent + "/events?identifierType=EMAIL";
				    
				    JSONObject properties = new JSONObject();
				    JSONObject requestBody = new JSONObject();

				    if (!event_type.equalsIgnoreCase("card_expired") && !event_type.equalsIgnoreCase("card_updated") && !event_type.equalsIgnoreCase("card_expired_reminder")) {
				        if (!content.isNull("subscription")) {
				            JSONObject subscrip = content.getJSONObject("subscription");
				            subId = subscrip.getString("id");
				            planId = subscrip.getString("plan_id");
				        }
				        properties.put("subscriptionId", subId);
				        properties.put("planName", planId);
				        properties.put("createdDate", IrisUtil.getCurrentTimeUTC());
				    }

				    requestBody.put("properties", properties);
				    String response1 = _helper.httpPOSTRequestV3(url, infobip_app_key, requestBody.toString());
				}
			}
		} catch (Exception e) {
			log.error("Exception in infobibEventhandling :" + e.getLocalizedMessage());
		}
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateDuplicateAllCBSubscription(AllSubscription prodSubs) {
		userService.updateDuplicateAllCBSubscription(prodSubs);
	}
	
	@Override
	@Async("asyncExecutor")
	public void updateDuplicateAllProductSubscription(AllProductSubscription prodSubs) {
		userService.updateDuplicateAllProductSubscription(prodSubs);
	}
}
