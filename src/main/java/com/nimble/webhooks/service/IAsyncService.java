package com.nimble.webhooks.service;

import org.json.JSONObject;

import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;

public interface IAsyncService {

	public void chargebeeEventWebhookInvoiceProcess(JSONObject res, ChargebeeWebhooksStatus chargebeeWebhooksStatus);

	public void checkAndRemoveUserCancelFeedBack(String chargebeeId);
	
	public void infobibEventhandling(JSONObject res, String event,ChargebeeWebhooksStatus whStatus);
	
	public void updateDuplicateAllCBSubscription(AllSubscription prodSubs);
	
	public void updateDuplicateAllProductSubscription(AllProductSubscription prodSubs);

	void asynSlackMessage(String title, String message);
}
