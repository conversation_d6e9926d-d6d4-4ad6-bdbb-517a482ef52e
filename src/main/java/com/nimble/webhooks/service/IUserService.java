package com.nimble.webhooks.service;

import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AdditionBenefitsCancelReward;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.UserCancelFeedBack;

public interface IUserService {

	public UserV4 verifyAuthV4(String key,String value);

	public UserV4 getUserByUNameOrEmail(String cbEmail);

	public boolean updateProductSubscription(long id);
	
	public int executeQuery(String qry);

	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id);

	public boolean deteleUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack);

	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id);

	public boolean deteleAdditionBenefitsCancelReward(AdditionBenefitsCancelReward additionBenefitsCancelReward);

	public long getUserByChargebeeId(String customerId);
	
	public String getQrcIdfromgatewayId(long gatewayid);
	
	public String getOrderIdfromQrc(String qrc);
	
	public boolean updateProductSubscriptionByOrderId(long id, String orderId);
	
	public boolean updateDuplicateAllCBSubscription(AllSubscription prodSubs);
	
	public boolean updateDuplicateAllProductSubscription(AllProductSubscription prodSubs);

	String getDeviceToken(long userId);
}
