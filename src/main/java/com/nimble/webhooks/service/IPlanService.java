package com.nimble.webhooks.service;

import java.util.ArrayList;

import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.GatewayFeature;

public interface IPlanService {

    public JResponse assignComboPlan(UserV4 user, long gateway_id, String chargebee_planid, String subscription_id);

    public ArrayList<GatewayFeature> getGatewayFeatureByGatewayIds(String gatewayIds);

    boolean isVetChatPlan(String planId);
    
    public void saveAllChargebeeSubs(long gatewayId, long monitorTypeId, AllProductSubscription subscription) ;
    
    public String getPlanConfig(String cbPlan);

    void handleVetchatPlanCancellation(String planId, UserV4 user);
}
