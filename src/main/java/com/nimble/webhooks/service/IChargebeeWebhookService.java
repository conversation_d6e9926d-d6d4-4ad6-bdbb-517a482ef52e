package com.nimble.webhooks.service;

import java.util.Date;
import java.util.List;

import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.CBCancelHistory;
import com.nimble.webhooks.entity.CbActivateCancelSubStatus;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.entity.Coupon;
import com.nimble.webhooks.entity.Credits;
import com.nimble.webhooks.entity.DeviceSubscription;
import com.nimble.webhooks.entity.Invoices;
import com.nimble.webhooks.entity.PlanToMonitorType;
import com.nimble.webhooks.entity.PlantoPeriod;
import com.nimble.webhooks.entity.Subscription;
import com.nimble.webhooks.entity.UnpaidInvoices;
import com.nimble.webhooks.entity.UserSubscription;
import org.json.JSONObject;

public interface IChargebeeWebhookService {

	public AllSubscription checkSubscriptionStatus(String subscriptionId);

	public String insertAllSubscription(AllSubscription subscription, boolean update);

	public String insertHistorySubscription(Subscription subscription, boolean update);

	public String insertCredit(Credits credits);

	public String getPlanToPeriod(String planId);

	public boolean checkAdditionalSubscription(String id);

	public CbActivateCancelSubStatus getUserDetails(String string);

	public boolean saveCancelActivateStatusData(CbActivateCancelSubStatus cSubs);

	public CbActivateCancelSubStatus getOrderDetails(CbActivateCancelSubStatus cSubs);

	//public int getVPMPlanTxnCount(String string);

	public CbActivateCancelSubStatus getCancelSubStatus(String chargebeeId);

	public ChargebeeWebhooksStatus webHookStatusIsAvailable(ChargebeeWebhooksStatus whStatus);

	public ChargebeeWebhooksStatus saveWebHookStatus(ChargebeeWebhooksStatus whStatus);
	
	public List<ChargebeeWebhooksStatus> getWebHookslist(Date fromDate);
	
	public ChargebeeWebhooksStatus saveWebHookStatusByEventId(ChargebeeWebhooksStatus whStatus);

	public boolean updateWebHookStatus(ChargebeeWebhooksStatus whStatus);

	public String checkInvoiceInDB(String invoice_id);

	public int updateInvoice(Invoices invoice);

	public int insertInvoice(Invoices invoice);

	public UserSubscription getUser_subscription(String chargebeeId);

	public int saveOrUpdateUser_subscription(UserSubscription userSub);

	public boolean saveOrUpdateUnpaidInvoice(UnpaidInvoices invoiceInDB);

	public UnpaidInvoices checkUnpaidInvoice(String sub_id, String chargebeeId);

	public UnpaidInvoices isUnpaidInvoice(String chargebeeId);

	public boolean saveRemoveAddon(String cus_id, String subId, String addon_id);

	public int updateAllCBSubscription(String curr_code,String ex_rate,String subs_id,String state_code);
	
	public boolean deleteAdditionalBenefits(String chargebeeid, String periodname);

	public boolean updateSubStatusInUserRetained(long user_id);
	
	public long getMonitorTypeByPlanId(String planId);

	public AllProductSubscription checkProductSubscriptionStatus(String subscriptionId);
	
	public String insertAllProductSubscription(AllProductSubscription subscriptions);

	public PlantoPeriod getPlanAndPeriodId(String planName);

	public long getUserByCbId(String cbId);

	public long getHostedPage(long userid, long monitortype_id);

	public void deleteHostedPageId(long userid, long monitortype_id);

	public PlantoPeriod getPlanToPeriodByMonitorId(long monitortype_id);

	public boolean deleteGatewayFeature(long gateway_id);

	public boolean insertOrUpdateCoupon(Coupon coupon);
	
	public String getPlanVersionbyplanname(String planName);
	
	public boolean saveMigratedSubsStatus(String cus_id, String subId);
	public int saveOrUpdateDevice_subscription(DeviceSubscription deviceSub);
	
	public boolean savePauseHistorysStatus(String subId);
	public DeviceSubscription getDevice_subscription(long gateway_id);
	
	public String getResumedatebySubId(String subId);
	
	public boolean deleteUserFeature(long userId);

	void deleteFlexiPlanHistory(long gatewayId, String sub_id);

	boolean saveCancelledSubscriptionHistory(CBCancelHistory cancelHistory);

	long getHostedPage(long userid);
	
	public String getPlanType(String cbPlanid);
	
	public boolean updateComboPlanStatus(AllProductSubscription subs);

	PlanToMonitorType getPlanToMonitorType(long planId);

	public AllProductSubscription getAllProductSubscriptionBySubId(String subscriptionId);

	public String insertAllProductSubscriptionSQL(AllProductSubscription allSubscription);

	boolean updateMRRInAllCB(String subId, long mrr, boolean isAllCBSub);

	String getSubExistIn(String subId);
	public boolean updateProductSubscriptionStatus(String subId, String subs_status);
	
	public boolean deleteGatewayFeatureBySubId(String sub_id);
	
	public boolean updateComboPlanMonitorType(AllProductSubscription subs);
	
	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeId);

	void handleVetchatPlanActivation(String planId, UserV4 user);
	
	public boolean changeGatewayStatus(long gatewayId, boolean isEnable);

	boolean updateSubStatusinAllProductSub(String subscriptionId, boolean isAllCbSub);
	
	public long findPetmonitorincomboPlan(String subId);
	
	public boolean deleteVetChatSub(long mtype, String subid);
	
	public boolean getduplicatewithactivesub(String subId, String chargbeeId, long gatewayId);
	
	public boolean getNonduplicateAllproductSubscription(String subId, String chargbeeId, long gatewayId,String email);
	
	public DeviceSubscription getDevice_subscription(String sub_id);
	
	public boolean updateDevicesubscriptionForV2Plan(DeviceSubscription deviceSub);

	void chargebeeEventWebhookProcess(JSONObject res, ChargebeeWebhooksStatus whStatus);

	public boolean saveSubsStatusFromcombomigration(String subId, long gateway_id);
}
