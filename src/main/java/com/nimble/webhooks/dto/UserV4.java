package com.nimble.webhooks.dto;

import com.nimble.webhooks.entity.Role;

public class UserV4 {

	private long id;
	
	private String authKey;

	private String password;

	private String username;

	private long cmpId;
	
	private Role role;

	private String email;

	private String mobileno;

	private int webappid;

	private int mobileappid;

	private boolean enable;

	private int resetpassword;

	private boolean notification;

	private String alternateemail = "NA";

	private String alternatephone = "NA";
	
	private String updatedOn = "1753-01-01 00:00:00";
	
	private boolean isVerified = false;

	private String firstname = "NA";

	private String lastname = "NA";

	private String zipcode = "NA";
	
	private String signupToken = "NA";
	
	private long signupTypeId;

	private String city = "NA";

	private String state = "NA";

	private String country = "NA";

	private String createdOn = "1753-01-01 00:00:00";

	private String imageUrl = "NA";

	private String chargebeeid = "NA";

	private boolean in_app = true;
	
	private int lastlogintype;
	
	private boolean completesetup = true;
	
	private String lastlogintime = "1753-01-01 00:00:00";

	private long role_id;
	
	private int inapp_purchase = 0;
	
	public UserV4() {
		super();
	}

	@Override
	public String toString() {
		return "UserV4 [id=" + id + ", authKey=" + authKey + ", password=" + password + ", username=" + username
				+ ", cmpId=" + cmpId + ", role=" + role + ", email=" + email + ", mobileno=" + mobileno + ", webappid="
				+ webappid + ", mobileappid=" + mobileappid + ", enable=" + enable + ", resetpassword=" + resetpassword
				+ ", notification=" + notification + ", alternateemail=" + alternateemail + ", alternatephone="
				+ alternatephone + ", updatedOn=" + updatedOn + ", isVerified=" + isVerified + ", firstname="
				+ firstname + ", lastname=" + lastname + ", zipcode=" + zipcode + ", signupToken=" + signupToken
				+ ", signupTypeId=" + signupTypeId + ", city=" + city + ", state=" + state + ", country=" + country
				+ ", createdOn=" + createdOn + ", imageUrl=" + imageUrl + ", chargebeeid=" + chargebeeid + ", in_app="
				+ in_app + ", lastlogintype=" + lastlogintype + ", completesetup=" + completesetup + ", lastlogintime="
				+ lastlogintime + ", getId()=" + getId() + ", getAuthKey()=" + getAuthKey() + ", getPassword()="
				+ getPassword() + ", getUsername()=" + getUsername() + ", getCmpId()=" + getCmpId() + ", getRole()="
				+ getRole() + ", getEmail()=" + getEmail() + ", getMobileno()=" + getMobileno() + ", getWebappid()="
				+ getWebappid() + ", getMobileappid()=" + getMobileappid() + ", isEnable()=" + isEnable()
				+ ", getResetpassword()=" + getResetpassword() + ", isNotification()=" + isNotification()
				+ ", getAlternateemail()=" + getAlternateemail() + ", getAlternatephone()=" + getAlternatephone()
				+ ", getUpdatedOn()=" + getUpdatedOn() + ", isVerified()=" + isVerified() + ", getFirstname()="
				+ getFirstname() + ", getLastname()=" + getLastname() + ", getZipcode()=" + getZipcode()
				+ ", getSignupToken()=" + getSignupToken() + ", getSignupTypeId()=" + getSignupTypeId() + ", getCity()="
				+ getCity() + ", getState()=" + getState() + ", getCountry()=" + getCountry() + ", getCreatedOn()="
				+ getCreatedOn() + ", getImageUrl()=" + getImageUrl() + ", getChargebeeid()=" + getChargebeeid()
				+ ", isIn_app()=" + isIn_app() + ", getLastlogintype()=" + getLastlogintype() + ", isCompletesetup()="
				+ isCompletesetup() + ", getLastlogintime()=" + getLastlogintime() + ", getClass()=" + getClass()
				+ ", hashCode()=" + hashCode() + ", toString()=" + super.toString() + ", role_id=" + role_id + "]";
	}

	public UserV4(long id, String authKey, String password, String username, long cmpId, Role role, String email,
			String mobileno, int webappid, int mobileappid, boolean enable, int resetpassword, boolean notification,
			String alternateemail, String alternatephone, String updatedOn, boolean isVerified, String firstname,
			String lastname, String zipcode, String signupToken, long signupTypeId, String city, String state,
			String country, String createdOn, String imageUrl, String chargebeeid, boolean in_app, int lastlogintype,
			boolean completesetup, String lastlogintime, long role_id) {
		super();
		this.id = id;
		this.authKey = authKey;
		this.password = password;
		this.username = username;
		this.cmpId = cmpId;
		this.role = role;
		this.email = email;
		this.mobileno = mobileno;
		this.webappid = webappid;
		this.mobileappid = mobileappid;
		this.enable = enable;
		this.resetpassword = resetpassword;
		this.notification = notification;
		this.alternateemail = alternateemail;
		this.alternatephone = alternatephone;
		this.updatedOn = updatedOn;
		this.isVerified = isVerified;
		this.firstname = firstname;
		this.lastname = lastname;
		this.zipcode = zipcode;
		this.signupToken = signupToken;
		this.signupTypeId = signupTypeId;
		this.city = city;
		this.state = state;
		this.country = country;
		this.createdOn = createdOn;
		this.imageUrl = imageUrl;
		this.chargebeeid = chargebeeid;
		this.in_app = in_app;
		this.lastlogintype = lastlogintype;
		this.completesetup = completesetup;
		this.lastlogintime = lastlogintime;
		this.role_id = role_id;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getAuthKey() {
		return authKey;
	}

	public void setAuthKey(String authKey) {
		this.authKey = authKey;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public long getCmpId() {
		return cmpId;
	}

	public void setCmpId(long cmpId) {
		this.cmpId = cmpId;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobileno() {
		return mobileno;
	}

	public void setMobileno(String mobileno) {
		this.mobileno = mobileno;
	}

	public int getWebappid() {
		return webappid;
	}

	public void setWebappid(int webappid) {
		this.webappid = webappid;
	}

	public int getMobileappid() {
		return mobileappid;
	}

	public void setMobileappid(int mobileappid) {
		this.mobileappid = mobileappid;
	}

	public boolean isEnable() {
		return enable;
	}

	public void setEnable(boolean enable) {
		this.enable = enable;
	}

	public int getResetpassword() {
		return resetpassword;
	}

	public void setResetpassword(int resetpassword) {
		this.resetpassword = resetpassword;
	}

	public boolean isNotification() {
		return notification;
	}

	public void setNotification(boolean notification) {
		this.notification = notification;
	}

	public String getAlternateemail() {
		return alternateemail;
	}

	public void setAlternateemail(String alternateemail) {
		this.alternateemail = alternateemail;
	}

	public String getAlternatephone() {
		return alternatephone;
	}

	public void setAlternatephone(String alternatephone) {
		this.alternatephone = alternatephone;
	}

	public String getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(String updatedOn) {
		this.updatedOn = updatedOn;
	}

	public boolean isVerified() {
		return isVerified;
	}

	public void setVerified(boolean isVerified) {
		this.isVerified = isVerified;
	}

	public String getFirstname() {
		return firstname;
	}

	public void setFirstname(String firstname) {
		this.firstname = firstname;
	}

	public String getLastname() {
		return lastname;
	}

	public void setLastname(String lastname) {
		this.lastname = lastname;
	}

	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	public String getSignupToken() {
		return signupToken;
	}

	public void setSignupToken(String signupToken) {
		this.signupToken = signupToken;
	}

	public long getSignupTypeId() {
		return signupTypeId;
	}

	public void setSignupTypeId(long l) {
		this.signupTypeId = l;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(String createdOn) {
		this.createdOn = createdOn;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public String getChargebeeid() {
		return chargebeeid;
	}

	public void setChargebeeid(String chargebeeid) {
		this.chargebeeid = chargebeeid;
	}

	public boolean isIn_app() {
		return in_app;
	}

	public void setIn_app(boolean in_app) {
		this.in_app = in_app;
	}

	public int getLastlogintype() {
		return lastlogintype;
	}

	public void setLastlogintype(int result) {
		this.lastlogintype = result;
	}

	public boolean isCompletesetup() {
		return completesetup;
	}

	public void setCompletesetup(boolean completesetup) {
		this.completesetup = completesetup;
	}

	public String getLastlogintime() {
		return lastlogintime;
	}

	public void setLastlogintime(String lastlogintime) {
		this.lastlogintime = lastlogintime;
	}

	public long getRole_id() {
		return role_id;
	}

	public void setRole_id(long role_id) {
		this.role_id = role_id;
	}

	public int getInapp_purchase() {
		return inapp_purchase;
	}

	public void setInapp_purchase(int inapp_purchase) {
		this.inapp_purchase = inapp_purchase;
	}
	
}
