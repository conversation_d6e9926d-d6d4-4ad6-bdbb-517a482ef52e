package com.nimble.webhooks.dto;

public class JKcalDetails {

	private String name;
	
	private String email = "NA";
	
	private String userName = "NA";
	
	private String pet_category = "NA";
	
	private String weight = "NA";
	
	private String activity_level = "NA";
	
	private String body_condition = "NA";
	
	private String pet_criteria = "NA";
	
	private String kcal = "NA";
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPet_category() {
		return pet_category;
	}

	public void setPet_category(String pet_category) {
		this.pet_category = pet_category;
	}

	public String getWeight() {
		return weight;
	}

	public void setWeight(String weight) {
		this.weight = weight;
	}

	public String getActivity_level() {
		return activity_level;
	}

	public void setActivity_level(String activity_level) {
		this.activity_level = activity_level;
	}

	public String getBody_condition() {
		return body_condition;
	}

	public void setBody_condition(String body_condition) {
		this.body_condition = body_condition;
	}

	public String getPet_criteria() {
		return pet_criteria;
	}

	public void setPet_criteria(String pet_criteria) {
		this.pet_criteria = pet_criteria;
	}

	public String getKcal() {
		return kcal;
	}

	public void setKcal(String kcal) {
		this.kcal = kcal;
	}

}
