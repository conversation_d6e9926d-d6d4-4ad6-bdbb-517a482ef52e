package com.nimble.webhooks.dto;

public class GatewayInfo {

    private long gateway_id;

    private String meid = "NA";

    private long monitor_type_id;

    private String sub_status = "NA";

    private String lastReportTime = "1753-01-01 00:00:00";

    private String sub_id = "NA";

	private String plan_id = "NA";

    public GatewayInfo() {
    }

    public GatewayInfo(long gateway_id, String meid, long monitor_type_id) {
        this.gateway_id = gateway_id;
        this.meid = meid;
        this.monitor_type_id = monitor_type_id;
    }

    public long getGateway_id() {
        return gateway_id;
    }

    public void setGateway_id(long gateway_id) {
        this.gateway_id = gateway_id;
    }

    public String getMeid() {
        return meid;
    }

    public void setMeid(String meid) {
        this.meid = meid;
    }

    public long getMonitor_type_id() {
        return monitor_type_id;
    }

    public void setMonitor_type_id(long monitor_type_id) {
        this.monitor_type_id = monitor_type_id;
    }

    public String getSub_status() {
        return sub_status;
    }

    public void setSub_status(String sub_status) {
        this.sub_status = sub_status;
    }

    public String getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(String lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public String getSub_id() {
        return sub_id;
    }

    public void setSub_id(String sub_id) {
        this.sub_id = sub_id;
    }

	public String getPlan_id() {
		return plan_id;
	}

	public void setPlan_id(String plan_id) {
		this.plan_id = plan_id;
	}

}
