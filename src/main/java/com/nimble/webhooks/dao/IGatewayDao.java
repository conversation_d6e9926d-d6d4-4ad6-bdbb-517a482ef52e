package com.nimble.webhooks.dao;

import java.util.ArrayList;
import java.util.HashMap;

import com.nimble.webhooks.dto.GatewayInfo;

public interface IGatewayDao {

	public long getgatewayByMonitorId(long userid, long monitortype_id);
	public HashMap<String, String> getGatewayDetail(long gateway_id);
	public long getMonitorType(String cbPlanid);

	long getgatewayById(long gatewayId);

	public ArrayList<Long> getGatewayIdsByUserId(long userId);

	public ArrayList<GatewayInfo> getGatewayInfo(String gatewayIds);
	
	public ArrayList<Long> getPlanmappedGateway(String chargebeeId, String subscription_id);

	public ArrayList<GatewayInfo> getUserGatewaySubInfo(String userId, boolean isUserBased);
	
	public int getPlanbaseddevicecnt(String planId);
}
