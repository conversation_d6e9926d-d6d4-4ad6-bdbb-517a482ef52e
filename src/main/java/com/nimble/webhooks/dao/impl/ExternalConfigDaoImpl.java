package com.nimble.webhooks.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IExternalConfigDao;
import com.nimble.webhooks.entity.CBShopifyOrders;
import com.nimble.webhooks.entity.ExternalConfig;

@Repository
public class ExternalConfigDaoImpl implements IExternalConfigDao {

	@Autowired
	private SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(ExternalConfigDaoImpl.class);

	@Override
	public ExternalConfig getExternalConfig(String name) throws DataIntegrityViolationException {

		try {

			List<ExternalConfig> externalConfig = new ArrayList<ExternalConfig>();

			log.info("Gettting all external configuration.");

			String query = "from ExternalConfig where parametername=" + "'" + name + "'";

			externalConfig = (List<ExternalConfig>) this.sessionFactory.getCurrentSession().createQuery(query).list();

			if (externalConfig.size() > 0) {
				log.info("Found  external configuration for name : " + name);
				return externalConfig.get(0);

			} else {
				log.info("No external configuration found for name : " + name);
				return null;
			}

		} catch (HibernateException e) {
			log.error("Error while getting external configuration:" + e.getLocalizedMessage());
			return null;
		}

	}

	@Override
	public ArrayList<CBShopifyOrders> getCBShopifyOrderInfo() {

		ArrayList<CBShopifyOrders> cbShopifyOrd = new ArrayList<CBShopifyOrders>();
		try {
			Session session = sessionFactory.getCurrentSession();
			cbShopifyOrd = (ArrayList<CBShopifyOrders>) session.createQuery("from CBShopifyOrders where enable=1")
					.list();
			return cbShopifyOrd;

		} catch (Exception e) {
			System.out.println(e.getMessage());
			log.error("Error while get Chargbee Shopify Order Info." + e.getLocalizedMessage());
		}
		return cbShopifyOrd;
	}

}
