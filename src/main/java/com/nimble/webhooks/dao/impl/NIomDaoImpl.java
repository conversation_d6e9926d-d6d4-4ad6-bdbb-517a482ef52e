package com.nimble.webhooks.dao.impl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.INiomDao;
import com.nimble.webhooks.entity.CbActivateCancelSubStatus;

@Repository
public class NIomDaoImpl implements INiomDao {

	@Autowired
	private SessionFactory niomSessionFactory;
	
	private static final Logger log = LogManager.getLogger(NIomDaoImpl.class);
	
	@Override
	public CbActivateCancelSubStatus getOrderDetails(CbActivateCancelSubStatus cSubs) {
		log.info("Enter in cancelSub :: DAO");
		try {
			Session sessionNiom = niomSessionFactory.getCurrentSession();
			
			String simNo = "NA";
			String meId = "NA";
			
			String qry = "select order_id,meid from ordermap where user_id='"+cSubs.getUserId()+"'";
			Query query = sessionNiom.createSQLQuery(qry);
			Object[] obj = (Object[]) query.list().get(0);
			
//			long orderId = ((BigInteger) obj[0]).longValue();
			meId = (String) obj[1];
			
			qry = "select sim_no from inventory where meid='"+meId+"'";
			query = sessionNiom.createSQLQuery(qry);
			simNo = (String) query.list().get(0);
//			cSubs.setOrderId(orderId);
			cSubs.setSimNo(simNo);
			return cSubs;
		} catch( Exception e ) {
			log.error("Error in cancelSub :: DAO "+e.getLocalizedMessage() );
			return null;
		}
	}

	@Override
	public boolean changeDeviceStatus(long orderId) {
		try {
			Session sessionNiom = niomSessionFactory.getCurrentSession();
			String qry = "UPDATE ordermap SET device_status = 'Suspended' WHERE order_id = "+orderId+";";
			int i = sessionNiom.createSQLQuery(qry).executeUpdate();
			return ( i > 0 ) ? true : false;
		} catch (Exception e) {
			return false;
		}
	}

}
