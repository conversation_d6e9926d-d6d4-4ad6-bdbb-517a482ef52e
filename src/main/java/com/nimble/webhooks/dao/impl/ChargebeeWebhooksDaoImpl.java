package com.nimble.webhooks.dao.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.persistence.PersistenceException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.LogicalExpression;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.query.NativeQuery;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IChargebeeWebhooksDao;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.CBCancelHistory;
import com.nimble.webhooks.entity.CbActivateCancelSubStatus;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.entity.Coupon;
import com.nimble.webhooks.entity.Credits;
import com.nimble.webhooks.entity.DeviceSubscription;
import com.nimble.webhooks.entity.Invoices;
import com.nimble.webhooks.entity.PlanToMonitorType;
import com.nimble.webhooks.entity.PlantoPeriod;
import com.nimble.webhooks.entity.SubPeriod;
import com.nimble.webhooks.entity.Subscription;
import com.nimble.webhooks.entity.UnpaidInvoices;
import com.nimble.webhooks.entity.UserSubscription;
import com.nimble.webhooks.helper.Helper;

@Repository
public class ChargebeeWebhooksDaoImpl implements IChargebeeWebhooksDao {

    @Autowired
    private SessionFactory sessionFactory;

    private static final Logger log = LogManager.getLogger(ChargebeeWebhooksDaoImpl.class);

    @Autowired
    Helper _helper;
    
    @Value("${microservice_url}")
	private String microservice_url;

    @Override
    public AllSubscription checkSubscription(String subscriptionId) {
        log.info("Enter in checkSubscription :: DAO ");
        AllSubscription sub = null;
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(AllSubscription.class);
            // cr.setProjection(Projections.property("id"));
            cr.add(Restrictions.eq("subscriptionId", subscriptionId));
            List list = cr.list();
            if (list.isEmpty()) {
                sub = null;
            } else {
                sub = (AllSubscription) list.get(0);
            }
        } catch (Exception e) {
            log.error("Error in checkSubscription :" + e.getLocalizedMessage());
        }

        return sub;
    }

    @Override
    public String updateHistorySubscription(Subscription subscription) {
        log.info(" Entered in updateSubscription :: DAO");
        try {
            sessionFactory.getCurrentSession().update(subscription);
            return "1";
        } catch (Exception e) {
            log.error(" Error in updateHistorySubscription : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public String insertHistorySubscription(Subscription subscription) {
        log.info(" Entered in insertHistorySubscription :: DAO ");
        try {
            sessionFactory.getCurrentSession().saveOrUpdate(subscription);
            return "1";
        } catch (Exception e) {
            log.error(" Error in insertHistorySubscription : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public String updateAllSubscription(AllSubscription subscription) {
        log.info(" Entered in updateSubscription :: DAO");
        try {
            sessionFactory.getCurrentSession().update(subscription);
            return "1";
        } catch (Exception e) {
            log.error(" Error in updateAllSubscription : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public String insertAllSubscription(AllSubscription subscription) {
        log.info(" Entered in insertAllSubscription :: DAO ");
        try {
            sessionFactory.getCurrentSession().saveOrUpdate(subscription);
            return "1";
        } catch (Exception e) {
            log.error(" Error in insertAllSubscription : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public Credits checkCredit(String chargebee_id) {
        log.info(" Entered in checkCredit :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(Credits.class);
            cr.add(Restrictions.eq("chargebee_id", chargebee_id));
            List list = cr.list();
            if (list.isEmpty()) {
                return null;
            } else {
                return (Credits) list.get(0);
            }
        } catch (Exception e) {
            log.error(" Error in checkCredit : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public String saveCredits(Credits credits) {
        log.info(" Entered in saveCredits :: DAO ");
        try {
            log.info(credits.getChargebee_id() + " P.Cre : " + credits.getPromotional_credits() + " R.Cre : "
                         + credits.getRefundable_credits());
            sessionFactory.getCurrentSession().save(credits);
            return "1";
        } catch (Exception e) {
            log.error("Error in saveCredits: " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public String updateCredits(Credits credits) {
        log.info(" Entered in updateCredits :: DAO ");
        try {
            log.info(credits.getChargebee_id() + " P.Cre : " + credits.getPromotional_credits() + " R.Cre : "
                         + credits.getRefundable_credits());
            this.sessionFactory.getCurrentSession().merge(credits);
            return "1";
        } catch (Exception e) {
            log.error("Error in updateCredits: " + e.getLocalizedMessage());
            return "0";
        }

	}

    @Override
    public String getPlanToPeriod(String planId) {
        log.info(" Entered in getPlanToPeriod :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(PlantoPeriod.class);
            cr.setProjection(Projections.property("sub_period_id"));
            cr.add(Restrictions.eq("chargebee_planid", planId));
            List list = cr.list();

            if (list.isEmpty()) {
                return "NA";
            } else {
                log.info(cr.list().get(0));
                Criteria cri = session.createCriteria(SubPeriod.class);
                cri.setProjection(Projections.property("period_name"));
                cri.add(Restrictions.eq("id", Integer.parseInt(String.valueOf(cr.list().get(0)))));
                List listPlan = cri.list();

                if (listPlan.isEmpty()) {
                    return "NA";
                } else {
                    log.info(listPlan.get(0));
                    return String.valueOf(listPlan.get(0)).toUpperCase();
                }
            }
        } catch (Exception e) {
            log.error("Error in getPlanToPeriod: " + e.getLocalizedMessage());
            return "NA";
        }
    }

    @Override
    public CbActivateCancelSubStatus getUserDetails(String chargebeeId) {
        log.info(" Entered in getUserDetails :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            CbActivateCancelSubStatus cSubs = new CbActivateCancelSubStatus();

            String qry = "select id,authkey from user where chargebeeid='" + chargebeeId + "'";
            SQLQuery query = session.createSQLQuery(qry);
            Object[] obj = (Object[]) query.list().get(0);
            long userId = ((BigInteger) obj[0]).longValue();
//			String authKey = (String) obj[1];

//			qry = "select gatewayId from usergateway where userId = '"+userId+"'";
//			query = session.createSQLQuery(qry);
//			gatewayId = ((BigInteger) query.list().get(0)).longValue();

            cSubs.setUserId(String.valueOf(userId));
//			cSubs.setGatewayId(gatewayId);
            cSubs.setChargebeeId(chargebeeId);
            return cSubs;
        } catch (Exception e) {
            log.error("Error in getUserDetails: " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public boolean saveCancelActivateStatusData(CbActivateCancelSubStatus cSubs) {
        log.info(" Entered in saveCancelActivateStatusData :: DAO ");
        try {
            sessionFactory.getCurrentSession().merge(cSubs);
            return true;
        } catch (Exception e) {
            log.error("Error in saveCancelActivateStatusData: " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public boolean changeGatewayStatus(long gatewayId, boolean isEnable) {
        log.info(" Entered in changeGatewayStatus :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "UPDATE gateway SET isenable = "+isEnable+" WHERE id = " + gatewayId + ";";
            int i = session.createSQLQuery(qry).executeUpdate();
            log.info("changeGatewayStatus:"+gatewayId+" : "+i);
            return (i > 0) ? true : false;
        } catch (Exception e) {
            log.error("Error in changeGatewayStatus: " + e.getLocalizedMessage());
            return false;
        }
    }

//	@Override
//	public int getVPMPlanTxnCount(String cb_planid) {
//		log.info(" Entered in getVPMPlanTxnCount :: DAO ");
//		try {
//			String qry = "SELECT txn_limit FROM plan_feature  WHERE plan_period_id = ( SELECT id FROM plan_to_period "
//					+ "WHERE chargebee_planid= '" + cb_planid + "');";
//
//			Query query = this.sessionFactory.getCurrentSession().createSQLQuery(qry);
//
//			List res = query.list();
//			int cnt = 0;
//
//			if (!res.isEmpty())
//				cnt = (int) res.get(0);
//
//			return cnt;
//		} catch (Exception e) {
//			log.error("Exception in getVPMPlanTxnCount: " + e.getLocalizedMessage());
//			return 0;
//		}
//	}

    @Override
    public CbActivateCancelSubStatus getCancelSub(String eventId) {
        log.info(" Entered in getCancelSub :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();

            Criteria criteria = session.createCriteria(CbActivateCancelSubStatus.class)
                                    .add(Restrictions.eq("eventId", eventId));
            CbActivateCancelSubStatus cSubs = (CbActivateCancelSubStatus) criteria.list().get(0);

            return cSubs;
        } catch (Exception e) {
            log.error("Error in getCancelSub : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public ChargebeeWebhooksStatus saveWebHookStatus(ChargebeeWebhooksStatus whStatus) {
        try {
            whStatus = (ChargebeeWebhooksStatus) sessionFactory.getCurrentSession().merge(whStatus);
            return whStatus;
        } catch (Exception e) {
            log.error("Error in saveWebHookStatus : " + e.getLocalizedMessage());
            return null;
        }
    }

    public int executeQuery(String qry) {
        log.info("Entered into executeQuery: ");
        try {
            Session create_ses = sessionFactory.getCurrentSession();
            NativeQuery createQuery = create_ses.createSQLQuery(qry);
            int Cstatus = createQuery.executeUpdate();
            log.info("Query :" + qry);
            log.info("executeStatus :" + Cstatus);
            return Cstatus;
        } catch (IndexOutOfBoundsException e) {
            log.error("Query : " + qry);
            log.error("executeQuery: " + e.getMessage());
            return 0;
        } catch (Exception e) {
            log.error("Query : " + qry);
            log.error("executeQuery: " + e.getMessage());
            return 0;
        }
    }

    @Override
    public ChargebeeWebhooksStatus webHookStatusIsAvailable(ChargebeeWebhooksStatus whStatus) {
        log.info(" Entered in webHookStatusIsAvailable :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(ChargebeeWebhooksStatus.class);
            Criterion eventId = Restrictions.eq("eventId", whStatus.getEventId());
            Criterion status = Restrictions.eq("status", true);
            LogicalExpression expression = Restrictions.and(eventId, status);
            cr.add(expression);
            List list = cr.list();

            if (!list.isEmpty() && list.size() > 0)
                return (ChargebeeWebhooksStatus) list.get(0);
            return null;
        } catch (Exception e) {
            log.error("Error in webHookStatusIsAvailable : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public ChargebeeWebhooksStatus saveWebHookStatusByEventId(ChargebeeWebhooksStatus whStatus) {
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(ChargebeeWebhooksStatus.class)
                              .add(Restrictions.eq("eventId", whStatus.getEventId()));
            List list = cr.list();

            if (!list.isEmpty() && list.size() > 0) {
                whStatus.setId(((ChargebeeWebhooksStatus) list.get(0)).getId());
                whStatus = (ChargebeeWebhooksStatus) sessionFactory.getCurrentSession().merge(whStatus);
                return whStatus;
            }
            return null;
        } catch (Exception e) {
            log.error("Error in saveWebHookStatusByEventId : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public boolean updateWebhookStatus(ChargebeeWebhooksStatus whStatus) {
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "UPDATE chargebee_webhooks_status SET vpm_status=" + whStatus.getVpmStatus()
                             + ",activate_sim_status=" + whStatus.getActivateSimStatus() + ",deactivate_sim_status="
                             + whStatus.getDeactivateSimStatus() + ",`status`=" + whStatus.isStatus() + ",`event_process`='"
                             + whStatus.getEventProcess() + "',`event_status`='" + whStatus.getEventStatus()
                             + "',`subscription_insert`='" + whStatus.getSubscriptionInsert() + "' ,`planfeature_status`='"
                             + whStatus.getPlanfeatureStatus() + "' WHERE event_id ='" + whStatus.getEventId() + "'";
            int i = session.createSQLQuery(qry).executeUpdate();
            return (i >= 1 ? true : false);
        } catch (Exception e) {
            log.error("Error in updateWebhookStatus : " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public String checkInvoiceInDB(String invoice_id) {
        log.info("Entered into checkInvoiceInDB :: invoice id : " + invoice_id);
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(Invoices.class);
            cr.setProjection(Projections.property("id"));
            cr.add(Restrictions.eq("invoice_id", invoice_id));
            List list = cr.list();
            if (list.isEmpty()) {
                return "NA";
            } else {
                System.out.println(list.get(0));
                return String.valueOf(list.get(0));
            }
        } catch (Exception e) {
            log.error("Error in checkInvoiceInDB " + e.getLocalizedMessage());
            return "NA";
        }

    }

    @Override
    public int updateInvoice(Invoices invoice) {
        log.info("Entered into updateInvoice : invoice id : " + invoice.getInvoice_id());
        try {
            sessionFactory.getCurrentSession().update(invoice);
            return 1;
        } catch (Exception e) {
            log.error("Error in updateInvoice " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public int insertInvoice(Invoices invoice) {
        log.info("Entered into checkInvoiceInDB :: invoice id : " + invoice.getInvoice_id());
        try {
            sessionFactory.getCurrentSession().save(invoice);
            return 1;
        } catch (Exception e) {
            log.error("Error in insertInvoice " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public UserSubscription getUser_subscription(String chargebeeId) {
        log.info(" Entered in getUser_subscription :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(UserSubscription.class);
            Criterion cr1 = Restrictions.eq("chargebeeid", chargebeeId);
            cr.add(cr1);

            List list = cr.list();

            if (!list.isEmpty() && list.size() > 0)
                return (UserSubscription) list.get(0);

            return null;
        } catch (Exception e) {
            log.error("Error in getUser_subscription : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public DeviceSubscription getDevice_subscription(long gateway_id) {
        log.info(" Entered in getDevice_subscription :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(DeviceSubscription.class);
            Criterion cr1 = Restrictions.eq("gateway_id", gateway_id);
            Criterion cr2 = Restrictions.eq("is_deleted", false);
            cr.add(Restrictions.and(cr1, cr2));
            cr.add(cr1);

            List list = cr.list();

            if (!list.isEmpty() && list.size() > 0)
                return (DeviceSubscription) list.get(0);

            return null;
        } catch (Exception e) {
            log.error("Error in getDevice_subscription : " + e.getLocalizedMessage());
            return null;
        }
    }
    
    @Override
    public DeviceSubscription getDevice_subscription(String sub_id) {
        log.info(" Entered in getDevice_subscription1 :: DAO ");
        try {
            Session session = this.sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(DeviceSubscription.class);
            Criterion cr1 = Restrictions.eq("sub_id", sub_id);
            Criterion cr2 = Restrictions.eq("is_deleted", false);
            cr.add(Restrictions.and(cr1, cr2));
            cr.add(cr1);

            List list = cr.list();

            if (!list.isEmpty() && list.size() > 0)
                return (DeviceSubscription) list.get(0);

            return null;
        } catch (Exception e) {
            log.error("Error in getDevice_subscription1 : " + e.getLocalizedMessage());
            return null;
        }
    }
    @Override
    public int saveOrUpdateUser_subscription(UserSubscription userSub) {
        log.info("Entered into saveOrUpdateUser_subscription :: Chargebee id : " + userSub.getChargebeeid());
        try {
            sessionFactory.getCurrentSession().saveOrUpdate(userSub);
            return 1;
        } catch (Exception e) {
            log.error("Error  in saveOrUpdateUser_subscription : " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public int saveOrUpdateDevice_subscription(DeviceSubscription deviceSub) {
        log.info("Entered into saveOrUpdateDevice_subscription :: Chargebee id : " + deviceSub.getChargebeeid());
        try {
            sessionFactory.getCurrentSession().saveOrUpdate(deviceSub);
            return 1;
        } catch (Exception e) {
            log.error("Error  in saveOrUpdateDevice_subscription : " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    // To update device sub details for more than one device plan and combo plan
    public boolean updateDevicesubscriptionForV2Plan(DeviceSubscription deviceSub) {
        log.info(" Entered in updateDevicesubscriptionForV2Plan :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String curTime = new Helper().getCurrentTimeinUTC();
            String qry = "UPDATE `device_subscription` SET "
            		+ "`sub_status` = '"+deviceSub.getSub_status()+"',"
            		+ "`plan_id`= '"+deviceSub.getPlan_id()+"',"
            		+ "`prev_planid`= '"+deviceSub.getPrev_planid()+"',"
            		+ "`prev_period`= '"+deviceSub.getPrev_period()+"',"
            		+ "`prev_plan_startdt`= '"+deviceSub.getPrev_plan_startdt()+"',"
            		+ "`prev_plan_enddt`= '"+deviceSub.getPrev_plan_enddt()+"',"
            		+ "`cur_plan_startdt`= '"+deviceSub.getCur_plan_startdt()+"',"
            		+ "`cur_plan_enddt`= '"+deviceSub.getCur_plan_enddt()+"',"
            		+ "`next_renewal_dt`= '"+deviceSub.getNext_renewal_dt()+"',"
                    + "`cancel_date`= '"+deviceSub.getCancel_date()+"',"
                    + "`cur_plan_activated_date`= '"+deviceSub.getCur_plan_activated_date()+"',"
            		+ "updated_on ='"+curTime+"' "
            		+ " WHERE `sub_id`= '"+deviceSub.getSub_id()+"';";
            int i = session.createSQLQuery(qry).executeUpdate();

            return (i > 0) ? true : false;
        } catch (Exception e) {
            log.error("Error in updateDevicesubscriptionForV2Plan: " + e.getLocalizedMessage());
            return false;
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<ChargebeeWebhooksStatus> getWebHookslist(Date fromDate) {
        log.info(" Entered in getWebHookslist :: DAO ");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.s");
            String fromdate = sdf.format(fromDate);
            String qry = "SELECT * FROM chargebee_webhooks_status WHERE event_process ='Initialize Event' AND event_status ='Initialize Event'"
                             + " AND  updatedon >= '" + fromdate + "'";

            List<ChargebeeWebhooksStatus> listCBWH = this.sessionFactory.getCurrentSession().createSQLQuery(qry)
                                                         .addEntity(ChargebeeWebhooksStatus.class).list();

            return listCBWH;
        } catch (Exception e) {
            log.error("Check  getWebHookslist : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public boolean saveOrUpdateUnpaidInvoice(UnpaidInvoices invoice) {
        log.info("Entered into saveOrUpdateUnpaidInvoice :: ");
        try {
            sessionFactory.getCurrentSession().merge(invoice);
            return true;
        } catch (Exception e) {
            log.error("Error in saveOrUpdateUnpaidInvoice : " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public UnpaidInvoices checkUnpaidInvoice(String sub_id, String chargebeeId) {
        log.info("Entered into checkUnpaidInvoice :: sub id : " + sub_id + " : CB id : " + chargebeeId);
        UnpaidInvoices upInvoice = new UnpaidInvoices();
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(UnpaidInvoices.class);
            Criterion filter1 = Restrictions.eq("sub_id", sub_id);
            Criterion filter2 = Restrictions.eq("chargebeeId", chargebeeId);
            cr.add(Restrictions.and(filter1, filter2));
            List list = cr.list();

            if (!list.isEmpty()) {
                upInvoice = (UnpaidInvoices) list.get(0);
                log.info("upInvoice : " + upInvoice.getInvoice_id());
                return upInvoice;
            }

        } catch (Exception e) {
            log.error("Error in checkUnpaidInvoice " + e.getLocalizedMessage());
        }
        return null;
    }

    @Override
    public UnpaidInvoices isUnpaidInvoice(String chargebeeId) {
        log.info("Entered into isUnpaidInvoice :: CB id : " + chargebeeId);
        UnpaidInvoices upInvoice = new UnpaidInvoices();
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(UnpaidInvoices.class);
            Criterion filter1 = Restrictions.eq("invoiceStatus", "not_paid");
            Criterion filter2 = Restrictions.eq("chargebeeId", chargebeeId);
            cr.add(Restrictions.and(filter1, filter2));
            List list = cr.list();
            if (!list.isEmpty()) {
                upInvoice = (UnpaidInvoices) list.get(0);
                log.info(upInvoice.getInvoice_id());
                return upInvoice;
            }
        } catch (Exception e) {
            log.error("Error in isUnpaidInvoice " + e.getLocalizedMessage());
        }
        return null;
    }

    @Override
    public boolean saveRemoveAddon(String cus_id, String subId, String addon_id) {
        log.info("Entered saveRemoveAddon...");
        String qry = "insert into removed_addons(chargebee_id,subscription_id,addon_id) values('" + cus_id + "','"
                         + subId + "','" + addon_id + "');";
        log.info("Qry : " + qry);
        try {
            Session session = sessionFactory.getCurrentSession();
            int updated = session.createSQLQuery(qry).executeUpdate();
            log.info("Insert status :" + updated);
        } catch (PersistenceException e) {
            if (e.getCause().toString().contains("ConstraintViolationException")) {
                log.info("constraintviolationexception handled");
                qry = "update removed_addons set addon_id='" + addon_id + "' where chargebee_id='" + cus_id
                          + "' and subscription_id='" + subId + "'";
                log.info("Qry : " + qry);
                Session session = sessionFactory.getCurrentSession();
                int updated = session.createSQLQuery(qry).executeUpdate();
                log.info("Update status :" + updated);
            }
        } catch (Exception e) {
            log.error("Error occurred : " + e.getMessage());
        }
        return false;
    }

    @Override
    public int updateAllCBSubscription(String curr_code, String ex_rate, String subs_id, String state_code) {
        try {
            String qry = "UPDATE all_chargebee_subscription SET curr_code='" + curr_code + "' , exchange_rate='"
                             + ex_rate + "',state_code='" + state_code + "' WHERE subscription_id='" + subs_id + "'";
            int stat = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
            log.info("updateAllCBSubscription :" + stat);
            return stat;
        } catch (Exception e) {
            log.error("Error in updateAllCBSubscription: " + e.getLocalizedMessage());
            return 0;
        }

    }

    @Override
    public boolean deleteAdditionalBenefits(String chargebeeid, String periodname) {
        try {
            String qry = "DELETE FROM `additional_benefits_credit` WHERE  user_id IN (SELECT id FROM `user` WHERE chargebeeid='"
                             + chargebeeid + "') AND planid != (" + "SELECT id FROM `benefits_plan_details` WHERE plan_name='"
                             + periodname + "')";
            int stat = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
            log.info("deleteAdditionalBenefits :" + stat);

            if (stat > 0)
                return true;
            else
                return false;
        } catch (Exception e) {
            log.error("Error in deleteAdditionalBenefits: " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public boolean updateSubStatusInUserRetained(long user_id) {
        log.info("Entered into updateSubStatusInUserRetained :: user_id : " + user_id);
        try {

            String qry = "UPDATE `user_retained` SET `activated` = '1' WHERE `user_id` = '" + user_id + "'; ";
            int status = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

            if (status > 0)
                return true;
            else
                return false;

        } catch (Exception e) {
            log.error("Error in updateSubStatusInUserRetained :: Error : " + e.getLocalizedMessage());
        }
        return false;
    }

    @Override
    public long getMonitorTypeByPlanId(String planId) {
        log.info(" Entered in getMonitorTypeByPlanId :: DAO ");

        long monitorType = 1;
        try {
            Session session = sessionFactory.getCurrentSession();

            String qry = "SELECT monitor_type FROM plan_to_period pp JOIN plan p ON p.id = pp.plan_id WHERE pp.chargebee_planid ='"
                             + planId + "'";
            SQLQuery query = session.createSQLQuery(qry);

            if (query.list().size() > 0) {
                monitorType = ((BigInteger) query.list().get(0)).longValue();
            }
            return monitorType;
        } catch (Exception e) {
            log.error("Error in getMonitorTypeByPlanId: " + e.getLocalizedMessage());
            return monitorType;
        }

	}

    @Override
    public AllProductSubscription checkProductSubscriptionStatus(String subscriptionId) {
        log.info("Enter in checkProductSubscriptionStatus :: DAO ");
        AllProductSubscription sub = null;
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(AllProductSubscription.class);
            // cr.setProjection(Projections.property("id"));
            cr.add(Restrictions.eq("subscriptionId", subscriptionId));
            List list = cr.list();
            if (list.isEmpty()) {
                sub = null;
            } else {
                sub = (AllProductSubscription) list.get(0);
            }
        } catch (Exception e) {
            log.error("Error in checkProductSubscriptionStatus :" + e.getLocalizedMessage());
        }

        return sub;
    }

    @Override
    public String insertAllProductSubscription(AllProductSubscription subscription) {
        log.info(" Entered in insertAllProductSubscription :: DAO ");
        try {
        	if(subscription.getSubscriptionStatus().equalsIgnoreCase("cancelled")) // to update mrr to 0 for cancelled
        		subscription.setMrr(0);
        	
            String curTime = new Helper().getCurrentTimeinUTC();
            subscription.setUpdatedIndb(curTime);
            this.sessionFactory.getCurrentSession().merge(subscription);
            return "1";
        } catch (Exception e) {
            log.error(" Error in insertAllSubscription : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public String insertAllProductSubscriptionSQL(AllProductSubscription subscription) {
        log.info(" Entered in insertAllProductSubscriptionSQL :: subscription_id : " + subscription.getSubscriptionId() + " :: gateway_id : " + subscription.getGateway_id());
        try {
            Session session = sessionFactory.openSession();
            try {
                String curTime = new Helper().getCurrentTimeinUTC();
                subscription.setUpdatedIndb(curTime);

                Transaction transaction = session.beginTransaction();
                String qry = "insert ignore into `all_product_subscription` " +
                                 "(`subscription_id`, `plan_id`, `subscription_status`, `subscription_created_at`, `subscription_started_at`, " +
                                 "`subscription_activated_at`, `subscription_cancelled_at`, `subscription_next_billing_at`, `plan_amount`, " +
                                 "`chargebee_id`, `billing_email`, `addons`, `enable`, `trial_start`, `trial_end`, `updated_indb`, `metadata`, " +
                                 "`is_deleted`, `updated_date`, `plan_period`, `event_type`, `curr_code`, `exchange_rate`, `state_code`, `monitor_type`," +
                                 " `gateway_id`, `is_test`, `mrr`, `resume_date`, `duplicate_subs`) " +
                                 "values('" + subscription.getSubscriptionId() + "','" + subscription.getPlanId() + "','" + subscription.getSubscriptionStatus() + "','" + subscription.getSubscriptionCreatedAt() + "','" + subscription.getSubscriptionStartedAt() + "'," +
                                 "'" + subscription.getSubscriptionActivatedAt() + "','" + subscription.getSubscriptionCancelledAt() + "','" + subscription.getNextBillingAt() + "','" + subscription.getPlanAmount() + "'," +
                                 "'" + subscription.getCustomerId() + "','" + subscription.getBillingEmail() + "','" + subscription.getAddons() + "','" + subscription.getEnable() + "','" + subscription.getTrialStart() + "','" + subscription.getTrialEnd() + "','" + _helper.getCurrentTimeinUTC() + "','" + subscription.getMetaData() + "'," +
                                 "'" + subscription.getIsDeleted() + "','" + subscription.getUpdatedDate() + "','" + subscription.getPlanPeriod() + "','" + subscription.getEvent_type() + "','" + subscription.getCurrency_code() + "','" + subscription.getExchange_rate() + "','" + subscription.getState_code() + "','" + subscription.getMonitor_type() + "'," +
                                 "'" + subscription.getGateway_id() + "','0','" + subscription.getMrr() + "','" + subscription.getResume_date() + "','0');";
                log.info("qry : " + qry);
                session.save(subscription);
//				int status = session.createSQLQuery(qry).executeUpdate();
                transaction.commit();
                session.flush();
                return "1";
            } finally {
                session.close();
            }

        } catch (Exception e) {
            log.error(" Error in insertAllProductSubscriptionSQL : " + e.getLocalizedMessage());
            return "0";
        }
    }

    @Override
    public boolean updateMRRInAllCB(String subId, long mrr, boolean isAllCBSub) {
        log.info("Entered updateMRRInAllCB :: DAO : isAllCBSub : " + isAllCBSub + " - Sub ID - " + subId);

        if (subId == null || subId.trim().isEmpty()) {
            log.error("Subscription ID is null or empty.");
            return false;
        }

        Transaction txn = null;
        try (Session session = sessionFactory.openSession()) {
            txn = session.beginTransaction();

            String qry = isAllCBSub
                             ? "UPDATE all_chargebee_subscription SET mrr = :mrr WHERE is_migrated = 0 AND subscription_id = :subId"
                             : "UPDATE all_product_subscription SET mrr = :mrr WHERE subscription_id = :subId";

            log.info("Update MRR Query : " + qry);
            int rowsAffected = session.createSQLQuery(qry)
                                   .setParameter("mrr", mrr)
                                   .setParameter("subId", subId.trim())
                                   .executeUpdate();

            txn.commit();
            log.info("MRR update successful: Mrr=" + mrr + ", SubId=" + subId + ", RowsAffected=" + rowsAffected);
            return rowsAffected > 0;

        } catch (Exception e) {
            log.error("Error in updateMRRInAllCB: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
	public boolean updateProductSubscriptionStatus(String subId, String subsStatus) {
		log.info(" Entered into updateProductSubscriptionStatus :: DAO ");
		try {
			String subQry ="";
			if(subsStatus.equalsIgnoreCase("cancelled")) // to update mrr for combo plan
				subQry =" , mrr=0 ";
			
			String qry = "UPDATE `all_product_subscription` SET `subscription_status` = '"+subsStatus+"' "+subQry+"  WHERE `subscription_id` = '"+subId+"'; ";
			int update = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("update status : " + update);
			return true;
		} catch (Exception e) {
			log.error(" Error in updateProductSubscriptionStatus :: Error : " + e.getLocalizedMessage());
		}
		return false;
	}
	
 	@Override
    public String getSubExistIn(String subId) {
        log.info("Entered getSubExistIn for Sub ID : " + subId);
        String existIn = "NA";
        String query = "SELECT 'true' AS all_cb_sub, 'false' AS all_pd_sub FROM all_chargebee_subscription WHERE subscription_id = '" + subId + "' AND is_migrated = 0 GROUP BY subscription_id UNION SELECT 'false' AS all_cb_sub, 'true' AS all_pd_sub FROM all_product_subscription WHERE subscription_id = '" + subId + "' GROUP BY subscription_id; ";

        log.info("getSubExistIn Query : " + query);
        try {
            Session session = sessionFactory.getCurrentSession();
            List<Object[]> list = (List<Object[]>)session.createSQLQuery(query).list();
            if (!list.isEmpty()) {

                for (Object[] obj : list) {
                    if (obj[0] != null && obj[0].toString().equalsIgnoreCase("true"))
                        existIn = "allCBSub";
                    else if (obj[1] != null && obj[1].toString().equalsIgnoreCase("true"))
                        existIn = "allProdSub";
                    else
                        existIn = "none";
                }
                log.info("existIn : " + existIn);

            }else {
            	log.info("getSubExist res empty : ");
            }
        } catch (Exception e) {
            log.error("Exception in getSubExistIn : " + e.getLocalizedMessage());
        }
        return existIn;
    }

    @Override
    public boolean updateSubStatusinAllProductSub(String subscriptionId, boolean isAllCbSub) {
        log.info(" Entered in updateSubStatusinAllProductSub :: DAO ");
        try {
            String curTime = new Helper().getCurrentTimeinUTC();
            String updateQry = "update all_product_subscription set subscription_status = 'cancelled', duplicate_subs = '1', updated_indb = '" + curTime + "' where subscription_id = '" + subscriptionId + "';";

            if(isAllCbSub)
                updateQry = "update all_chargebee_subscription set subscription_status = 'cancelled', duplicate_subs = '1', updated_indb = '" + curTime + "' where subscription_id = '" + subscriptionId + "';";

            int status = this.sessionFactory.getCurrentSession().createSQLQuery(updateQry).executeUpdate();

            if (status > 0)
                return true;

        } catch (Exception e) {
            log.error("Error in updateSubStatusinAllProductSub : " + e.getLocalizedMessage());
        }
        return false;
    }

    @Override
    public PlantoPeriod getPlanAndPeriodId(String planName) {
        log.info(" Entered in getPlanAndPeriodId :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            Criteria cr = session.createCriteria(PlantoPeriod.class);
            cr.add(Restrictions.eq("chargebee_planid", planName));
            List<PlantoPeriod> list = (List<PlantoPeriod>) cr.list();

            if (list.isEmpty()) {
                return null;
            }
            return list.get(0);
        } catch (Exception e) {
            log.error("Error in getPlanAndPeriodId : " + e.getLocalizedMessage());
            return null;
        }
    }

    @Override
    public long getUserByCbId(String cbId) {
        log.info(" Entered in getUserByCbId :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT id FROM `user` WHERE chargebeeid='" + cbId + "';";
            List<BigInteger> list = (List<BigInteger>) session.createSQLQuery(qry).list();

            if (list.isEmpty()) {
                return 0;
            }
            return list.get(0).longValue();
        } catch (Exception e) {
            log.error("Error in getUserByCbId : " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public long getHostedPage(long userid, long monitortype_id) {
        log.info(" Entered in getHostedPage :: userid : " + userid + " monitortype_id : " + monitortype_id);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT gatewayid FROM temp_sub_purchase WHERE userid=" + userid + " AND monitortype_id="
                             + monitortype_id + " ORDER BY updated_date DESC;";
            List<BigInteger> list = (List<BigInteger>) session.createSQLQuery(qry).list();
            if (list.isEmpty()) {
                return 0;
            }
            return list.get(0).longValue();
        } catch (Exception e) {
            log.error("Error in getHostedPage: " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public void deleteHostedPageId(long userid, long monitortype_id) {
        log.info(" Entered deleteHostedPageId :: DAO ");
        try {
            String qry = "DELETE FROM temp_sub_purchase WHERE userid=" + userid + " AND monitortype_id="
                             + monitortype_id;
            int deleted = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
            log.info("Deleted : " + deleted);
        } catch (Exception e) {
            log.error(" Error in deleteHostedPageId : " + e.getLocalizedMessage());
        }
    }

    @Override
    public PlantoPeriod getPlanToPeriodByMonitorId(long monitortype_id) {
        log.info(" Entered in getPlanToPeriodByMonitorId :: monitortype_id : " + monitortype_id);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT PP.plan_id,PP.sub_period_id,PP.chargebee_planid FROM plan_to_period PP "
                             + "JOIN plan PL ON PL.id= PP.plan_id " + "WHERE PL.monitor_type=:monitorid AND PL.is_freeplan=1;";
            Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
            query.setParameter("monitorid", monitortype_id);

            PlantoPeriod planToPeriod = null;
            List<Object[]> list = query.list();
            if (list.isEmpty()) {
                return new PlantoPeriod();
            }

            Object[] obj = list.get(0);
            planToPeriod = new PlantoPeriod(((BigInteger) obj[0]).longValue(), ((BigInteger) obj[1]).longValue(),
                (String) obj[2]);
            return planToPeriod;
        } catch (Exception e) {
            log.error("Error in getHostedPage: " + e.getLocalizedMessage());
            return new PlantoPeriod();
        }
    }

    @Override
    public boolean deleteGatewayFeature(long gateway_id) {
        log.info(" Entered deleteGatewayFeature :: gateway_id : " + gateway_id);
        try {
            int updated = sessionFactory.getCurrentSession()
                              .createSQLQuery("DELETE FROM gateway_feature where gateway_id=" + gateway_id).executeUpdate();
            if (updated > 0)
                return true;
        } catch (Exception e) {
            log.error("Error in deleteGatewayFeature : " + e.getLocalizedMessage());
        }
        return false;
    }

    @Override
    public boolean updateMRR(String subId, long mrr) {
        log.info("Entered updateMRR :: DAO");

        if (subId == null || subId.trim().isEmpty()) {
            log.error("Subscription ID is null or empty.");
            return false;
        }

        Session session = sessionFactory.openSession();

        try {
            Transaction tx1 = session.beginTransaction();
            String qry1 = "UPDATE all_chargebee_subscription SET mrr = :mrr "
                              + "WHERE is_migrated = 0 AND subscription_id = :subId ";

            int rowsAffected = session.createSQLQuery(qry1)
                                   .setParameter("mrr", mrr)
                                   .setParameter("subId", subId.trim())
                                   .executeUpdate();
            session.flush();
            tx1.commit();
            log.info("Executing fallback1 MRR update:" + qry1 + " :Mrr :" + mrr + ":" + subId + " : " + rowsAffected);

            if (rowsAffected == 0) {
                String qry2 = "UPDATE all_product_subscription SET mrr = :mrr "
                                  + "WHERE subscription_id = :subId";
                Transaction tx2 = session.beginTransaction(); // NEW transaction

                rowsAffected = session.createSQLQuery(qry2)
                                   .setParameter("mrr", mrr)
                                   .setParameter("subId", subId.trim())
                                   .executeUpdate();
                session.flush();
                tx2.commit();

                log.info("Executing fallback2 MRR update:" + qry2 + " : Mrr:" + mrr + ":" + subId + " : " + rowsAffected);
            }

            boolean success = rowsAffected > 0;
            log.info("DAO:updateMRR: " + mrr + ":" + success);

            return success;
        } catch (Exception e) {
            log.error("Error in updateMRR: " + e.getMessage(), e);
            return false;
        } finally {
            session.close();
        }
    }

    @Override
    public boolean insertOrUpdateCoupon(Coupon coupon) {
        log.info(" Entered insertOrUpdateCoupon");
        try {
            this.sessionFactory.getCurrentSession().saveOrUpdate(coupon);
            return true;
        } catch (Exception e) {
            log.error("Error in insertOrUpdateCoupon : " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public String getPlanVersionbyplanname(String planName) {
        log.info("Entered in getPlanVersionbyplanname :: planName: " + planName);
        String plan_ver = "NA";
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT PL.plan_ver FROM plan PL JOIN plan_to_period PP ON PL.id = PP.plan_id "
                             + "WHERE PP.chargebee_planid = :planName ;";

            Query query = session.createSQLQuery(qry);
            query.setParameter("planName", planName);

            List<String> list = query.list();
            if (list.isEmpty()) {
                return plan_ver;
            }

            return list.get(0);
        } catch (Exception e) {
            log.error("Error in getPlanVersionbyplanname", e);
            return plan_ver;
        }
    }

    @Override
    public boolean saveMigratedSubsStatus(String cus_id, String subId) {
        log.info(" Entered in saveMigratedSubsStatus :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "UPDATE `all_chargebee_subscription` set subscription_status = 'cancelled', is_migrated=1 where chargebee_id = '" + cus_id + "';";
            int i = session.createSQLQuery(qry).executeUpdate();

            return (i > 0) ? true : false;
        } catch (Exception e) {
            log.error("Error in saveMigratedSubsStatus: " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public boolean savePauseHistorysStatus(String subId) {
        log.info(" Entered in savePauseHistorysStatus :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT id FROM `subscription_pause_history` WHERE pause_status = 1 AND cb_sub_id =:subId ;";
            Query query = session.createSQLQuery(qry);
            query.setParameter("subId", subId);

            long historyId = 0;
            if (query.list().size() > 0 && !query.list().isEmpty()) {
                historyId = ((BigInteger) query.list().get(0)).longValue();

                if (historyId > 0) {
                    Session session1 = sessionFactory.getCurrentSession();
                    String qry1 = "UPDATE `subscription_pause_history` set pause_status= 0 where cb_sub_id = '" + subId + "';";
                    int i = session1.createSQLQuery(qry1).executeUpdate();

                    return (i > 0) ? true : false;
                }
            }
        } catch (Exception e) {
            log.error("Error in savePauseHistorysStatus: " + e.getLocalizedMessage());
            return false;
        }
        return false;
    }

    @Override
    public String getResumedatebySubId(String subId) {
        log.info(" Entered in getResumedatebySubId :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT resume_date FROM `subscription_pause_history` WHERE cb_sub_id =:subId ;";
            Query query = session.createSQLQuery(qry);
            query.setParameter("subId", subId);

            if (query.list().size() > 0 && !query.list().isEmpty()) {

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dt = dateFormat.format((Date) query.list().get(0));
                return dt;

            }
        } catch (Exception e) {
            log.error("Error in getResumedatebySubId: " + e.getLocalizedMessage());
            return null;
        }
        return null;
    }

    public boolean deleteUserFeature(long userId) {
        try {
            Session ses = sessionFactory.getCurrentSession();

            String updateQry = "DELETE FROM user_feature WHERE user_id = :userId";

            Query query = ses.createSQLQuery(updateQry);
            query.setParameter("userId", userId);

            int deleteCount = query.executeUpdate();
            log.info("Rows deleted: " + deleteCount);

        } catch (Exception e) {
            log.error("Exception in deleteUserFeature: " + e.getMessage(), e);
            log.error("deleteUserFeature details not found for userId: " + userId);
            return false;
        }

        return true;
    }

    @Override
    public void deleteFlexiPlanHistory(long gatewayId, String subId) {
        log.info("deleteFlexiPlanHistory : " + gatewayId);
        try {
            Session ses = sessionFactory.getCurrentSession();

            String updateQry = "DELETE FROM flexi_plan_history WHERE subscription_id = :subid ";
            if (gatewayId != 0) {
                updateQry += "AND gateway_id= :gatewayid ";
            }

            Query query = ses.createSQLQuery(updateQry);
            query.setParameter("subid", subId);
            if (gatewayId != 0) {
                query.setParameter("gatewayid", gatewayId);
            }

            int deleteCount = query.executeUpdate();
            log.info("Rows deleted: " + deleteCount);

        } catch (Exception e) {
            log.error("Exception in delete flexiplanhistory : " + e.getMessage(), e);
        }
    }

    @Override
    public boolean saveCancelledSubscriptionHistory(CBCancelHistory cancelHistory) {

        log.info("saveCancelledSubscriptionHistory");
        try {
            Session session = sessionFactory.getCurrentSession();

            session.save(cancelHistory);

            return true;
        } catch (Exception e) {
            log.error("Exception in save Cancelled Subscription History : {}", e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public long getHostedPage(long userid) {
        log.info(" Entered in getHostedPage :: userid : " + userid);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT gatewayid FROM temp_sub_purchase WHERE userid=" + userid + " " +
                             "ORDER BY updated_date DESC;";
            List<BigInteger> list = (List<BigInteger>) session.createSQLQuery(qry).list();
            if (list.isEmpty()) {
                return 0;
            }
            return list.get(0).longValue();
        } catch (Exception e) {
            log.error("Error in getHostedPage: " + e.getLocalizedMessage());
            return 0;
        }
    }

    @Override
    public String getPlanType(String cbPlanid) {
        log.info("Entered getMonitorType :" + cbPlanid);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT P.`plan_type` FROM plan P JOIN plan_to_period PP ON P.id=PP.plan_id "
                             + " WHERE PP.chargebee_planid=:cbPlanid";
            Query query = session.createSQLQuery(qry);
            query.setParameter("cbPlanid", cbPlanid);

            List<String> planType = (List<String>) query.list();
            if (planType.size() == 1) {
                return (String) (planType.get(0));
            }
        } catch (Exception e) {
            log.error("Error in getMonitorType: " + e.getLocalizedMessage());
        }
        return "NA";
    }

    @Override
    public boolean updateComboPlanStatus(AllProductSubscription subs) {
        log.info(" Entered in saveMigratedSubsStatus :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            String curTime = new Helper().getCurrentTimeinUTC();
            subs.setUpdatedIndb(curTime);
            String qry = "UPDATE all_product_subscription SET subscription_status='" + subs.getSubscriptionStatus() + "',"
                             + "subscription_cancelled_at='" + subs.getSubscriptionCancelledAt() + "',subscription_started_at='" + subs.getSubscriptionStartedAt() + "',"
                             + "subscription_activated_at='" + subs.getSubscriptionActivatedAt() + "',subscription_next_billing_at='" + subs.getNextBillingAt() + "',"
                             + "updated_indb='" + subs.getUpdatedIndb() + "', updated_date='" + subs.getUpdatedDate() + "' WHERE subscription_id='" + subs.getSubscriptionId() + "'";
            int i = session.createSQLQuery(qry).executeUpdate();

            return (i > 0) ? true : false;
        } catch (Exception e) {
            log.error("Error in saveMigratedSubsStatus: " + e.getLocalizedMessage());
            return false;
        }
    }

    @Override
    public PlanToMonitorType getPlanToMonitorType(long planId) {
        log.info("Enterd into getPlanToMonitorType :: plan_id : " + planId);
        try {
            Session session = sessionFactory.getCurrentSession();
            CriteriaBuilder builder = session.getCriteriaBuilder();
            CriteriaQuery<PlanToMonitorType> query = builder.createQuery(PlanToMonitorType.class);
            Root<PlanToMonitorType> root = query.from(PlanToMonitorType.class);
            query.select(root).where(builder.equal(root.get("plan_id"), planId));
            List<PlanToMonitorType> planToMonitorTypeList = session.createQuery(query).list();

            if (planToMonitorTypeList.isEmpty()) {
                log.info("plan_to_monitortype not found for plan_id : " + planId);
                return null;
            }
            return planToMonitorTypeList.get(0);
        } catch (Exception e) {
            log.error("Error in getPlanToMonitorType :: Error : " + e.getLocalizedMessage());
        }
        return null;
    }

    @Override
    public AllProductSubscription getAllProductSubscriptionBySubId(String subscriptionId) {
        log.info("Enterd into getAllProductSubscriptionBySubId :: subscription_id : " + subscriptionId);
        try {
            Session session = sessionFactory.getCurrentSession();
            CriteriaBuilder builder = session.getCriteriaBuilder();
            CriteriaQuery<AllProductSubscription> query = builder.createQuery(AllProductSubscription.class);
            Root<AllProductSubscription> root = query.from(AllProductSubscription.class);
            query.select(root).where(builder.equal(root.get("subscriptionId"), subscriptionId));
            List<AllProductSubscription> allSubscriptionList = session.createQuery(query).list();

            if (allSubscriptionList.isEmpty()) {
                log.info("all_product_subscription not found for subscription_id : " + subscriptionId);
                return null;
            }
            return allSubscriptionList.get(0);
        } catch (Exception e) {
            log.error("Error in getAllProductSubscriptionBySubId :: Error : " + e.getLocalizedMessage());
        }
        return null;
    }

	@Override
	public boolean deleteGatewayFeatureBySubId(String sub_id) {

		log.info(" Entered in deleteGatewayFeatureBySubId :: sub_id " + sub_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "UPDATE `gateway_feature` set enable=0 where sub_id = '"+sub_id+"';";
			int i = session.createSQLQuery(qry).executeUpdate();
			
			return (i > 0) ? true : false;
		} catch (Exception e) {
			log.error("Error in deleteGatewayFeatureBySubId: " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean updateComboPlanMonitorType(AllProductSubscription subs) {

		log.info(" Entered in updateComboPlanMonitorType :: sub_id " + subs);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "UPDATE `all_product_subscription` set monitor_type=11 where subscription_id = '"+subs.getSubscriptionId()+"';";
			int i = session.createSQLQuery(qry).executeUpdate();
			
			return (i > 0) ? true : false;
		} catch (Exception e) {
			log.error("Error in updateComboPlanMonitorType: " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public List<AllSubscription> getSubscriptionByChargebeeId(String chargebeeid) {
		log.info(" Entered into getSubscriptionByChargebeeId :: chargebeeId : " + chargebeeid);
		try {

			Session session = sessionFactory.getCurrentSession();

			Criteria criteria = session.createCriteria(AllSubscription.class);

			Criterion chargebeeId = Restrictions.eq("customerId", chargebeeid);
			Criterion statusActive = Restrictions.ilike("subscriptionStatus", "active");
			Criterion statusNonRenewing = Restrictions.ilike("subscriptionStatus", "non_renewing");
			Criterion statusInTrial = Restrictions.ilike("subscriptionStatus", "in_trial");
			Criterion statusPaused = Restrictions.ilike("subscriptionStatus", "paused");
			Criterion statusDeleted = Restrictions.eq("isDeleted", 0);

			LogicalExpression status = Restrictions.or(statusNonRenewing, statusInTrial);
			status = Restrictions.or(statusActive, status);
			
			LogicalExpression newStatus = Restrictions.or(status, statusPaused);

			LogicalExpression id = Restrictions.and(chargebeeId, newStatus);
			
			LogicalExpression dbid = Restrictions.and(id, statusDeleted);

			criteria.addOrder(Order.desc("updatedDate"));
			criteria.add(dbid);

			List<AllSubscription> allSubscription = (List<AllSubscription>) criteria.list();

			if (allSubscription.isEmpty()) {
				return null;
			}

			return allSubscription;

		} catch (Exception e) {
			log.error(" Error in getSubscriptionByChargebeeId : " + e.getLocalizedMessage());
			return null;
		}
	}
	
	@Override
	public long findPetmonitorincomboPlan(String subId) {
        log.info("Entered findPetmonitorincomboPlan :" + subId);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT AP.gateway_id FROM all_product_subscription AP WHERE AP.subscription_id =:subId AND AP.monitor_type = 1;";
            Query query = session.createSQLQuery(qry);
            query.setParameter("subId", subId);

            List<BigInteger> planType = (List<BigInteger>) query.list();
            if (planType.size() == 1) {
                return (long) (planType.get(0).longValue());
            }
        } catch (Exception e) {
            log.error("Error in findPetmonitorincomboPlan: " + e.getLocalizedMessage());
        }
        return 0;
    }
	
	@Override
	public boolean deleteVetChatSub(long mtype, String subid) {
		try {
			String qry = " DELETE FROM `all_product_subscription` a WHERE subscription_id ='"+subid+"'  AND monitor_type ="+mtype+";";
			int resultVal = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			return (resultVal == 1) ? true : false;
		} catch (Exception e) {
			log.error("Error in deleteVetChatSub: "+e.getLocalizedMessage());
			return false;
		}	
	}
	
	@Override
	public boolean getduplicatewithactivesub(String subId, String chargbeeId, long gatewayId) {
		 log.info("Entered getduplicatewithactivesub :" + subId);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = " SELECT id FROM all_product_subscription WHERE duplicate_subs = 0 AND subscription_status IN ('active', 'non_renewing','in_trial') AND subscription_id !=:subId AND chargebee_id =:chargebeeId AND gateway_id =:gatewayId ;";
			Query query = session.createSQLQuery(qry);
	        query.setParameter("subId", subId);
	        query.setParameter("chargebeeId", chargbeeId);
	        query.setParameter("gatewayId", gatewayId);

	        List<BigInteger> planType = (List<BigInteger>) query.list();
	        if (planType.size() == 1) {
	             return true;
	        }
		} catch (Exception e) {
			log.error("Error in deleteVetChatSub: "+e.getLocalizedMessage());
			return false;
		}
		return false;	
		
	}
	
	@Override
	public boolean getNonduplicateAllproductSubscription(String subId, String chargbeeId,long gatewayId,String email) {
        log.info(" Entered in getUserDetails :: DAO ");
        try {
            Session session = sessionFactory.getCurrentSession();
            CbActivateCancelSubStatus cSubs = new CbActivateCancelSubStatus();

            String qry = "SELECT subscription_id,plan_id FROM all_product_subscription WHERE duplicate_subs = 0 AND subscription_status IN ('active', 'non_renewing','in_trial') AND subscription_id !=:subId AND chargebee_id =:chargebeeId AND gateway_id =:gatewayId";
            Query query = session.createSQLQuery(qry);
	        query.setParameter("subId", subId);
	        query.setParameter("chargebeeId", chargbeeId);
	        query.setParameter("gatewayId", gatewayId);
	        if(query.list().size() > 0) {
	        	Object[] obj = (Object[]) query.list().get(0);
	        	String subsId = (String) obj[0];
	        	String planId = (String) obj[1];
	        	String msurl = microservice_url + "/v4.0/gatewayfeaturewithmonitor?" + "cbplan=" + planId
				+ "&chargbeeid=" + chargbeeId
				+ "&eventtype=subscription_created&isupgrade=false&cbemail=" + email + "&gatewayid="
				+ gatewayId + "&subId=" + subsId;
	        	
	        	String msPlanRes = _helper.httpPOSTRequest(msurl, null);
				log.info("Plan Url update : " + msPlanRes);
	        }
        } catch (Exception e) {
            log.error("Error in getUserDetails: " + e.getLocalizedMessage());
            return false;
        }
        return true;
    }
	
	public boolean saveSubsStatusFromcombomigration(String subId, long gateway_id) {
        log.info(" Entered in saveSubsStatusFromcombomigration :: DAO ");
        try {
            String curTime = new Helper().getCurrentTimeinUTC();
            String updateQry = "update all_product_subscription set subscription_status = 'cancelled', duplicate_subs = '1', updated_indb = '" + curTime + "' where subscription_id = '" + subId + "' and gateway_id != "+gateway_id+";";

            int status = this.sessionFactory.getCurrentSession().createSQLQuery(updateQry).executeUpdate();

            if (status > 0)
                return true;

        } catch (Exception e) {
            log.error("Error in saveSubsStatusFromcombomigration : " + e.getLocalizedMessage());
        }
        return false;
    }
}
