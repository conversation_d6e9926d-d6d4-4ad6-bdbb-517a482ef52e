package com.nimble.webhooks.dao.impl;

import java.util.ArrayList;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IErrorDao;
import com.nimble.webhooks.entity.ErrorInfo;

@Repository
public class ErrorDaoImpl implements IErrorDao {

	@Autowired
	private SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(ExternalConfigDaoImpl.class);

	@Override
	public ArrayList<ErrorInfo> getErrorInfo() {
		ArrayList<ErrorInfo> errorInfoList = new ArrayList<ErrorInfo>();
		try {
			Session session = sessionFactory.getCurrentSession();
			String sql = "SELECT * FROM `error_info` WHERE `is_resolved` = 0";
			SQLQuery query = session.createSQLQuery(sql).addEntity(ErrorInfo.class);
			errorInfoList = (ArrayList<ErrorInfo>) query.list();
			return errorInfoList;

		} catch (Exception e) {
			log.error("Error in getErrorInfo: " + e.getLocalizedMessage());
		}
		return errorInfoList;
	}

	@Override
	public ErrorInfo updateErrorInfo(ErrorInfo errorInfo) {
		try {
			Session session = sessionFactory.getCurrentSession();
			session.merge(errorInfo);
			return errorInfo;
		} catch (Exception e) {
			log.error("Error in updateErrorInfo: " + e.getMessage());
		}
		return null;
	}

	@Override
	public boolean AckErrorinfo(ErrorInfo errInfo) {
		return false;
	}
}
