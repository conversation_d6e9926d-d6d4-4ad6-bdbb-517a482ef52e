package com.nimble.webhooks.dao.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Disjunction;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IUserDao;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AdditionBenefitsCancelReward;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.UserCancelFeedBack;

@Repository
public class UserDaoImpl implements IUserDao {

	@Autowired
	SessionFactory sessionFactory;

	private static final Logger log = LogManager.getLogger(UserDaoImpl.class);

	@Override
	public UserV4 verifyAuthV4(String key, String value) {
		log.info("Entered in verifyAuthV4:: key : " + key + " :: value : " + value);
		UserV4 usr = null;
		try {

			Query qry = sessionFactory.getCurrentSession().createSQLQuery(
					"SELECT U.id, U.cmp_id,U.chargebeeid , U.email,U.authkey, U.firstname, U.lastname, U.zipcode, U.mobileno FROM user U  WHERE U." + key + "='" + value + "';");

			List res = qry.list();

			if (!res.isEmpty() && res.size() > 0) {

				Object[] tuple = (Object[]) res.get(0);

				usr = new UserV4();

				if (tuple[0] != null) {
					BigInteger userId = (BigInteger) tuple[0];
					usr.setId(userId.longValue());
				}
				if (tuple[1] != null) {
					BigInteger cmp_id = (BigInteger) tuple[1];
					usr.setCmpId(cmp_id.longValue());
				}
				if (tuple[2] != null)
					usr.setChargebeeid((String) tuple[2]);

				if (tuple[3] != null)
					usr.setEmail((String) tuple[3]);

				if (tuple[4] != null)
					usr.setAuthKey((String) tuple[4]);

				if (tuple[5] != null)
					usr.setFirstname((String) tuple[5]);

				if (tuple[6] != null)
					usr.setLastname((String) tuple[6]);

				if (tuple[7] != null)
					usr.setZipcode((String) tuple[7]);

				if (tuple[8] != null)
					usr.setMobileno((String) tuple[8]);

				return usr;

			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("Error in verifyAuthV4: " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public UserV4 getUserByUNameOrEmail(String cbEmail) {
		log.info("Entered getUserByUNameOrEmail : " + cbEmail);

		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria crit = session.createCriteria(UserV4.class);

			Criterion userCr = Restrictions.eq("email", cbEmail).ignoreCase();
			Criterion emailCr = Restrictions.eq("username", cbEmail).ignoreCase();

			Disjunction disjunction = Restrictions.disjunction();
			disjunction.add(userCr);
			disjunction.add(emailCr);

			crit.add(disjunction);

			UserV4 user = (UserV4) crit.list().get(0);

			return user;
		} catch (IndexOutOfBoundsException e) {
			log.error("getUserByUNameOrEmail : " + e.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public boolean updateProductSubscription(long user_id) {
		log.info("Entered into updateProductSubscription :: user_id : " + user_id);
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			String qry = "update  `product_subscription` set enable=0,status ='sub_activated',is_subscription_activated=1,updatedon='"
					+ sdf.format(new Date()) + "' WHERE  user_id='" + user_id + "' and enable = 1;";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("sub_created status updated : user_id : " + user_id);
				return true;
			}
		} catch (Exception e) {
			log.error("Error in updateProductSubscription : " + e.getLocalizedMessage());
		}
		return false;
	}
	
	@Override
	public int executeQuery(String qry) {
		log.info("Entered ::  UserDaoImplV4:: executeQuery:: ");
		try {
			Session create_ses = sessionFactory.getCurrentSession();
			SQLQuery createQuery = create_ses.createSQLQuery(qry);
			int Cstatus = createQuery.executeUpdate();
			log.info("Query :" + qry);
			log.info("executeStatus :" + Cstatus);
			return Cstatus;
		} catch (IndexOutOfBoundsException e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		} catch (Exception e) {
			log.error("Query : " + qry);
			log.error("Error in executeQuery :: Session Name : sessionFactory :: error : " + e.getLocalizedMessage());
			return 0;
		}

	}

	@Override
	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id) {
		log.info("Entered into getUserCancelFeedBackByUserId :: user_id : "+ user_id);
		List<UserCancelFeedBack> userCancelFeedBackList = new ArrayList<>();
		try {
			userCancelFeedBackList = sessionFactory.getCurrentSession().createCriteria(UserCancelFeedBack.class)
					.add( Restrictions.eq("user_id", user_id))
					.list();
			if( userCancelFeedBackList.isEmpty() ) { 
				log.info("userCancelFeedBackList is empty");
				return null;
			}
				
			return userCancelFeedBackList.get(0);
		} catch (Exception e) {
			log.error("Error in getUserCancelFeedBackByUserId :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean deteleUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack) {
		log.info("Entered into deteleUserCancelFeedBack :: user_id : "+ userCancelFeedBack.getUser_id());
		try {
			sessionFactory.getCurrentSession().delete(userCancelFeedBack);
			return true;
		} catch (Exception e) {
			log.error("Error in deteleUserCancelFeedBack :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id) {
		log.info("Entered into getAdditionBenefitsCancelReward :: user_id : "+ user_id);
		List<AdditionBenefitsCancelReward> additionBenefitsCancelReward = new ArrayList<>();
		try {
			additionBenefitsCancelReward = sessionFactory.getCurrentSession().createCriteria(AdditionBenefitsCancelReward.class)
					.add( Restrictions.eq("user_id", user_id))
					.list();
			if( additionBenefitsCancelReward.isEmpty() ) { 
				log.info("additionBenefitsCancelReward is empty");
				return null;
			}
			return additionBenefitsCancelReward.get(0);
		} catch (Exception e) {
			log.error("Error in getAdditionBenefitsCancelReward :: Error : "+ e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean deteleAdditionBenefitsCancelReward(AdditionBenefitsCancelReward additionBenefitsCancelReward) {
		log.info("Entered into deteleAdditionBenefitsCancelReward :: user_id : "+ additionBenefitsCancelReward.getUser_id());
		try {
			sessionFactory.getCurrentSession().delete(additionBenefitsCancelReward);
			return true;
		} catch (Exception e) {
			log.error("Error in deteleAdditionBenefitsCancelReward :: Error : "+ e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public long getUserByChargebeeId(String customerId) {
		log.info("Entered into getUserByChargebeeId :: customerId : "+ customerId);
		try {
			List<BigInteger> userList = sessionFactory.getCurrentSession().createSQLQuery("SELECT id FROM user WHERE "
					+ "chargebeeid = '"+customerId+"'").list();
			if (userList.isEmpty()) {
				return 0;
			}
			return userList.get(0).longValue();
		} catch (Exception e) {
			log.error("Error in getUserByChargebeeId :: Error : "+ e.getLocalizedMessage());
		}
		return 0;
	}
	
	@Override
	public String getQrcIdfromgatewayId(long gatewayid) {
		log.info("Entered into getOrderIdfromgatewayId :: gatewayid : " + gatewayid);
		try {

			String qrCode = (String) sessionFactory.getCurrentSession()
					.createSQLQuery("SELECT qrcode FROM iris.gateway WHERE id = :gatewayId")
					.setParameter("gatewayId", gatewayid).uniqueResult();

			if (qrCode != null) {
				return qrCode;
			}
			
		} catch (Exception e) {
			log.error("Error in getOrderIdfromgatewayId :: Error : " + e.getLocalizedMessage());
			return null;
		}
		return null;
	}
	
	@Override
	public String getOrderIdfromQrc(String qrc) {
		log.info("Entered into getOrderIdfromgatewayId :: gatewayid : " + qrc);
		try {

			Number orderId = (Number) sessionFactory.getCurrentSession()
					.createSQLQuery("SELECT order_id FROM niom.inventory WHERE qrc = :qrCode")
					.setParameter("qrCode", qrc).uniqueResult();

			if (orderId != null) {
				String ord = orderId.toString();
				return ord;
			}

		} catch (Exception e) {
			log.error("Error in getOrderIdfromgatewayId :: Error : " + e.getLocalizedMessage());
			return null;
		}
		return null;
	}
	
	@Override
	public boolean updateProductSubscriptionByOrderId(long user_id, String orderId) {
		log.info("Entered into updateProductSubscriptionByOrderId :: user_id : " + user_id + " orderId ::" +orderId);
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			String qry = "update  `product_subscription` set enable=0,status ='sub_activated',is_subscription_activated=1,updatedon='"
					+ sdf.format(new Date()) + "' WHERE  user_id='" + user_id + "' and enable = 1 and order_id = '"+orderId+"';";

			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("sub_created status updated : user_id : " + user_id);
				return true;
			}
		} catch (Exception e) {
			log.error("Error in updateProductSubscriptionByOrderId : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateDuplicateAllCBSubscription(AllSubscription prodSubs) {

		log.info("Entered into updateDuplicateAllCBSubscription cbid : " + prodSubs.getCustomerId());
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			/* for active */
			String qry = "UPDATE all_chargebee_subscription SET duplicate_subs=1 WHERE `subscription_status` NOT IN ('active', 'non_renewing','in_trial') AND "
					+ " chargebee_id ='"+prodSubs.getCustomerId()+"' AND subscription_id !='"+prodSubs.getSubscriptionId()+"' AND duplicate_subs=0 ;";

			if(prodSubs.getSubscriptionStatus().toLowerCase().contains("cancel")) {
				qry = "SELECT COUNT(id) FROM all_chargebee_subscription WHERE chargebee_id = '"+prodSubs.getCustomerId()+"'  AND duplicate_subs=0 "
						+ " AND subscription_status  IN ('active', 'non_renewing','in_trial')";
				String subQry ="";
				List<BigInteger> userList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
				
				if (userList.isEmpty()|| userList.get(0).intValue()==0) {
					subQry = " AND subscription_id != '"+prodSubs.getSubscriptionId()+"'";
				}
				
				qry = "UPDATE all_chargebee_subscription SET duplicate_subs = 1 WHERE chargebee_id = '"+prodSubs.getCustomerId()
						+ "'  AND duplicate_subs=0 AND subscription_id IN ("
						+ " SELECT sub_id FROM (SELECT subscription_id AS sub_id FROM all_chargebee_subscription WHERE chargebee_id = "
						+ " '"+prodSubs.getCustomerId()+"'" +	subQry	+ "  AND subscription_status NOT IN ('active', 'non_renewing','in_trial')"
						+ " AND duplicate_subs = 0 ) AS derived );";
			}
			//System.out.println(qry);
			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			
			this.sessionFactory.getCurrentSession().flush();
			
			if (stat > 0) {
				log.info("duplicate status updated gateway_id : " + prodSubs.getCustomerId());
				return true;
			}
		} catch (Exception e) {
			log.error("Error in updateDuplicateSubs : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean updateDuplicateAllProductSubscription(AllProductSubscription prodSubs) {

		log.info("Entered into updateDuplicateSubs gateway : " + prodSubs.getGateway_id());
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			/* for active */
			String qry = "UPDATE all_product_subscription SET duplicate_subs=1 WHERE `subscription_status` NOT IN ('active', 'non_renewing','in_trial') AND "
					+ " chargebee_id ='"+prodSubs.getCustomerId()+"' AND subscription_id !='"+prodSubs.getSubscriptionId()+"' AND duplicate_subs=0 "
					+ " AND gateway_id ="+prodSubs.getGateway_id()+" ;";

			if(prodSubs.getSubscriptionStatus().toLowerCase().contains("cancel")) {
				qry = "SELECT COUNT(id) FROM all_product_subscription WHERE chargebee_id = '"+prodSubs.getCustomerId()+"'  AND duplicate_subs=0 "
						+ " AND subscription_status  IN ('active', 'non_renewing','in_trial') AND gateway_id ="+prodSubs.getGateway_id()+"";
				String subQry ="";
				List<BigInteger> userList = sessionFactory.getCurrentSession().createSQLQuery(qry).list();
				
				if (!userList.isEmpty()) {
					if(userList.get(0).intValue()==0)
						subQry = " AND subscription_id != '"+prodSubs.getSubscriptionId()+"' ";
				}
				
				qry = "UPDATE all_product_subscription SET duplicate_subs = 1 WHERE chargebee_id = '"+prodSubs.getCustomerId()
						+ "' AND gateway_id = "+prodSubs.getGateway_id()+" AND duplicate_subs=0 AND subscription_id  IN ("
						+ " SELECT sub_id FROM (SELECT subscription_id AS sub_id FROM all_product_subscription WHERE chargebee_id = "
						+ " '"+prodSubs.getCustomerId()+"' AND gateway_id ="+prodSubs.getGateway_id()+subQry+ " AND subscription_status NOT IN ('active','non_renewing','in_trial')"
						+ " AND duplicate_subs = 0 ) AS derived );";
			}
			//System.out.println(qry);
			log.info(qry);
			int stat = this.sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();

			if (stat > 0) {
				log.info("duplicate status updated gateway_id : " + prodSubs.getGateway_id());
				return true;
			}
		} catch (Exception e) {
			log.error("Error in updateDuplicateSubs : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public String getDeviceToken(long userId) {

		log.info("Entered into getDeviceToken :: userId : {}", userId);
		String deviceToken = "NA";
		try {
			String qry = "SELECT token FROM usertoken WHERE userId = :userId";
			deviceToken = sessionFactory.getCurrentSession().createSQLQuery(qry).setParameter("userId", userId).getSingleResult().toString();

			return deviceToken;
		} catch (Exception e) {
			log.error("Error in getDeviceToken :: Error : {}", e.getLocalizedMessage());
		}

		return deviceToken;
	}
}
