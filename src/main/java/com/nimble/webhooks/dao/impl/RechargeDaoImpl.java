package com.nimble.webhooks.dao.impl;

import java.util.HashMap;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IRechargeDao;
import com.nimble.webhooks.dto.JKcalDetails;
import com.nimble.webhooks.entity.LatestRechargeSubscription;
import com.nimble.webhooks.entity.ReCBWebhookStatus;
import com.nimble.webhooks.entity.RechargeSubscription;
import com.nimble.webhooks.helper.Helper;


@Repository
public class RechargeDaoImpl implements IRechargeDao {

	private static final Logger log = LogManager.getLogger(RechargeDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Override
	public String getCBSubId(String re_subid) {
		String cbSubid = "NA";
		try {
			String qry = " SELECT cb_subid FROM `recharge_cb_webhook_status` WHERE re_subid ='"+re_subid
					+"'  ORDER BY id DESC LIMIT 1;";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (!res.isEmpty() && res.size() > 0) {
				cbSubid = (String)res.get(0);
				
			}
			return cbSubid;
		} catch (Exception e) {
			log.error("Error in getCBSubId : " + e.getLocalizedMessage());
		}
		return cbSubid;
	}

	@Override
	public boolean saveRechargeSubscription(RechargeSubscription reSub) {
		log.info(" Entered into saveRechargeSubscription ");
		try {
			sessionFactory.getCurrentSession().saveOrUpdate(reSub);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(" Error in saveRechargeSubscription : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean saveLatestRechargeSubscription(LatestRechargeSubscription reSub) {
		log.info(" Entered into saveRechargeSubscription ");
		try {
			sessionFactory.getCurrentSession().saveOrUpdate(reSub);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			log.error(" Error in saveLatestRechargeSubscription : " + e.getLocalizedMessage());
			return false;
		}
	}
	@Override
	public HashMap<String,String> getReSubscriptionPlans() {
		HashMap<String,String> planList = new HashMap<String,String>();
		try {
			String qry = "SELECT `chargebee_planid`,sp.period_name as period_name FROM `plan_to_period` PTP JOIN plan P ON PTP.plan_id = P.id"
					+ " JOIN sub_period sp on sp.id=PTP.sub_period_id WHERE is_recharge_plan=1 and P.custom=1 and `plan_ver` = 'V2';";

			Query query = sessionFactory.getCurrentSession().createSQLQuery(qry);
			List res = query.list();

			if (!res.isEmpty() && res.size() > 0) {
				int size = res.size();						
				
				for (int i = 0; i < size; i++) {
					Object[] cp = (Object[]) res.get(i);
					String planid = ((String) cp[0]);
					String period = ((String)cp[1]);
					planList.put(planid, period);
				}
			}
			return planList;
		} catch (Exception e) {
			log.error("getReSubscriptionPlans : " + e.getLocalizedMessage());
		}
		return planList;
	}
	
	@Override
	public int saveReCancelCustomer(long userid, String re_cust_id) {
		try {
			String curUTC = new Helper().getCurrentTimeinUTC();
			
			String qry = "insert into `recharge_cancel_customer` ( `user_id`, `re_cust_id`, `created_on`)" + 
					" values('"+userid+"','"+re_cust_id+"','"+curUTC+"');";
			int stat = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			
			log.info("save saveReCancelCustomer :" + stat);
			return stat;
		} catch (Exception e) {
			log.error("Error in saveReCancelCustomer: " + e.getLocalizedMessage());
			return 0;
		}

	}
	
	@Override
	public int updateReSubHistory(String re_sub_id) {
		try {
			String qry = "update recharge_latest_sub_history set enable=0,cb_sub_status='Sub_Cancelled' where  sub_id='"+re_sub_id+"';";
			int stat = sessionFactory.getCurrentSession().createSQLQuery(qry).executeUpdate();
			log.info("updateReSubHistory :" + stat);
			return stat;
		} catch (Exception e) {
			log.error("Error in updateReSubHistory: " + e.getLocalizedMessage());
			return 0;
		}

	}

	@Override
	public LatestRechargeSubscription getRechargeSubDetailsByCusID(String reCustomerId) {
		log.info("Entered into getRechargeSubDetailsByCusID :: recharge_customer_id : "+reCustomerId);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria cr = session.createCriteria(LatestRechargeSubscription.class);
//			cr.setProjection(Projections.property("id"));
			cr.add(Restrictions.eq("customer_id", reCustomerId));
			List list = cr.list();
			if (list.isEmpty()) {
				return null;
			} else {
				return (LatestRechargeSubscription) list.get(0);
			}
		} catch (Exception e) {
			log.error("Error in  getRechargeSubDetailsByCusID :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public LatestRechargeSubscription getRechargeSubDetailsBySubID(String subid,String reCustomerId) {
		log.info("Entered into getRechargeSubDetailsByCusID :: recharge_customer_id : "+reCustomerId);
		try {
			Session session = sessionFactory.getCurrentSession();
			Criteria cr = session.createCriteria(LatestRechargeSubscription.class);
//			cr.setProjection(Projections.property("id"));
			cr.add(Restrictions.eq("customer_id", reCustomerId));
			cr.add(Restrictions.eq("sub_id", subid));
			List list = cr.list();
			if (list.isEmpty()) {
				return null;
			} else {
				return (LatestRechargeSubscription) list.get(0);
			}
		} catch (Exception e) {
			log.error("Error in  getRechargeSubDetailsByCusID :: Error : "+e.getLocalizedMessage());
		}
		return null;
	}
	
	@Override
	public boolean saveRechargeCBSubStatus(ReCBWebhookStatus reSub) {
		log.info(" Entered into saveRechargeCBSubStatus ");
		String curUTC = new Helper().getCurrentTimeinUTC();
		String query = "insert into `recharge_cb_webhook_status` ( `cb_id`, `cb_subid`, `re_subid`, `event_status`, `created_at`)"
				+ " values('"+reSub.getCb_id()+"','"+reSub.getCb_subid()+"','"+reSub.getRe_subid()+"','"+reSub.getEvent_status()+"','"+curUTC+"'); ";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in saveRechargeCBSubStatus : " + e.getLocalizedMessage());
			return false;
		}
	}
	
	@Override
	public boolean saveSmartBowlUserDetails(JKcalDetails jdetails) {
		log.info(" Entered into saveSmartBowlUserDetails ");
		String query = "insert into `calories_details` ( email, name, pet_name,pet_category,weight,activity_level,body_condition,pet_criteria,kcal)"
				+ " values('"+jdetails.getEmail()+"','"+jdetails.getUserName()+"','"+jdetails.getName()+"','"+jdetails.getPet_category()+"','"+jdetails.getWeight()+"','"+jdetails.getActivity_level()+"','"+jdetails.getBody_condition()+"','"+jdetails.getPet_criteria()+"','"+jdetails.getKcal()+"'); ";
		try {
			int update = this.sessionFactory.getCurrentSession().createSQLQuery(query).executeUpdate();
			if (update >= 1) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			log.error("Error in saveSmartBowlUserDetails : " + e.getLocalizedMessage());
			return false;
		}
	}
}
