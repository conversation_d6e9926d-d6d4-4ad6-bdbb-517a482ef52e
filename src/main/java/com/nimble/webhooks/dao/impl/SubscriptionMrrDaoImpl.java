package com.nimble.webhooks.dao.impl;

import com.nimble.webhooks.dao.ISubscriptionMrrDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;

@Repository
public class SubscriptionMrrDaoImpl implements ISubscriptionMrrDao {

    private static final Logger log = LogManager.getLogger(SubscriptionMrrDaoImpl.class);

    @Autowired
    @Qualifier("sessionFactory")
    private SessionFactory sessionFactory;

    public boolean updateMRR(String subId, long mrr) {

        log.info("Entered updateMRR :: subId: {}, mrr: {}", subId, mrr);

        try {
            Session session = sessionFactory.getCurrentSession();

            String sourceQuery =
                    "SELECT CASE " +
                            "WHEN EXISTS (SELECT 1 FROM all_chargebee_subscription WHERE subscription_id = :subId AND is_migrated = 0) THEN 'cb' " +
                            "WHEN EXISTS (SELECT 1 FROM all_product_subscription WHERE subscription_id = :subId) THEN 'pd' " +
                            "ELSE 'none' END";

            String source = (String) session.createNativeQuery(sourceQuery)
                    .setParameter("subId", subId)
                    .getSingleResult();

            String updateQueryStr;
            if ("cb".equals(source)) {
                updateQueryStr = "UPDATE all_chargebee_subscription SET mrr = :mrr WHERE is_migrated = 0 AND subscription_id = :subId";
            } else if ("pd".equals(source)) {
                updateQueryStr = "UPDATE all_product_subscription SET mrr = :mrr WHERE subscription_id = :subId";
            } else {
                log.error("No matching subscription found for subId: {}", subId);
                return false;
            }

            Query updateQuery = session.createNativeQuery(updateQueryStr);
            updateQuery.setParameter("mrr", mrr);
            updateQuery.setParameter("subId", subId);
            return updateQuery.executeUpdate() > 0;

        } catch (Exception e) {
            log.error("Error in updateMRR: {}", e.getMessage(), e);
            return false;
        }
    }
}
