package com.nimble.webhooks.dao.impl;

import com.nimble.webhooks.dao.ISubscriptionMrrDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;

@Repository
public class SubscriptionMrrDaoImpl implements ISubscriptionMrrDao {

    private static final Logger log = LogManager.getLogger(SubscriptionMrrDaoImpl.class);

    @Autowired
    @Qualifier("sessionFactory")
    private SessionFactory sessionFactory;

    @Override
    public boolean updateMRR(String subId, long mrr) {

        log.info("Entered updateMRR :: subId: {}, mrr: {}", subId, mrr);

        try {
            Session session = sessionFactory.getCurrentSession();

            // Use SELECT FOR UPDATE to lock the row and prevent concurrent modifications
            String sourceQuery =
                    "SELECT CASE " +
                            "WHEN EXISTS (SELECT 1 FROM all_chargebee_subscription WHERE subscription_id = :subId AND is_migrated = 0 FOR UPDATE) THEN 'cb' " +
                            "WHEN EXISTS (SELECT 1 FROM all_product_subscription WHERE subscription_id = :subId FOR UPDATE) THEN 'pd' " +
                            "ELSE 'none' END";

            String source = (String) session.createNativeQuery(sourceQuery)
                    .setParameter("subId", subId)
                    .getSingleResult();

            String updateQueryStr;
            if ("cb".equals(source)) {
                updateQueryStr = "UPDATE all_chargebee_subscription SET mrr = :mrr, updated_on = NOW() WHERE is_migrated = 0 AND subscription_id = :subId";
            } else if ("pd".equals(source)) {
                updateQueryStr = "UPDATE all_product_subscription SET mrr = :mrr, updated_on = NOW() WHERE subscription_id = :subId";
            } else {
                log.error("No matching subscription found for subId: {}", subId);
                return false;
            }

            Query updateQuery = session.createNativeQuery(updateQueryStr);
            updateQuery.setParameter("mrr", mrr);
            updateQuery.setParameter("subId", subId);

            int rowsAffected = updateQuery.executeUpdate();
            boolean success = rowsAffected > 0;
            log.info("updateMRR result: subId={}, mrr={}, source={}, rowsAffected={}, success={}",
                    subId, mrr, source, rowsAffected, success);

            return success;
        } catch (Exception e) {
            log.error("updateMRR failed: subId={}, mrr={}, error={}", subId, mrr, e.getMessage(), e);
            return false;
        }
    }
}
