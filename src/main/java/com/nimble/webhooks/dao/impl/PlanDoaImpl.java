package com.nimble.webhooks.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IPlanDao;
import com.nimble.webhooks.entity.GatewayFeature;

@Repository
public class PlanDoaImpl implements IPlanDao {

    private static final Logger log = LogManager.getLogger(PlanDoaImpl.class);

    @Autowired
    private SessionFactory sessionFactory;

    @Override
    public ArrayList<GatewayFeature> getGatewayFeatureByGatewayIds(String gatewayIds) {
        log.info("Enterd into getGatewayFeatureByGatewayIds :: gatewayIds : "+gatewayIds);
        ArrayList<GatewayFeature> gatewayFeatures = new ArrayList<>();
        try {
            Session session = sessionFactory.getCurrentSession();
            CriteriaBuilder builder = session.getCriteriaBuilder();
            CriteriaQuery<GatewayFeature> query = builder.createQuery(GatewayFeature.class);
            Root<GatewayFeature> root = query.from(GatewayFeature.class);
            List<String> s = Arrays.asList( gatewayIds.split(",") );
            Predicate gatewayIdPredicate = root.get("gateway_id").in(s);
            Predicate statusPredicate = builder.equal(root.get("enable"), 1);
            query.select(root).where(builder.and(gatewayIdPredicate, statusPredicate))
                    .groupBy(root.get("gateway_id"));
            gatewayFeatures = (ArrayList<GatewayFeature>) session.createQuery(query).list();
        } catch (Exception e) {
            log.error("Error in getGatewayFeatureByGatewayIds :: Error : "+ e.getLocalizedMessage());
        }
        return gatewayFeatures;
    }

    @Override
    public boolean isVetChatPlan(String planId) {

        log.info("Enterd into isVetChatPlan :: planId : {}", planId);
        try {
            Session session = sessionFactory.getCurrentSession();
            String qry = "SELECT PM.monitortype_id FROM plan_to_monitortype PM JOIN plan_to_period PP ON PP.plan_id = PM.plan_id WHERE chargebee_planid = :planId";
            Query query = session.createSQLQuery(qry);
            query.setParameter("planId", planId);

            String planType = (String) query.uniqueResult();

            return planType.contains("11");
        } catch (Exception e) {
            log.error("Error in isVetChatPlan: {}", e.getLocalizedMessage());
        }
        return false;
    }
    
    @Override
	public String getPlanConfig(String cbPlan) {
		log.info("Entered getPlanConfig : "+cbPlan);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT `monitortype_id` FROM  `plan_to_monitortype` PM JOIN plan_to_period PP ON PP.plan_id=PM.plan_id \r\n"
					+ "WHERE PP.`chargebee_planid` =:cbPlan";
			Query query = session.createSQLQuery(qry);
			query.setParameter("cbPlan", cbPlan);

			List<?> gatewayList = (List<?>) query.list();
			if (!gatewayList.isEmpty()|| gatewayList.size() >0) {
				return gatewayList.get(0).toString();
			}
		} catch (Exception e) {
			log.error("Error in getPlanConfig: " + e.getLocalizedMessage());
		}
		return "NA";
	}
}