package com.nimble.webhooks.dao.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IGatewayDao;
import com.nimble.webhooks.dto.GatewayInfo;

@Repository
public class GatewayDaoImpl implements IGatewayDao {
	
	@Autowired
	private SessionFactory sessionFactory;
	
	private static final Logger log = LogManager.getLogger(GatewayDaoImpl.class);

	@Override
	public long getgatewayByMonitorId(long userid, long monitortype_id) {
		log.info("Entered getgatewayByMonitorId :: monitortype_id : "+monitortype_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT g.id "
					+ "FROM gateway g "
					+ "join assetmodel am on am.id=g.model_id "
					+ "join usergateway ug on ug.gatewayid=g.id "
					+ "join user u on u.id=ug.userid "
					+ "where u.id=:userid and am.monitor_type_id=:monitorid";
			Query query = session.createSQLQuery(qry);
			query.setParameter("monitorid", monitortype_id);
			query.setParameter("userid", userid);

			List<BigInteger> gatewayList = (List<BigInteger>) query.list();
			if (gatewayList.size() == 1) {
				return gatewayList.get(0).longValue();
			}
		} catch (Exception e) {
			log.error("Error in getgatewayByMonitorId: " + e.getLocalizedMessage());
		}
		return 0;
	}
	
	@Override
	public HashMap<String, String> getGatewayDetail(long gateway_id) {
		log.info("Entered getGatewayDetail : "+gateway_id);
		 HashMap<String, String> gatewayInfo = null;

		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT `installed_date`,isgps,O.shortdescription FROM gateway G  JOIN assetmodel A ON A.id=G.model_id "
					+ "join order_channel O on O.id= G.order_channel WHERE "
					+ "installed_date !=('1970-01-01 10:00:00') AND installed_date !=('1753-01-01 00:00:00' ) AND G.id=:id";
			Query query = session.createSQLQuery(qry);
			query.setParameter("id", gateway_id);

			List<Object[]> gatewayList = (List<Object[]>) query.list();
			gatewayInfo = new HashMap<String, String>();
		        if (!gatewayList.isEmpty()) {
		        	Object[] gObj = gatewayList.get(0);
		        	gatewayInfo.put("installed_date", (String)gObj[0]);
		        	gatewayInfo.put("isgps", (String)gObj[1]);
		        	gatewayInfo.put("order_channel", (String)gObj[2]);
		        }
		} catch (Exception e) {
			log.error("Error in getgatewayByMonitorId: " + e.getLocalizedMessage());
		}
		return gatewayInfo;
	}
	
	@Override
	public long getMonitorType(String cbPlanid) {
		log.info("Entered getMonitorType :"+cbPlanid);
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT P.`monitor_type` FROM plan P JOIN plan_to_period PP ON P.id=PP.plan_id WHERE PP.chargebee_planid=:cbPlanid";
			Query query = session.createSQLQuery(qry);
			query.setParameter("cbPlanid", cbPlanid);

			List<BigInteger> gatewayList = (List<BigInteger>) query.list();
			if (gatewayList.size() == 1) {
				return gatewayList.get(0).longValue();
			}
		} catch (Exception e) {
			log.error("Error in getMonitorType: " + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public long getgatewayById(long gatewayId) {
		log.info("Entered getgatewayById :: ");
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT am.monitor_type_id "
					+ "FROM gateway g "
					+ "join assetmodel am on am.id=g.model_id "
					+ "where g.id=:gatewayid ";
			Query query = session.createSQLQuery(qry);
			query.setParameter("gatewayid", gatewayId);

			List<BigInteger> gatewayList = (List<BigInteger>) query.list();
			if (!gatewayList.isEmpty()) {
				return gatewayList.get(0).longValue();
			}
		} catch (Exception e) {
			log.error("Error in getgatewayById: " + e.getLocalizedMessage());
		}
		return 0;
	}

	@Override
	public ArrayList<Long> getGatewayIdsByUserId(long userId) {
		log.info("Entered into getGatewayIdsByUserId :: user_id : "+ userId);
		ArrayList<Long> gatewayList = new ArrayList<>();
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = "SELECT gatewayId FROM usergateway WHERE userId = "+userId;
			List str = session.createSQLQuery(qry).list();
			gatewayList = (ArrayList<Long>) str.stream().map(id -> ((BigInteger) id).longValue() ).collect(Collectors.toList());
		} catch (Exception e) {
			log.error("Error in getUserGateway :: Error : "+e.getLocalizedMessage());
		}
		return gatewayList;
	}

	@Override
	public ArrayList<GatewayInfo> getGatewayInfo(String gatewayIds) {
		log.info("Entered into getGatewayModelType :: gateway_ids : "+ gatewayIds);
		ArrayList<GatewayInfo> gatewayInfoList = new ArrayList<>();
		try {
			Session ses = sessionFactory.getCurrentSession();

			String qry = "SELECT G.id, G.meid, AM.monitor_type_id FROM gateway G " +
					"JOIN assetmodel AM ON AM.id = G.model_id " +
					"WHERE G.id IN("+ gatewayIds +")";

			SQLQuery sqlQry = ses.createSQLQuery(qry);
			List<Object[]> gatewayResult = sqlQry.list();

			for (Object[] result : gatewayResult) {

				GatewayInfo gatewayInfo = new GatewayInfo();
				if (result[0] != null)
					gatewayInfo.setGateway_id(((BigInteger) result[0]).longValue());
				if (result[1] != null)
					gatewayInfo.setMeid((String) result[1]);
				if (result[2] != null)
					gatewayInfo.setMonitor_type_id(((BigInteger) result[2]).longValue());

				gatewayInfoList.add( gatewayInfo );
			}
		} catch (Exception e) {
			log.error("Error in getGatewayModelType :: Error : "+e.getLocalizedMessage());
		}
		return gatewayInfoList;
	}
	
	@Override
	public ArrayList<Long> getPlanmappedGateway(String chargebeeId, String subscription_id) {
		log.info("Entered into getPlanmappedGateway :: chargebeeId : "+ chargebeeId);
		ArrayList<Long> gatewayList = new ArrayList<>();
		try {
			Session session = sessionFactory.getCurrentSession();

			String qry = "SELECT AP.gateway_id FROM all_product_subscription AP JOIN plan_to_period PP ON PP.chargebee_planid = AP.plan_id WHERE AP.subscription_status = 'active' AND AP.chargebee_id = '"+chargebeeId+"' AND AP.subscription_id != '"+subscription_id+"';";
			List str = session.createSQLQuery(qry).list();
			gatewayList = (ArrayList<Long>) str.stream().map(id -> ((BigInteger) id).longValue() ).collect(Collectors.toList());
		} catch (Exception e) {
			log.error("Error in getPlanmappedGateway :: Error : "+e.getLocalizedMessage());
		}
		return gatewayList;
	}

	@Override
	public ArrayList<GatewayInfo> getUserGatewaySubInfo(String userId, boolean isUserBased) {
		log.info("Entered into getGatewayModelType :: userIds : "+ userId);
		ArrayList<GatewayInfo> gatewayInfoList = new ArrayList<>();
		try {
			Session ses = sessionFactory.getCurrentSession();

			String qry = "SELECT G.id, G.meid, AM.monitor_type_id,APS.subscription_status, '1753-01-01 00:00:00' as pkttime_utc,APS.subscription_id, APS.plan_id FROM gateway G JOIN assetmodel AM ON AM.id = G.model_id " +
							 "JOIN usergateway UG on UG.gatewayId = G.id LEFT JOIN all_product_subscription APS on APS.gateway_id = G.id WHERE UG.userId IN(" + userId + ") AND APS.is_deleted = 0";
			if (isUserBased) {
				qry = "SELECT G.id, G.meid, AM.monitor_type_id,ACS.subscription_status, LGR.pkttime_utc,ACS.subscription_id, ACS.plan_id FROM gateway G JOIN assetmodel AM ON AM.id = G.model_id " +
						  "JOIN usergateway UG on UG.gatewayId = G.id JOIN `user` U on U.id = UG.userId JOIN lastgatewayreport LGR on LGR.gateway_id = G.id LEFT JOIN all_chargebee_subscription ACS on ACS.chargebee_id = U.chargebeeId WHERE UG.userId IN(" + userId + ") AND ACS.is_deleted = 0 AND ACS.is_migrated = 0 AND ACS.duplicate_subs = 0 GROUP BY G.id; ";
			}

			SQLQuery sqlQry = ses.createSQLQuery(qry);
			List<Object[]> gatewayResult = sqlQry.list();

			for (Object[] result : gatewayResult) {

				GatewayInfo gatewayInfo = new GatewayInfo();
				if (result[0] != null)
					gatewayInfo.setGateway_id(((BigInteger) result[0]).longValue());
				if (result[1] != null)
					gatewayInfo.setMeid((String) result[1]);
				if (result[2] != null)
					gatewayInfo.setMonitor_type_id(((BigInteger) result[2]).longValue());
				if(result[3] != null)
					gatewayInfo.setSub_status(result[3].toString());
				if(result[4] != null)
					gatewayInfo.setLastReportTime(result[4].toString());
				if(result[5] != null)
					gatewayInfo.setSub_id(result[5].toString());
				if(result[6] != null)
					gatewayInfo.setPlan_id(result[6].toString());

				gatewayInfoList.add( gatewayInfo );
			}
		} catch (Exception e) {
			log.error("Error in getGatewayModelType :: Error : "+e.getLocalizedMessage());
		}
		return gatewayInfoList;
	}
	
	@Override
	public int getPlanbaseddevicecnt(String planId) {
		log.info("Entered into getPlanbaseddevicecnt :: planId : "+ planId);
		int deviceCnt = 0;
		try {
			Session session = sessionFactory.getCurrentSession();
			String qry = " SELECT p.device_cnt FROM plan p JOIN plan_to_period pp ON pp.plan_id = p.id WHERE pp.chargebee_planid = '"+planId+"';";
			List str = session.createSQLQuery(qry).list();
			deviceCnt = (int) str.get(0);
		} catch (Exception e) {
			log.error("Error in getPlanbaseddevicecnt :: Error : "+e.getLocalizedMessage());
		}
		return deviceCnt;
	}
}
