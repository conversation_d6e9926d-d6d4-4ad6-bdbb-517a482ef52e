package com.nimble.webhooks.dao;

import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.AdditionBenefitsCancelReward;
import com.nimble.webhooks.entity.AllProductSubscription;
import com.nimble.webhooks.entity.AllSubscription;
import com.nimble.webhooks.entity.UserCancelFeedBack;

public interface IUserDao {

	UserV4 verifyAuthV4(String key, String value);

	UserV4 getUserByUNameOrEmail(String cbEmail);

	boolean updateProductSubscription(long user_id);
	
	public int executeQuery(String qry);

	public UserCancelFeedBack getUserCancelFeedBackByUserId(long user_id);

	public boolean deteleUserCancelFeedBack(UserCancelFeedBack userCancelFeedBack);

	public AdditionBenefitsCancelReward getAdditionBenefitsCancelReward(long user_id);

	public boolean deteleAdditionBenefitsCancelReward(AdditionBenefitsCancelReward additionBenefitsCancelReward);

	long getUserByChargebeeId(String customerId);

	boolean updateProductSubscriptionByOrderId(long id, String orderId);

	String getQrcIdfromgatewayId(long gatewayid);

	String getOrderIdfromQrc(String qrc);
	
	public boolean updateDuplicateAllCBSubscription(AllSubscription prodSubs);
	
	public boolean updateDuplicateAllProductSubscription(AllProductSubscription prodSubs);

	String getDeviceToken(long userId);
}
