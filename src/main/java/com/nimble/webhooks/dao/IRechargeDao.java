package com.nimble.webhooks.dao;

import java.util.HashMap;

import com.nimble.webhooks.dto.JKcalDetails;
import com.nimble.webhooks.entity.LatestRechargeSubscription;
import com.nimble.webhooks.entity.ReCBWebhookStatus;
import com.nimble.webhooks.entity.RechargeSubscription;

public interface IRechargeDao {

	public String getCBSubId(String re_subid);
	
	public boolean saveRechargeSubscription(RechargeSubscription reSub);
	
	public boolean saveLatestRechargeSubscription(LatestRechargeSubscription reSub);
	
	public HashMap<String,String> getReSubscriptionPlans();
	
	public int saveReCancelCustomer(long userid, String re_cust_id);
	
	public int updateReSubHistory(String re_sub_id);

	public LatestRechargeSubscription getRechargeSubDetailsByCusID(String reCustomerId);
	
	public LatestRechargeSubscription getRechargeSubDetailsBySubID(String subid,String reCustomerId);
	
	public boolean saveRechargeCBSubStatus(ReCBWebhookStatus reSub);
	
	public boolean saveSmartBowlUserDetails(JKcalDetails jdetails);
}
