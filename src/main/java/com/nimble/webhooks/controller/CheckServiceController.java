package com.nimble.webhooks.controller;

import java.net.InetAddress;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.webhooks.dto.pingdom_http_custom_check;
import com.nimble.webhooks.entity.Testtable;
import com.nimble.webhooks.service.ICheckserviceService;

@Controller
public class CheckServiceController {

	private static final Logger log = LogManager.getLogger(CheckServiceController.class);

	@Autowired
	ICheckserviceService checkService;

	@RequestMapping(value = "/checkwagglehooks", method = RequestMethod.GET, produces = MediaType.APPLICATION_XML_VALUE)
	@ResponseBody
	public ResponseEntity<pingdom_http_custom_check> checkDatabase() {

		log.info("Entered in checkDatabse : ");
		String result = "";

		try {
			long milliseconds = new Date().getTime();
			String hashKey = InetAddress.getLocalHost().getHostAddress() + "_" + milliseconds;
			
			Testtable tt = new Testtable();

			log.info("Server Ip With Hash : " + hashKey);

			tt.setServerIp(hashKey);
			tt.setCheckServer("wagglehooks_Create");

			log.info("wagglehooks test Create Query ");
			Testtable ttable = checkService.testSaveOrUpdatequery(tt);

			if (ttable == null)
				result = "i";

			log.info("wagglehooks test Select Query ");
			ttable = checkService.testselectquery(tt);

			if (ttable != null || ttable.equals(tt))
				result = "s";

			log.info("wagglehooks test Update Query ");
			log.info(InetAddress.getLocalHost().getHostAddress());

			ttable.setCheckServer("wagglehooks_Close");
			ttable.setServerIp(hashKey);
			ttable = checkService.testSaveOrUpdatequery(ttable);

			if (ttable == null)
				result = "u";

			log.info("wagglehooks test Delete Query ");
			ttable = checkService.testDeletequery(ttable);

			if (ttable == null)
				result = "d";

			pingdom_http_custom_check response = new pingdom_http_custom_check();
			response.setStatus("OK");
			response.setResponse_time(96.777);
			return new ResponseEntity<>(response, HttpStatus.OK);

		} catch (Exception e) {
			log.error("Error in checkDatabase: "+e.getLocalizedMessage());
			pingdom_http_custom_check response = new pingdom_http_custom_check();
			response.setStatus("FAIL");
			response.setResponse_time(96.777);
			return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
		}

	}
}
