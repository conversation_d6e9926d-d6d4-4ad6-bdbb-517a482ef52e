package com.nimble.webhooks.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Base64;
import java.util.Enumeration;
import java.util.HashMap;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.IUserService;
import com.nimble.webhooks.util.IrisUtil;

@RestController
public class ChargebeeWebhooks {

	@Value("${iris.services.amazonSQS.microserviceQueue.url}")
	private String amazonSQS_microserviceQueue_url;

	@Value("${microservice_url}")
	private String microservice_url;

	@Value("${microservice_api_call}")
	private boolean microserviceApiCall;

	@Value("${chargebee.credentials}")
	private String headerCredentials;

	@Autowired
	IChargebeeWebhookService chargebeeWebhookService;

	@Autowired
	IAsyncService async;

	@Autowired
	IUserService userService;

	UserV4 user = null;

	Helper _helper = new Helper();

	public IrisUtil irisUtil;

	private static final Logger log = LogManager.getLogger(ChargebeeWebhooks.class);

	@RequestMapping(value = "/v1.0/waggle/chargebeewebhooks", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<?> Chargebeewebhooks(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("Entered into Chargebee webhooks");
		ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
		try {

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			try {
				HashMap<String,String> map = new HashMap<String,String>();
				Enumeration headerNames = request.getHeaderNames();
				while (headerNames.hasMoreElements()) {
					String key = (String) headerNames.nextElement();
					String value = request.getHeader(key);
					map.put(key, value);
				}

				String values = (String) map.get("authorization");
				byte[] authEncBytes = Base64.getDecoder().decode(values.split(" ")[1].getBytes("UTF-8"));
				String credentials = new String(authEncBytes, "UTF-8");
				if (!credentials.equals(headerCredentials))
					return new ResponseEntity<>("Invalid Credentials", HttpStatus.UNAUTHORIZED);

			} catch (Exception e) {
				return new ResponseEntity<>("Error ", HttpStatus.UNAUTHORIZED);
			}

			rd.close();

			JSONObject res = new JSONObject(result.toString());

			String event_id = res.getString("id");
			log.info("Event ID : " + event_id);
			String event_type = res.getString("event_type");

			whStatus.setEventId(event_id);
			whStatus.setEventType(event_type);
			whStatus = chargebeeWebhookService.webHookStatusIsAvailable(whStatus);

			if (whStatus != null && whStatus.isStatus()) {
				log.error("event_id and event_type Already available in webhooks_status history Table. : " + event_id);
				return new ResponseEntity<>("Already available", HttpStatus.OK);
			}

			whStatus = setData(res);
			ChargebeeWebhooksStatus chargebeeWebhooksStatus = chargebeeWebhookService.saveWebHookStatus(whStatus);

			log.info("Event status Stored in DB : " + chargebeeWebhooksStatus == null ? "Not able to update in DB"
					: chargebeeWebhooksStatus.getEventId());

			chargebeeWebhookService.chargebeeEventWebhookProcess(res, chargebeeWebhooksStatus);

			return new ResponseEntity<>("Success", HttpStatus.OK);
		} catch (Exception e) {
			log.error("Error in Chargebeewebhooks : " + e.getLocalizedMessage());
			return new ResponseEntity<>("Exception - checkChargebeewebhooks:", HttpStatus.OK);
		}
	}
	/**
	 * <AUTHOR>
	 * @param res
	 * @return
	 */	
	private ChargebeeWebhooksStatus setData(JSONObject res) {
		try {

			JSONObject content = res.getJSONObject("content");
			String chargebeeId = "NA";

			if (content.has("customer")) {
				JSONObject customer = content.getJSONObject("customer");
				chargebeeId = customer.getString("id");
			} else if (content.has("invoice")) {
				chargebeeId = content.getJSONObject("invoice").getString("customer_id");
			}else if (content.has("subscription")) {
				chargebeeId = content.getJSONObject("subscription").getString("customer_id");
			}

			String event_id = res.getString("id");
			String event_type = res.getString("event_type");

			ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
			String curUTC = IrisUtil.getUtcDateTime();
			whStatus.setChargebeeId(chargebeeId);
			whStatus.setCreatedOn(curUTC);
			whStatus.setUpdatedOn(curUTC);
			whStatus.setEventId(event_id);
			whStatus.setEventType(event_type);
			whStatus.setEventProcess("Initialize Event");
			whStatus.setEventStatus("Initialize Event");

			return whStatus;
		} catch (Exception e) {
			log.error("Error in setData:"+e.getLocalizedMessage());
			return null;
		}
	}

	@RequestMapping(value = "/v1.0/waggle/chargebeewebhooksinvoice", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<?> chargebeeInvoice(HttpServletRequest request, HttpServletResponse responce)
			throws ServletException, IOException {
		log.info("Entered into chargebeeInvoice");
		ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
		try {

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			try {
				HashMap<String,String> map = new HashMap<String,String>();
				Enumeration headerNames = request.getHeaderNames();
				while (headerNames.hasMoreElements()) {
					String key = (String) headerNames.nextElement();
					String value = request.getHeader(key);
					map.put(key, value);
				}

				String values = (String) map.get("authorization");
				byte[] authEncBytes = Base64.getDecoder().decode(values.split(" ")[1].getBytes("UTF-8"));
				String credentials = new String(authEncBytes, "UTF-8");
				if (!credentials.equals(headerCredentials))
					return new ResponseEntity<>("Invalid Credentials", HttpStatus.UNAUTHORIZED);

				rd.close();

				JSONObject res = new JSONObject(result.toString());

				//JSONObject content = res.getJSONObject("content");

				String event_id = res.getString("id");
				log.info("Event ID : " + event_id);
				String event_type = res.getString("event_type");
				//String eventProcess = "";

				whStatus.setEventId(event_id);
				whStatus.setEventType(event_type);
				whStatus = chargebeeWebhookService.webHookStatusIsAvailable(whStatus);

				if (whStatus != null && whStatus.isStatus()) {
					log.error("event_id and event_type Already available in webhooks_status history Table. : "
							+ event_id);
					return new ResponseEntity<>("Already available", HttpStatus.OK);
				}

				whStatus = setData(res);
				ChargebeeWebhooksStatus chargebeeWebhooksStatus = chargebeeWebhookService.saveWebHookStatus(whStatus);
				log.info("Event status Stored in DB : " + chargebeeWebhooksStatus == null ? "Not able to update in DB"
						: chargebeeWebhooksStatus.getEventId());

				async.chargebeeEventWebhookInvoiceProcess(res, chargebeeWebhooksStatus);

				return new ResponseEntity<>("Success", HttpStatus.OK);
			} catch (Exception e) {
				return new ResponseEntity<>("Error ", HttpStatus.UNAUTHORIZED);
			}
		} catch (Exception e) {
			log.error("Exception - checkChargebeewebhooks : " + e.getLocalizedMessage());
			return new ResponseEntity<>("Exception - checkChargebeewebhooks:", HttpStatus.OK);
		}
	}
	
	@RequestMapping(value = "/v1.0/waggle/chargebeewebhooksinfobip", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<?> ChargebeewebhooksInfoBip(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("Entered into ChargebeewebhooksInfoBip");
		ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
		try {

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			try {
				HashMap<String,String> map = new HashMap<String,String>();
				Enumeration headerNames = request.getHeaderNames();
				while (headerNames.hasMoreElements()) {
					String key = (String) headerNames.nextElement();
					String value = request.getHeader(key);
					map.put(key, value);
				}

				String values = (String) map.get("authorization");
				byte[] authEncBytes = Base64.getDecoder().decode(values.split(" ")[1].getBytes("UTF-8"));
				String credentials = new String(authEncBytes, "UTF-8");
				if (!credentials.equals(headerCredentials))
					return new ResponseEntity<>("Invalid Credentials", HttpStatus.UNAUTHORIZED);

			} catch (Exception e) {
				return new ResponseEntity<>("Error ", HttpStatus.UNAUTHORIZED);
			}

			rd.close();

			JSONObject res = new JSONObject(result.toString());

			String event_id = res.getString("id");
			log.info("ChargebeewebhooksInfoBip Event ID : " + event_id);
			String event_type = res.getString("event_type");
			whStatus.setEventId(event_id);
			whStatus.setEventType(event_type);
			whStatus = chargebeeWebhookService.webHookStatusIsAvailable(whStatus);

			if (whStatus != null && whStatus.isStatus()) {
				log.error("event_id and event_type Already available in webhooks_status history Table. : " + event_id);
				return new ResponseEntity<>("Already available", HttpStatus.OK);
			}

			whStatus = setData(res);
			whStatus = setData(res);
			ChargebeeWebhooksStatus chargebeeWebhooksStatus = chargebeeWebhookService.saveWebHookStatus(whStatus);
			log.info("Event status Stored in DB : " + chargebeeWebhooksStatus == null ? "Not able to update in DB"
					: chargebeeWebhooksStatus.getEventId());
			
			async.infobibEventhandling(res, event_type,chargebeeWebhooksStatus);

			return new ResponseEntity<>("Success", HttpStatus.OK);
		} catch (Exception e) {
			log.error("Error in ChargebeewebhooksInfoBip : " + e.getLocalizedMessage());
			return new ResponseEntity<>("Exception - ChargebeewebhooksInfoBip:", HttpStatus.OK);
		}
	}
	
}
