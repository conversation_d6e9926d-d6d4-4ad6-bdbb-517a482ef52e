package com.nimble.webhooks.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.nimble.webhooks.entity.LatestRechargeSubscription;
import com.nimble.webhooks.entity.RechargeSubscription;
import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.pojo.Order;
import com.nimble.webhooks.pojo.Order.LineItem;
import com.nimble.webhooks.pojo.Subscription;
import com.nimble.webhooks.service.IRechargeService;

@Controller
public class RechargeController {

	private static final Logger log = LogManager.getLogger(RechargeController.class);

	@Autowired
	@Lazy
	IRechargeService reService;

	Helper helper = new Helper();

	@Value("${Recharge.url}")
	private String recharge_url;
	
	@Value("${Recharge.access-token}")
	private String access_token;

	@ResponseBody
	@RequestMapping(value = "/ordercreated", headers = "Accept=application/json")
	public ResponseEntity<?> orderCreated(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		try {
			log.info("orderCreated method");
			System.out.println("orderCreated");
			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {

				result.append(line);
			}
			rd.close();
			log.info("orderCreated response : " + result);
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Order order = gson.fromJson(json.getJSONObject("order").toString(), Order.class);

			if (order != null) {
				RechargeSubscription sub = new RechargeSubscription();
				String sku = "NA";
				String cb_plan = "NA";
				String subId = "NA";
				int qty = 0;
				for (LineItem li : order.line_items ) {
					
					if (li.purchase_item_type.equalsIgnoreCase("subscription")) {
						sku = li.sku;
						subId = String.valueOf(li.purchase_item_id);
						
						if (sku != null && !sku.equalsIgnoreCase("null"))
							cb_plan = getCBPlanFromSKU(sku);
						else
							sku = "NA";
						
					}else if (li.purchase_item_type.equalsIgnoreCase("onetime")) {
						qty = li.quantity;
					}
				}
				
				sub.setOrder_id(order.getExternal_order_name().ecommerce);
				sub.setSku(sku);
				sub.setCb_plan(cb_plan);
				sub.setSub_id(subId);
				sub.setCustomer_id(String.valueOf(order.getCustomer().id));
				sub.setEmail(String.valueOf(order.getCustomer().email));
				sub.setCreated_at(order.getCreated_at());
				sub.setUpdated_at(order.getUpdated_at());
				
				Subscription subPojo = getSubRenewalDt(sub.getSub_id());
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date nextrenewal_at = null;
			
				if(subPojo.getNext_charge_scheduled_at() != null)
					 nextrenewal_at = sdf.parse(subPojo.getNext_charge_scheduled_at());
				
				sub.setNextrenewal_at(nextrenewal_at);				
				sub.setStatus(subPojo.getStatus());
				float sprice = Float.parseFloat(subPojo.getPrice());
				sub.setPrice(sprice);

				sub.setEvent_type("order/created");
				
				if(order.getType().equalsIgnoreCase("checkout"))
				{
					LatestRechargeSubscription lrs = new LatestRechargeSubscription(sub.getSub_id(), sub.getOrder_id(),
							sub.getCustomer_id(), sub.getEmail(), sub.getSku(), sub.getCb_plan(), sub.getNextrenewal_at(),
							sub.getStatus(), sub.getCreated_at(), sub.getUpdated_at(), sub.getCancelled_at(), sub.getPrice(),
							qty,"NA");
					lrs.setEvent_type("order/created");
					
					boolean stat1 = reService.saveLatestRechargeSubscription(lrs);
					log.info("saveLatestRechargeSubscription:"+stat1);
				}else {
					LatestRechargeSubscription lrs = reService.getRechargeSubDetailsBySubID(sub.getSub_id(), sub.getCustomer_id());
					if(lrs != null) {
						lrs.setNextrenewal_at(sub.getNextrenewal_at());
						lrs.setPrice(sub.getPrice());
						lrs.setUpdated_at(sub.getUpdated_at());
						
					}else {
						lrs = new LatestRechargeSubscription(sub.getSub_id(), sub.getOrder_id(),
								sub.getCustomer_id(), sub.getEmail(), sub.getSku(), sub.getCb_plan(), sub.getNextrenewal_at(),
								sub.getStatus(), sub.getCreated_at(), sub.getUpdated_at(), sub.getCancelled_at(), sub.getPrice(),qty,"NA");
						lrs.setEvent_type("order/created");
					}
					
					boolean stat1 = reService.saveLatestRechargeSubscription(lrs);
					log.info("saveLatestRechargeSubscription:"+stat1);
				}
				boolean stat = reService.saveRechargeSubscription(sub);
				log.info("saveRechargeSubscription:"+stat);
			}
		} catch (Exception e) {
			//e.printStackTrace();
			log.error("orderCreated : " + e.getLocalizedMessage());
			return new ResponseEntity<>("Exception - orderCreated", HttpStatus.OK);
		}
		return new ResponseEntity<>("Success", HttpStatus.OK);
	}

	@ResponseBody
	@RequestMapping(value = "/subscriptioncreated", headers = "Accept=application/json")
	public ResponseEntity<?> subscriptioncreated(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("subscriptioncreated method");

		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("subscriptioncreated response : " + result);
		try {
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);

			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
			reSub.setEvent_type("subscription/created");
			boolean stat = reService.saveRechargeSubscription(reSub);

		} catch (Exception e) {
			//e.printStackTrace();
			log.error("subscriptioncreated : " + e.getLocalizedMessage());
			return new ResponseEntity<>("Exception - subscriptioncreated", HttpStatus.OK);
		}
		return new ResponseEntity<>("Success", HttpStatus.OK);
	}

	@ResponseBody
	@RequestMapping(value = "/subscriptionupdated", headers = "Accept=application/json")
	public void subscriptionupdated(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("subscriptionupdated method");
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("subscriptionupdated response : " + result);
		try {
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);

			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
			reSub.setEvent_type("subscription/updated");

			boolean stat = reService.saveRechargeSubscription(reSub);
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			//sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
			
			String nxtRenewDt = sdf.format(reSub.getNextrenewal_at());
			
			reService.updateCBSubsRenewalDt(reSub.getSub_id(), nxtRenewDt,subs.getPrice());
			
		} catch (Exception e) {
			//e.printStackTrace();
			log.error("subscriptionupdated : " + e.getLocalizedMessage());
		}
	}

	@ResponseBody
	@RequestMapping(value = "/subscriptionactivated", headers = "Accept=application/json")
	public void subscriptionActivated(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("subscriptionactivated method");
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("subscriptionactivated response : " + result);
		try {
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);

			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
			reSub.setEvent_type("subscription/activated");

			boolean stat = reService.saveRechargeSubscription(reSub);

		} catch (Exception e) {
			//e.printStackTrace();
			log.error("subscriptionupdated : " + e.getLocalizedMessage());
		}
	}

	@ResponseBody
	@RequestMapping(value = "/subscriptiondeleted", headers = "Accept=application/json")
	public void subscriptionDeleted(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("subscriptiondeleted method");
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("subscriptiondeleted response : " + result);
		try {
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);

			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
			reSub.setEvent_type("subscription/deleted");

			boolean stat = reService.saveRechargeSubscription(reSub);
			boolean stat1 = reService.cancelRechargeSubscription(reSub);

		} catch (Exception e) {
			//e.printStackTrace();
			log.error("subscriptiondeleted : " + e.getLocalizedMessage());
		}
	}

	@ResponseBody
	@RequestMapping(value = "/subscriptioncancelled", headers = "Accept=application/json")
	public void subscriptioncancelled(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("subscriptioncancelled method");
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("subscriptioncancelled response : " + result);
		try {
			Gson gson = new Gson();
			JSONObject json = new JSONObject(result.toString());
			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);
			
			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
			reSub.setEvent_type("subscription/cancelled");
			
//			LatestRechargeSubscription latestRechargeSubscription = null;
//			try {
//				// email and other info not comes in subcription cancel event in recharge, So we get it from recharge_latest_sub_history table
//				latestRechargeSubscription = reService.getRechargeSubDetailsByCusID(subs.getCustomer_id()+"");
//				
//				if(latestRechargeSubscription != null ) {
//					reSub.setUserInfo(latestRechargeSubscription.getOrder_id(), latestRechargeSubscription.getEmail(), latestRechargeSubscription.getSku(), latestRechargeSubscription.getCb_plan());
//				}
//				
//			} catch (Exception e) {
//				log.info("Recharge customer id not found in recharge_latest_sub_history table :: recharge_customer_id : "+subs.getCustomer_id());
//			}
			
			boolean stat = reService.saveRechargeSubscription(reSub);
			
			boolean stat1 = reService.cancelRechargeSubscription(reSub);

		} catch (Exception e) {
			e.printStackTrace();
			log.error("subscriptioncancelled : " + e.getLocalizedMessage());
		}
	}

	@ResponseBody
	@RequestMapping(value = "/maxretriesreached", headers = "Accept=application/json")
	public void maxretriesreached(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("maxretriesreached method");
		BufferedReader rd = request.getReader();
		StringBuffer result = new StringBuffer();
		String line = "";

		while ((line = rd.readLine()) != null) {

			result.append(line);
		}
		rd.close();
		log.info("maxretriesreached response : " + result);
//		try {
//			Gson gson = new Gson();
//			JSONObject json = new JSONObject(result.toString());
//			Subscription subs = gson.fromJson(json.getJSONObject("subscription").toString(), Subscription.class);
//
//			RechargeSubscription reSub = convertPojotoRechargeSub(subs);
//			reSub.setEvent_type("maxretriesreached");
//
//			boolean stat = reService.saveRechargeSubscription(reSub);
//			boolean stat1 = reService.cancelRechargeSubscription(String.valueOf(subs.getCustomer_id()), String.valueOf(subs.getId()));
//
//		} catch (Exception e) {
//			e.printStackTrace();
//			log.error("Error maxretriesreached : " + e.getLocalizedMessage());
//		}
	}

	public RechargeSubscription convertPojotoRechargeSub(Subscription sub) {

		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
			SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss");
			sdf1.setTimeZone(TimeZone.getTimeZone("UTC"));
			
			String sub_id = String.valueOf(sub.getId());
			String order_id = "NA";
			String cb_plan = "NA";
			String customer_id = String.valueOf(sub.getCustomer_id());
			String sku = sub.getSku();
			String next_charge_scheduled_at = sub.getNext_charge_scheduled_at();
			Date nextrenewal_at = null;
			String email = "NA";

			if(next_charge_scheduled_at!= null) {
				nextrenewal_at = sdf.parse(next_charge_scheduled_at+" 00:00:01");
				
				email = getCustomerEmail(customer_id);
			}
			
			String status = sub.getStatus();
			Date created_at = sub.getCreated_at();
			Date updated_at = sub.getUpdated_at();
			
			if(sku != null && !sku.equalsIgnoreCase("null"))
				cb_plan = getCBPlanFromSKU(sku);
			else
				sku = "NA";
			
			Date cancelled_at = null;
			if (sub.getCancelled_at() != null) {
				cancelled_at = sdf1.parse((String)sub.getCancelled_at());
				
			}
			//String email = getCustomerEmail(customer_id);
			RechargeSubscription reSub = new RechargeSubscription(sub_id, order_id, customer_id, email, sku,cb_plan,
					nextrenewal_at, status, created_at, updated_at,	cancelled_at,Float.valueOf(sub.getPrice()));

			return reSub;
		} catch (Exception e) {
			log.error("convertPojotoRechargeSub" + e.getLocalizedMessage());
			return null;
		}
	}

	private String getCustomerEmail(String cusId) {
		String email = "NA";
		try {
			String sub_url = recharge_url + "/customers/" + cusId.trim();
			String respstatus = helper.getRechargeResp(sub_url,access_token);
			JSONObject json = new JSONObject(respstatus.toString());
			JSONObject customer = json.getJSONObject("customer");
			email = customer.getString("email");
		} catch (Exception e) {
			log.error("getCustomerEmail: " + e.getLocalizedMessage());
		}
		return email;
	}
	
	private Subscription getSubRenewalDt(String subId) {
		Subscription sub = new Subscription();
		
		String next_charge_scheduled_at = "NA";
		String price = "0.0";
		try {
			String sub_url = recharge_url + "/subscriptions/" + subId.trim();
			String respstatus = helper.getRechargeResp(sub_url,access_token);
			JSONObject json = new JSONObject(respstatus.toString());
			JSONObject subscription = json.getJSONObject("subscription");
			next_charge_scheduled_at = subscription.getString("next_charge_scheduled_at");
			
			price = subscription.getString("price");
			sub.setPrice(price);
			sub.setStatus(subscription.getString("status"));
			
			if(next_charge_scheduled_at.equalsIgnoreCase("null"))
				sub.setNext_charge_scheduled_at(null);
			else
				sub.setNext_charge_scheduled_at(next_charge_scheduled_at);
			
				
		} catch (Exception e) {
			log.error("getSubRenewalDt: " + e.getLocalizedMessage());
		}
		return sub;
	}
	private String getCBPlanFromSKU(String sku) {
		String cbPlanId = "NA";
		try {
			HashMap<String, String> planList = reService.getReSubscriptionPlans();
			String sku_list[] = sku.split("\\.");

			for (String subsku : sku_list) {
				if (planList.containsKey(subsku)) {					
					cbPlanId = subsku;
					return cbPlanId;
				}
			}
		}catch (Exception e) {
			log.error("getCBPlanFromSKU: " + e.getLocalizedMessage());
		}
		
		return cbPlanId;
	}
}
