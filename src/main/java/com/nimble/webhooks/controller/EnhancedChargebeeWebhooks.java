package com.nimble.webhooks.controller;

import com.nimble.webhooks.entity.ChargebeeWebhooksStatus;
import com.nimble.webhooks.service.IChargebeeWebhookService;
import com.nimble.webhooks.service.ISubscriptionMrrService;
import com.nimble.webhooks.service.impl.IdempotentWebhookService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;

/**
 * Enhanced webhook controller with improved concurrency control and idempotency
 */
@RestController
public class EnhancedChargebeeWebhooks {

    private static final Logger log = LogManager.getLogger(EnhancedChargebeeWebhooks.class);

    @Autowired
    private IChargebeeWebhookService chargebeeWebhookService;

    @Autowired
    @Qualifier("enhancedSubscriptionMrrService")
    private ISubscriptionMrrService enhancedMrrService;

    @Autowired
    private IdempotentWebhookService idempotentWebhookService;

    /**
     * Enhanced webhook endpoint with better concurrency control
     */
    @PostMapping("/enhanced-chargebee-webhooks")
    public ResponseEntity<String> handleEnhancedChargebeeWebhook(HttpServletRequest request) {
        
        String eventId = null;
        String eventType = null;
        
        try {
            // Read the webhook payload
            StringBuilder result = new StringBuilder();
            BufferedReader rd = request.getReader();
            String line;
            while ((line = rd.readLine()) != null) {
                result.append(line);
            }
            rd.close();

            JSONObject webhookPayload = new JSONObject(result.toString());
            
            eventId = webhookPayload.getString("id");
            eventType = webhookPayload.getString("event_type");
            
            log.info("Received enhanced webhook: eventId={}, eventType={}", eventId, eventType);

            // Handle MRR updates with enhanced processing
            if ("mrr_updated".equalsIgnoreCase(eventType)) {
                return handleMrrUpdatedWebhook(webhookPayload, eventId, eventType);
            }
            
            // For other webhook types, use the original processing
            return handleOtherWebhooks(webhookPayload, eventId, eventType);
            
        } catch (Exception e) {
            log.error("Error processing enhanced webhook: eventId={}, eventType={}, error={}", 
                     eventId, eventType, e.getMessage(), e);
            return new ResponseEntity<>("Error processing webhook", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    private ResponseEntity<String> handleMrrUpdatedWebhook(JSONObject payload, String eventId, String eventType) {
        try {
            JSONObject content = payload.getJSONObject("content");
            if (!content.has("subscription")) {
                log.warn("MRR webhook missing subscription data: eventId={}", eventId);
                return new ResponseEntity<>("Missing subscription data", HttpStatus.BAD_REQUEST);
            }
            
            JSONObject subscription = content.getJSONObject("subscription");
            String subId = subscription.getString("id");
            
            if (!subscription.has("mrr")) {
                log.warn("MRR webhook missing MRR value: eventId={}, subId={}", eventId, subId);
                return new ResponseEntity<>("Missing MRR value", HttpStatus.BAD_REQUEST);
            }
            
            long mrr = subscription.getLong("mrr");
            
            if (mrr <= 0) {
                log.info("Skipping MRR update with non-positive value: eventId={}, subId={}, mrr={}", 
                        eventId, subId, mrr);
                return new ResponseEntity<>("Non-positive MRR value", HttpStatus.OK);
            }
            
            // Use idempotent webhook service to check for duplicates
            IdempotentWebhookService.WebhookProcessingResult result = 
                idempotentWebhookService.checkAndReserveWebhookProcessing(eventId, eventType, subId, mrr);
            
            if (!result.shouldProcess()) {
                log.info("Webhook processing skipped: eventId={}, reason={}", eventId, result.getReason());
                return new ResponseEntity<>("Duplicate webhook - " + result.getReason(), HttpStatus.OK);
            }
            
            // Process the MRR update with enhanced service
            boolean updateSuccess = enhancedMrrService.updateMRR(subId, mrr);
            
            // Update webhook status
            ChargebeeWebhooksStatus webhookStatus = result.getWebhookStatus();
            if (updateSuccess) {
                webhookStatus.setEventStatus("MRR updated successfully");
                webhookStatus.setStatus(true);
                log.info("Enhanced MRR update successful: eventId={}, subId={}, mrr={}", eventId, subId, mrr);
            } else {
                webhookStatus.setEventStatus("MRR update failed");
                webhookStatus.setStatus(false);
                log.error("Enhanced MRR update failed: eventId={}, subId={}, mrr={}", eventId, subId, mrr);
            }
            
            chargebeeWebhookService.updateWebHookStatus(webhookStatus);
            
            return new ResponseEntity<>(updateSuccess ? "Success" : "Update failed", HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("Error in enhanced MRR webhook processing: eventId={}, error={}", eventId, e.getMessage(), e);
            return new ResponseEntity<>("Error processing MRR webhook", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    private ResponseEntity<String> handleOtherWebhooks(JSONObject payload, String eventId, String eventType) {
        try {
            // For non-MRR webhooks, use the original processing logic
            ChargebeeWebhooksStatus whStatus = new ChargebeeWebhooksStatus();
            whStatus.setEventId(eventId);
            whStatus.setEventType(eventType);
            
            // Check for duplicates using original method
            whStatus = chargebeeWebhookService.webHookStatusIsAvailable(whStatus);
            if (whStatus != null && whStatus.isStatus()) {
                log.info("Duplicate webhook detected: eventId={}, eventType={}", eventId, eventType);
                return new ResponseEntity<>("Already processed", HttpStatus.OK);
            }
            
            // Create new webhook status
            whStatus = createWebhookStatus(payload);
            ChargebeeWebhooksStatus saved = chargebeeWebhookService.saveWebHookStatus(whStatus);
            
            // Process using original method
            chargebeeWebhookService.chargebeeEventWebhookProcess(payload, saved);
            
            return new ResponseEntity<>("Success", HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("Error processing other webhook: eventId={}, eventType={}, error={}", 
                     eventId, eventType, e.getMessage(), e);
            return new ResponseEntity<>("Error processing webhook", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    private ChargebeeWebhooksStatus createWebhookStatus(JSONObject payload) {
        ChargebeeWebhooksStatus status = new ChargebeeWebhooksStatus();
        status.setEventId(payload.getString("id"));
        status.setEventType(payload.getString("event_type"));
        status.setEventProcess("Processing");
        status.setEventStatus("Received");
        status.setChargebeeId("NA"); // Will be set during processing
        status.setStatus(false);
        
        String currentTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
        status.setCreatedOn(currentTime);
        status.setUpdatedOn(currentTime);
        
        return status;
    }
}
