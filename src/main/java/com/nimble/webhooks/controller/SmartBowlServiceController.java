package com.nimble.webhooks.controller;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.nimble.webhooks.dto.JKcalDetails;
import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.service.IRechargeService;

@Controller
public class SmartBowlServiceController {
	
	private static final Logger log = LogManager.getLogger(SmartBowlServiceController.class);
	
	@Value("${kcalUsername}")
	private String kcalUsername;
	
	@Value("${kcalPassword}")
	private String kcalPassword;
	
	@Autowired
	@Lazy
	IRechargeService reService;
	
	@RequestMapping(value = "/saveSmartBowlUser", method = RequestMethod.POST, headers = "Accept=application/json")
	@ResponseBody
	public JResponse saveSmartBowlUser(@RequestBody JKcalDetails jpetprofiles,@RequestHeader HttpHeaders header) {
		log.info("Entered saveSmartBowlUser api : ");
		JResponse response = new JResponse();
		
		String basicAuth= header.getFirst("authorization");
		
		String deCodeAuth=new String(Base64.decodeBase64(basicAuth.substring(6)));
		String userName=deCodeAuth.split(":")[0];
		String password=deCodeAuth.split(":")[1];		

		if (userName == null || password == null || !userName.equals(kcalUsername) || !password.equals(kcalPassword) ) {
			response.put("Status", 0);
			response.put("Msg", "Authentication Error");
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}

		try {
			
			reService.saveSmartBowlUserDetails(jpetprofiles);
			
			response.put("Status", 1);
			response.put("Msg", "success");
			
		} catch (Exception e) {
			log.error("Exception in saveSmartBowlUser : " + e.getLocalizedMessage());
			response.put("Status", 0);
			response.put("Msg", "Exception Occured");
			response.put("Error", e.getLocalizedMessage());
			response.put("Return Time", System.currentTimeMillis());
			return response;
		}
		response.put("Return Time", System.currentTimeMillis());
		return response;
	}
	
}
