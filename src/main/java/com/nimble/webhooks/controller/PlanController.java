package com.nimble.webhooks.controller;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.webhooks.dto.JResponse;
import com.nimble.webhooks.dto.UserV4;
import com.nimble.webhooks.service.IPlanService;
import com.nimble.webhooks.service.IUserService;

@RestController
public class PlanController {

    @Autowired
    IUserService userService;

    @Autowired
    @Lazy
    IPlanService planService;

    private static final Logger log = LogManager.getLogger(PlanController.class);

    @PostMapping("/assigncomboplan")
    public JResponse assignCombo(@RequestParam long gateway_id, @RequestParam long user_id, @RequestParam String chargebee_planid, @RequestParam String subscription_id) {
       log.info("Entered into assignCombo :: user_id : "+user_id+" :: gateway_id : "+gateway_id+" :: chargebee_planid : "+chargebee_planid+" :: subscription_id : "+subscription_id);
        JResponse response = new JResponse();
       try {
           UserV4 user = null;
           try {
              user = userService.verifyAuthV4("id", String.valueOf(user_id));
           } catch (Exception e) {
               response.put("Status", 0);
               response.put("Msg", "User Not Found");
               response.put("Error", e.getMessage());
               return response;
           }

           response = planService.assignComboPlan(user, gateway_id, chargebee_planid, subscription_id);

       } catch (Exception e) {
           log.error("Error in assignCombo :: Error : "+ e.getLocalizedMessage());
           response.put("Status", 0);
           response.put("Msg", e.getMessage());
       }
       return response;
    }

}
