package com.nimble.webhooks.util;

import java.sql.Time;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import com.nimble.webhooks.constant.IrisConstants;


public class IrisUtil {

	public static String getCurrentTimeUTC() {
		Calendar calendar = Calendar.getInstance();
		Date d = calendar.getTime();

		SimpleDateFormat df = new SimpleDateFormat(IrisConstants.DATETIMEFORMAT);
		df.setTimeZone(TimeZone.getTimeZone(IrisConstants.UTCFORMAT));

		String currentTime = df.format(d);
		return currentTime;
	}

	/**
	 * 
	 * returns the dateTime string in "yyyy-mm-dd HH:mm:ss" format
	 * 
	 * @param date - in ddmmyy format
	 * @param time - in HHmmss format
	 * @return the combined date and time parameters in "yyyy-mm-dd HH:mm:ss" format
	 */
	public static String getDateTime(String date, String time) {
		String dateYYYYMMDD;
		String dateTime;
		dateYYYYMMDD = new String(
				"20" + date.substring(4, 6) + "-" + date.substring(2, 4) + "-" + date.substring(0, 2));
		dateTime = new String(
				dateYYYYMMDD + " " + time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return dateTime;
	}

	public static Timestamp getDateTime_TS(String dateTime) {
		return Timestamp.valueOf(dateTime);
	}

	public static String getDate(String date) {
		String newdate = new String(
				"20" + date.substring(4, 6) + "-" + date.substring(2, 4) + "-" + date.substring(0, 2));
		return newdate;
	}

	public static String getTime(String time) {
		String newtime = new String(time.substring(0, 2) + ":" + time.substring(2, 4) + ":" + time.substring(4, 6));
		return newtime;
	}

	public static String getUTCTime(String datetime, String timeZoneOffset) {

		SimpleDateFormat formatter = new SimpleDateFormat(IrisConstants.DATETIMEFORMAT);

		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + timeZoneOffset));

		Date date = null;
		try {
			date = formatter.parse(datetime);
			SimpleDateFormat dateFormat = new SimpleDateFormat(IrisConstants.DATETIMEFORMAT);
			dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
			return dateFormat.format(date);
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	public static long calcTimeDiffInMillis(String currTime, String prevTime) {
		long timeDiff = 0;
		try {
			SimpleDateFormat format = new SimpleDateFormat(IrisConstants.TIMEFORMAT);
			Date prevDate = format.parse(prevTime);
			Date currDate = format.parse(currTime);
			if (currDate.getTime() > prevDate.getTime()) {
				timeDiff = currDate.getTime() - prevDate.getTime();
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return timeDiff;
	}

	public static String getDateTimeTest(String dateTime, int minutes) {
		try {
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date d = df.parse(dateTime);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d);
			cal.add(Calendar.MINUTE, minutes);
			return df.format(cal.getTime());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return dateTime;
	}

	public static Date convertStrToDate(String dt, String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		try {
			Date date = formatter.parse(dt);
			return date;
			
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
		
	/*sql date*/
	public static java.sql.Date convertToDate(String dt, String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		try {
			java.util.Date date = formatter.parse(dt);
			//System.out.println("Date is: " + date);

			java.sql.Date dateObj = new java.sql.Date(date.getTime());

			return dateObj;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	
	/* sql time */
	public static Time convertToTime(String time, String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		try {
			java.util.Date date = formatter.parse(time);
			//System.out.println("Date is: " + date);
			java.sql.Time timeobj = new java.sql.Time(date.getTime());

			return timeobj;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		// time format should be hh:mm:ss format
		//Time timeobj = Time.valueOf(time);
		//return timeobj;
	}

	public static double calculateDistance(double longitude1, double latitude1, double longitude2, double latitude2) {
		double c = Math.sin(Math.toRadians(latitude1)) *
				Math.sin(Math.toRadians(latitude2))	+ 
				Math.cos(Math.toRadians(latitude1)) *
				Math.cos(Math.toRadians(latitude2))	*
				Math.cos(Math.toRadians(longitude2) -
						Math.toRadians(longitude1));

		c = c > 0 ? Math.min(1, c) : Math.max(-1, c);
		return 3959 * 1.609 * 1000 * Math.acos(c);
	}

	public static String hexToBinary(String hexValue) {
		String bin = "";
		String binFragment = "";
		int iHex;
		hexValue = hexValue.trim();

		int ioLength = 8 - hexValue.length();

		for (int i = 0; i < ioLength; i++) {
			hexValue = "0" + hexValue;
		}

		for (int i = 0; i < hexValue.length(); i++) {
			iHex = Integer.parseInt("" + hexValue.charAt(i), 16);
			binFragment = Integer.toBinaryString(iHex);

			while (binFragment.length() < 4) {
				binFragment = "0" + binFragment;
			}
			bin += binFragment;
		}
		return new StringBuffer(bin).reverse().toString();
	}

	public static String getCurrentDateTime(String timeFormat, String timezone, int days) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, days);
		Date d = calendar.getTime();
		SimpleDateFormat df = new SimpleDateFormat(timeFormat);
		df.setTimeZone(TimeZone.getTimeZone(timezone));
		String currentTime = df.format(d);
		return currentTime;
	}

	public static String getDateime_Timezone(String datetime, String inTimeZoneOffset, String outTimeZoneOffset){

		SimpleDateFormat formatter = new SimpleDateFormat(IrisConstants.DATETIMEFORMAT);
		formatter.setTimeZone(TimeZone.getTimeZone("GMT" + inTimeZoneOffset));
		Date date = null;
		try {
			date = formatter.parse(datetime);			
			SimpleDateFormat dateFormat = new SimpleDateFormat(IrisConstants.DATETIMEFORMAT);
			dateFormat.setTimeZone(TimeZone.getTimeZone("GMT" +outTimeZoneOffset));
			return dateFormat.format(date);
		} catch (ParseException e) {
			return null;
		}
	}

	public static long calDaysDiff(String startDate, String endDate) {
		//Parsing the date
		LocalDate dateBefore = LocalDate.parse(startDate);
		LocalDate dateAfter = LocalDate.parse(endDate);
		//calculating number of days in between
		long noOfDaysBetween = ChronoUnit.DAYS.between(dateBefore, dateAfter);
		return noOfDaysBetween;
	}

	public static  String getUtcDateTime() {

		Calendar currDateCal = Calendar.getInstance();
		currDateCal.setTimeZone(TimeZone.getTimeZone("UTC"));
		currDateCal.setTime(new Date(System.currentTimeMillis()));

		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");         
		dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

		String utcDateTime = dateFormat.format(currDateCal.getTime());
		return utcDateTime;
	}
	
}
