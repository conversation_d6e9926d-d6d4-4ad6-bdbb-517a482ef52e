#  MySQL Database Configuration
database.driverClassName=com.mysql.cj.jdbc.Driver

#development DB
database.url=******************************************************************************************

#DB Password
database.username=wghokss
database.password=hd@4e3Y50

#Microservice SQS configurations
send_registeruseremail_simactivation_to_microservice=false
iris.services.amazonSQS.microserviceQueue.url=https://sqs.us-west-2.amazonaws.com/258751312124/staging-microservice
iris.services.amazonSQS.accessKey=********************
iris.services.amazonSQS.secretKey=6g+X/4/z4RM17yFYTVtRl8Zl1RGu/yG6PaQFgk0f

############################################################
#Asyn Thread Pool Configuration
aysnc.corePoolSize=30
aysnc.maxPoolSize=100
aysnc.queueCapacity=150

#################################################################
#chargebee authorization credentials
chargebee.credentials=test:test

#database.url=***********************************
#database.url=**********************************
#database.username=nimble
#database.password=flamingo2010
#######################################################################
#DB 2
database.niom.url=******************************************************************************************
database.niom.username=niomsvc
database.niom.password=goyang1fetch

#######################################################################
#  Hibernate Configuration
hibernate.dialect=org.hibernate.dialect.MySQLDialect

#######################################################################
# Hibernate General Configuration
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.generate_statistics=false

#chargebee secret key test
chargebee.site.name=nimblepetapp-test
chargebee.site.key=test_Hupy8td63cYyP3moTGeYZouL4oNNYZQL

#microservice
microservice_url=http://staging-api8.nimblepetapp.com/wgtxnsvc
microservice_api_call=true

# insert or update Subscription 
insertOrUpdateSubscription = true

# insert or update Credits
insertOrUpdateCredits = true

#disableCancellation
disableCancellation = true

#verizon sim activation
verizon_activation = true

# vpm update
vpm_update = true
#######################################################################
## Iris service connection Details
iris.base.url=https://staging-api8.nimblepetapp.com/irisservices/

#######################################################################

iris.slackurl=*******************************************************************************

#######################################################################
#######################################################################

iris.slackurl=*******************************************************************************

#######################################################################
#if disable schedule Functionality , use ="-"
#scheduler.cron=* 0/1 * ? * *
scheduler.cron=-
scheduler.date=-35

#######################################################################

#######################################################################
Shopify.OrderCreate.Enable=true

#waggle
#Shopify.DNS=waggle-app.myshopify.com
#Shopify.Username=********************************
#Shopify.password=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

#wagglecam
Shopify.DNS=wagvu.myshopify.com
Shopify.Username=********************************
Shopify.password=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

#######################################################################
# Void Invoice While purchase plan after retain valid Card info 
InvoiceVoid.enable=true

#######################################################################
omitplan=stop-subscription,stop-subscription-non,product-accessories,furbit-pre-order,reseller,product-only

#Recharge
Recharge.access-token=***********************************************************************
Recharge.url=https://api.rechargeapps.com

#addons
addonids=setup_charges,upgrade_charges,reactivation_charges,setup-charges-aud,upgrade-charges-aud,upgrade-charges-cad

upgrade.addon=upgrade_charges,upgrade-charges-aud,upgrade-charges-cad

#MoEngage
moengage.app.id=4F46KL5MWXIEMO2350YV47U8_DEBUG
moengage.basic.auth=4F46KL5MWXIEMO2350YV47U8_DEBUG:+wQRCYLY1PhSxv7+efgU@9IP
moengage.event.url=https://api-03.moengage.com/v1/event/app_id_cus?app_id=app_id_cus