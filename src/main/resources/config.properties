#  MySQL Database Configuration
database.driverClassName=com.mysql.cj.jdbc.Driver

#development DB
database.url=***********************************

#DB Password
database.username=nimble
database.password=flamingo2010

#Microservice SQS configurations
send_registeruseremail_simactivation_to_microservice=false
iris.services.amazonSQS.microserviceQueue.url=https://sqs.us-west-2.amazonaws.com/258751312124/dev3-microservice

############################################################
#Asyn Thread Pool Configuration
aysnc.corePoolSize=30
aysnc.maxPoolSize=100
aysnc.queueCapacity=150

#################################################################
#chargebee authorization credentials
chargebee.credentials=test:test

#database.url=***********************************
#database.url=**********************************
#database.username=nimble
#database.password=flamingo2010
#######################################################################
#DB 2
database.niom.url=***********************************************
database.niom.username=nimble
database.niom.password=flamingo2010

#######################################################################
#  Hibernate Configuration
hibernate.dialect=org.hibernate.dialect.MySQLDialect

#######################################################################
# Hibernate General Configuration
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.generate_statistics=false

#chargebee secret key test
chargebee.site.name=nimblepetapp-test
chargebee.site.key=test_Hupy8td63cYyP3moTGeYZouL4oNNYZQL

#microservice
microservice_url=http://dev-api.nimblepetapp.com/wgtxnsvc
microservice_api_call=true

# insert or update Subscription
insertOrUpdateSubscription = true

# insert or update Credits
insertOrUpdateCredits = true

#disableCancellation
disableCancellation = true

#verizon sim activation
verizon_activation = true

# vpm update
vpm_update = true
#######################################################################
## Iris service connection Details
iris.base.url=https://dev-api.nimblepetapp.com/irisservices/

#######################################################################

iris.slackurl=*******************************************************************************

#######################################################################
#if disable schedule Functionality , use ="-"
#scheduler.cron=* 0/1 * ? * *
scheduler.cron=-
scheduler.date=-35

#######################################################################
Shopify.OrderCreate.Enable=true
#waggle
Shopify.DNS=waggle-app.myshopify.com
Shopify.Username=********************************
Shopify.password=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

#wagglecam
#Shopify.DNS=wagvu.myshopify.com
#Shopify.Username=********************************
#Shopify.password=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

#######################################################################
# Void Invoice While purchase plan after retain valid Card info
InvoiceVoid.enable=true

#######################################################################
omitplan=stop-subscription,stop-subscription-non,product-accessories,furbit-pre-order,reseller,product-only

#Recharge
Recharge.access-token=***********************************************************************
Recharge.url=https://api.rechargeapps.com

#addons
addonids=setup_charges,setup_charges_cad,upgrade_charges,reactivation_charges,setup-charges-aud,upgrade-charges-aud,upgrade_charges_cad

upgrade.addon=upgrade_charges,upgrade-charges-aud,upgrade_charges_cad

#MoEngage
moengage.app.id=4F46KL5MWXIEMO2350YV47U8_DEBUG
moengage.basic.auth=4F46KL5MWXIEMO2350YV47U8_DEBUG:+wQRCYLY1PhSxv7+efgU@9IP
moengage.event.url=https://api-03.moengage.com/v1/event/app_id_cus?app_id=app_id_cus

#Email from address
from_email=<EMAIL>

retain_user_add_on=retain-charges-us

#username smart bowl details
kcalUsername=waggle
kcalPassword=Waggle@123

infobip.url=https://vvn3kp.api.infobip.com
infobip.app.key=********************************-f531317b-ec4a-4e66-935c-9bf6574f40c9
#infobip.url=https://2v2enz.api-in.infobip.com
#infobip.app.key=********************************-218600ea-de21-4491-a32d-0148097746dc

#vetchat activation API
vetchat.activation_url=https://dev.itmedicalvetsolutions.com/client/user/waggle/v2/register-user
vetchat.cancellation_url=https://dev.itmedicalvetsolutions.com/client/user/waggle/v2/actions
vetchat.username=waggle_staging_access
vetchat.password=staging_access_password

#Secret Manager Cache Timeout
aws_sm_cache_expire_minutes = 10

#Secret Names Of Secret Manager
aws_sqs_secret_name=prod/svc/sqssvc
aws_ses_secret_name=/prod/svc/awsses