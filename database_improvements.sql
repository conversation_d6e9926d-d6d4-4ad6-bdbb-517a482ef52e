-- Database improvements for webhook idempotency and MRR tracking

-- 1. Add unique constraint on webhook events to prevent duplicates
-- This will cause database-level rejection of duplicate webhook events
ALTER TABLE chargebee_webhooks_status 
ADD CONSTRAINT uk_webhook_event_id_type 
UNIQUE (event_id, event_type);

-- 2. Add index on subscription_id for faster MRR lookups
CREATE INDEX idx_all_chargebee_subscription_id_migrated 
ON all_chargebee_subscription (subscription_id, is_migrated);

CREATE INDEX idx_all_product_subscription_id 
ON all_product_subscription (subscription_id);

-- 3. Add MRR history table for tracking changes (optional)
CREATE TABLE IF NOT EXISTS mrr_update_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    subscription_id VARCHAR(255) NOT NULL,
    old_mrr BIGINT DEFAULT 0,
    new_mrr BIGINT NOT NULL,
    webhook_event_id VARCHAR(255),
    source_table ENUM('all_chargebee_subscription', 'all_product_subscription') NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'webhook_system',
    
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_webhook_event_id (webhook_event_id),
    INDEX idx_updated_at (updated_at)
);

-- 4. Add updated_on column if it doesn't exist (for timestamp tracking)
-- Check if column exists first
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'all_chargebee_subscription' 
         AND COLUMN_NAME = 'updated_on') = 0,
        'ALTER TABLE all_chargebee_subscription ADD COLUMN updated_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'SELECT "Column updated_on already exists in all_chargebee_subscription"'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'all_product_subscription' 
         AND COLUMN_NAME = 'updated_on') = 0,
        'ALTER TABLE all_product_subscription ADD COLUMN updated_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'SELECT "Column updated_on already exists in all_product_subscription"'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Create stored procedure for atomic MRR updates with history tracking
DELIMITER //

CREATE PROCEDURE UpdateMRRWithHistory(
    IN p_subscription_id VARCHAR(255),
    IN p_new_mrr BIGINT,
    IN p_webhook_event_id VARCHAR(255)
)
BEGIN
    DECLARE v_old_mrr BIGINT DEFAULT 0;
    DECLARE v_source_table VARCHAR(50);
    DECLARE v_rows_affected INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Try to update all_chargebee_subscription first
    SELECT mrr INTO v_old_mrr 
    FROM all_chargebee_subscription 
    WHERE subscription_id = p_subscription_id AND is_migrated = 0
    FOR UPDATE;
    
    IF FOUND_ROWS() > 0 THEN
        UPDATE all_chargebee_subscription 
        SET mrr = p_new_mrr, updated_on = NOW() 
        WHERE subscription_id = p_subscription_id AND is_migrated = 0;
        
        SET v_rows_affected = ROW_COUNT();
        SET v_source_table = 'all_chargebee_subscription';
    ELSE
        -- Try all_product_subscription
        SELECT mrr INTO v_old_mrr 
        FROM all_product_subscription 
        WHERE subscription_id = p_subscription_id
        FOR UPDATE;
        
        IF FOUND_ROWS() > 0 THEN
            UPDATE all_product_subscription 
            SET mrr = p_new_mrr, updated_on = NOW() 
            WHERE subscription_id = p_subscription_id;
            
            SET v_rows_affected = ROW_COUNT();
            SET v_source_table = 'all_product_subscription';
        END IF;
    END IF;
    
    -- Insert history record if update was successful
    IF v_rows_affected > 0 THEN
        INSERT INTO mrr_update_history 
        (subscription_id, old_mrr, new_mrr, webhook_event_id, source_table)
        VALUES 
        (p_subscription_id, v_old_mrr, p_new_mrr, p_webhook_event_id, v_source_table);
    END IF;
    
    COMMIT;
    
    SELECT v_rows_affected as rows_affected, v_source_table as source_table;
END //

DELIMITER ;

-- 6. Add webhook processing status enum for better tracking
ALTER TABLE chargebee_webhooks_status 
MODIFY COLUMN event_status ENUM(
    'received', 'processing', 'completed', 'failed', 'duplicate', 'skipped',
    'mrr updated', 'mrr update failed', 'NA'
) DEFAULT 'received';

-- 7. Add webhook retry mechanism table (optional)
CREATE TABLE IF NOT EXISTS webhook_retry_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    webhook_status_id BIGINT NOT NULL,
    event_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    payload JSON,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    next_retry_at TIMESTAMP NULL,
    last_error TEXT,
    status ENUM('pending', 'processing', 'completed', 'failed', 'abandoned') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_event_id (event_id),
    INDEX idx_status_retry_time (status, next_retry_at),
    FOREIGN KEY (webhook_status_id) REFERENCES chargebee_webhooks_status(id)
);
